<!-- 小程序登录页面示例 -->
<view class="login-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在登录...</text>
  </view>

  <!-- 登录内容 -->
  <view wx:else class="login-content">
    <!-- Logo和标题 -->
    <view class="header">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
      <text class="title">欢迎使用小程序</text>
      <text class="subtitle">请授权登录以获得更好的体验</text>
    </view>

    <!-- 登录按钮区域 -->
    <view class="button-area">
      <!-- 快速登录按钮 -->
      <button class="login-btn primary" bindtap="quickLogin">
        <text class="btn-text">快速登录</text>
      </button>

      <!-- 手机号授权登录按钮 -->
      <button 
        class="login-btn secondary" 
        open-type="getPhoneNumber" 
        bindgetphonenumber="onGetPhoneNumber">
        <text class="btn-text">手机号登录</text>
      </button>

      <!-- 说明文字 -->
      <view class="tips">
        <text class="tip-text">点击"快速登录"即可开始使用</text>
        <text class="tip-text">选择"手机号登录"可享受完整功能</text>
      </view>
    </view>

    <!-- 用户协议 -->
    <view class="agreement">
      <text class="agreement-text">
        登录即表示同意
        <text class="link" bindtap="showUserAgreement">《用户协议》</text>
        和
        <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </text>
    </view>
  </view>
</view>

<!-- 样式 -->
<style>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.login-content {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.button-area {
  width: 100%;
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  border: none;
  font-size: 32rpx;
}

.login-btn.primary {
  background: #fff;
  color: #667eea;
}

.login-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.login-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-text {
  font-weight: 500;
}

.tips {
  margin-top: 20rpx;
  text-align: center;
}

.tip-text {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 22rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.agreement {
  text-align: center;
}

.agreement-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 20rpx;
  line-height: 1.5;
}

.link {
  color: #fff;
  text-decoration: underline;
}
</style>
