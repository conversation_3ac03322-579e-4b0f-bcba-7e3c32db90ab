package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniActivity;

/**
 * 精彩活动Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniActivityService 
{
    /**
     * 查询精彩活动
     * 
     * @param activityId 精彩活动主键
     * @return 精彩活动
     */
    public MiniActivity selectMiniActivityByActivityId(Long activityId);

    /**
     * 查询精彩活动列表
     * 
     * @param miniActivity 精彩活动
     * @return 精彩活动集合
     */
    public List<MiniActivity> selectMiniActivityList(MiniActivity miniActivity);

    /**
     * 新增精彩活动
     * 
     * @param miniActivity 精彩活动
     * @return 结果
     */
    public int insertMiniActivity(MiniActivity miniActivity);

    /**
     * 修改精彩活动
     * 
     * @param miniActivity 精彩活动
     * @return 结果
     */
    public int updateMiniActivity(MiniActivity miniActivity);

    /**
     * 批量删除精彩活动
     * 
     * @param activityIds 需要删除的精彩活动主键集合
     * @return 结果
     */
    public int deleteMiniActivityByActivityIds(Long[] activityIds);

    /**
     * 删除精彩活动信息
     * 
     * @param activityId 精彩活动主键
     * @return 结果
     */
    public int deleteMiniActivityByActivityId(Long activityId);

    /**
     * 查询启用的精彩活动列表（小程序端调用）
     * 
     * @return 精彩活动集合
     */
    public List<MiniActivity> selectEnabledMiniActivityList();

    /**
     * 查询推荐的精彩活动列表
     * 
     * @return 精彩活动集合
     */
    public List<MiniActivity> selectRecommendedMiniActivityList();
} 