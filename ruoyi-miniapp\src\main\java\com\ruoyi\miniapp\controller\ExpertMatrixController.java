package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.ExpertMatrix;
import com.ruoyi.miniapp.service.IExpertMatrixService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 专家矩阵Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "创赛路演-专家矩阵管理")
@RestController
@RequestMapping("/miniapp/expert")
public class ExpertMatrixController extends BaseController
{
    @Autowired
    private IExpertMatrixService expertMatrixService;

    /**
     * 查询专家矩阵列表
     */
    @ApiOperation("查询专家矩阵列表")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") ExpertMatrix expertMatrix)
    {
        startPage();
        List<ExpertMatrix> list = expertMatrixService.selectExpertMatrixList(expertMatrix);
        return getDataTable(list);
    }

    /**
     * 导出专家矩阵列表
     */
    @ApiOperation("导出专家矩阵列表")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:export')")
    @Log(title = "专家矩阵", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") ExpertMatrix expertMatrix)
    {
        List<ExpertMatrix> list = expertMatrixService.selectExpertMatrixList(expertMatrix);
        ExcelUtil<ExpertMatrix> util = new ExcelUtil<ExpertMatrix>(ExpertMatrix.class);
        util.exportExcel(response, list, "专家矩阵数据");
    }

    /**
     * 获取专家矩阵详细信息
     */
    @ApiOperation("获取专家矩阵详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("专家ID") @PathVariable("id") Long id)
    {
        return success(expertMatrixService.selectExpertMatrixById(id));
    }

    /**
     * 新增专家矩阵
     */
    @ApiOperation("新增专家矩阵")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:add')")
    @Log(title = "专家矩阵", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("专家矩阵信息") @RequestBody ExpertMatrix expertMatrix)
    {
        return toAjax(expertMatrixService.insertExpertMatrix(expertMatrix));
    }

    /**
     * 修改专家矩阵
     */
    @ApiOperation("修改专家矩阵")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:edit')")
    @Log(title = "专家矩阵", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("专家矩阵信息") @RequestBody ExpertMatrix expertMatrix)
    {
        return toAjax(expertMatrixService.updateExpertMatrix(expertMatrix));
    }

    /**
     * 删除专家矩阵
     */
    @ApiOperation("删除专家矩阵")
    @PreAuthorize("@ss.hasPermi('miniapp:expert:remove')")
    @Log(title = "专家矩阵", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("专家ID数组") @PathVariable Long[] ids)
    {
        return toAjax(expertMatrixService.deleteExpertMatrixByIds(ids));
    }
} 