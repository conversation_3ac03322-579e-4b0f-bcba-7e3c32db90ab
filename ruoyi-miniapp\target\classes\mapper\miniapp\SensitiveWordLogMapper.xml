<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.SensitiveWordLogMapper">
    
    <resultMap type="SensitiveWordLog" id="SensitiveWordLogResult">
        <result property="logId"    column="log_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="moduleName"    column="module_name"    />
        <result property="operationType"    column="operation_type"    />
        <result property="originalContent"    column="original_content"    />
        <result property="filteredContent"    column="filtered_content"    />
        <result property="hitWords"    column="hit_words"    />
        <result property="hitCount"    column="hit_count"    />
        <result property="clientIp"    column="client_ip"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSensitiveWordLogVo">
        select log_id, user_id, user_name, module_name, operation_type, original_content, filtered_content, hit_words, hit_count, client_ip, user_agent, create_time from sensitive_word_log
    </sql>

    <select id="selectSensitiveWordLogList" parameterType="SensitiveWordLog" resultMap="SensitiveWordLogResult">
        <include refid="selectSensitiveWordLogVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="moduleName != null  and moduleName != ''"> and module_name = #{moduleName}</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSensitiveWordLogByLogId" parameterType="Long" resultMap="SensitiveWordLogResult">
        <include refid="selectSensitiveWordLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectSensitiveWordLogByUserId" parameterType="Long" resultMap="SensitiveWordLogResult">
        <include refid="selectSensitiveWordLogVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectSensitiveWordLogByModuleName" parameterType="String" resultMap="SensitiveWordLogResult">
        <include refid="selectSensitiveWordLogVo"/>
        where module_name = #{moduleName}
        order by create_time desc
    </select>
        
    <insert id="insertSensitiveWordLog" parameterType="SensitiveWordLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sensitive_word_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="moduleName != null">module_name,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="originalContent != null">original_content,</if>
            <if test="filteredContent != null">filtered_content,</if>
            <if test="hitWords != null">hit_words,</if>
            <if test="hitCount != null">hit_count,</if>
            <if test="clientIp != null">client_ip,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="moduleName != null">#{moduleName},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="originalContent != null">#{originalContent},</if>
            <if test="filteredContent != null">#{filteredContent},</if>
            <if test="hitWords != null">#{hitWords},</if>
            <if test="hitCount != null">#{hitCount},</if>
            <if test="clientIp != null">#{clientIp},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSensitiveWordLog" parameterType="SensitiveWordLog">
        update sensitive_word_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="moduleName != null">module_name = #{moduleName},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="originalContent != null">original_content = #{originalContent},</if>
            <if test="filteredContent != null">filtered_content = #{filteredContent},</if>
            <if test="hitWords != null">hit_words = #{hitWords},</if>
            <if test="hitCount != null">hit_count = #{hitCount},</if>
            <if test="clientIp != null">client_ip = #{clientIp},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteSensitiveWordLogByLogId" parameterType="Long">
        delete from sensitive_word_log where log_id = #{logId}
    </delete>

    <delete id="deleteSensitiveWordLogByLogIds" parameterType="String">
        delete from sensitive_word_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="cleanSensitiveWordLogByDays" parameterType="int">
        delete from sensitive_word_log 
        where create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
