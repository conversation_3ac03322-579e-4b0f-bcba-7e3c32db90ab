import request from '@/utils/request'

// 查询专家矩阵列表
export function listExpert(query) {
  return request({
    url: '/miniapp/expert/list',
    method: 'get',
    params: query
  })
}

// 查询专家矩阵详细
export function getExpert(id) {
  return request({
    url: '/miniapp/expert/' + id,
    method: 'get'
  })
}

// 新增专家矩阵
export function addExpert(data) {
  return request({
    url: '/miniapp/expert',
    method: 'post',
    data: data
  })
}

// 修改专家矩阵
export function updateExpert(data) {
  return request({
    url: '/miniapp/expert',
    method: 'put',
    data: data
  })
}

// 删除专家矩阵
export function delExpert(id) {
  return request({
    url: '/miniapp/expert/' + id,
    method: 'delete'
  })
} 