package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniJob;
import com.ruoyi.miniapp.service.IMiniJobService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 招聘职位Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "招聘信息管理")
@RestController
@RequestMapping("/miniapp/job")
public class MiniJobController extends BaseController
{
    @Autowired
    private IMiniJobService miniJobService;

    /**
     * 查询招聘职位列表
     */
    @ApiOperation("查询招聘职位列表")
    @PreAuthorize("@ss.hasPermi('miniapp:job:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniJob miniJob)
    {
        startPage();
        List<MiniJob> list = miniJobService.selectMiniJobList(miniJob);
        return getDataTable(list);
    }

    /**
     * 导出招聘职位列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:job:export')")
    @Log(title = "招聘职位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniJob miniJob)
    {
        List<MiniJob> list = miniJobService.selectMiniJobList(miniJob);
        ExcelUtil<MiniJob> util = new ExcelUtil<MiniJob>(MiniJob.class);
        util.exportExcel(response, list, "招聘职位数据");
    }

    /**
     * 获取招聘职位详细信息
     */
    @ApiOperation("获取招聘职位详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:job:query')")
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId)
    {
        return AjaxResult.success(miniJobService.selectMiniJobByJobId(jobId));
    }

    /**
     * 新增招聘职位
     */
    @ApiOperation("新增招聘职位")
    @PreAuthorize("@ss.hasPermi('miniapp:job:add')")
    @Log(title = "招聘职位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniJob miniJob)
    {
        return toAjax(miniJobService.insertMiniJob(miniJob));
    }

    /**
     * 修改招聘职位
     */
    @ApiOperation("修改招聘职位")
    @PreAuthorize("@ss.hasPermi('miniapp:job:edit')")
    @Log(title = "招聘职位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniJob miniJob)
    {
        return toAjax(miniJobService.updateMiniJob(miniJob));
    }

    /**
     * 删除招聘职位
     */
    @ApiOperation("删除招聘职位")
    @PreAuthorize("@ss.hasPermi('miniapp:job:remove')")
    @Log(title = "招聘职位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable Long[] jobIds)
    {
        return toAjax(miniJobService.deleteMiniJobByJobIds(jobIds));
    }

    /**
     * 获取启用的招聘职位列表（小程序用）
     */
    @ApiOperation("获取启用的招聘职位列表（小程序用）")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<MiniJob> list = miniJobService.selectEnabledMiniJobList();
        return AjaxResult.success(list);
    }

} 