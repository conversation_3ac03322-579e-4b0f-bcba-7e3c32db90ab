<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniPageContentMapper">
    
    <resultMap type="MiniPageContent" id="MiniPageContentResult">
        <result property="pageId"    column="page_id"    />
        <result property="pageType"    column="page_type"    />
        <result property="pageTitle"    column="page_title"    />
        <result property="pageContent"    column="page_content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniPageContentVo">
        select page_id, page_type, page_title, page_content, status, create_by, create_time, update_by, update_time, remark from mini_page_content
    </sql>

    <select id="selectMiniPageContentList" parameterType="MiniPageContent" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        <where>  
            <if test="pageType != null  and pageType != ''"> and page_type = #{pageType}</if>
            <if test="pageTitle != null  and pageTitle != ''"> and page_title like concat('%', #{pageTitle}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMiniPageContentByPageId" parameterType="Long" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where page_id = #{pageId}
    </select>

    <select id="selectMiniPageContentByPageType" parameterType="String" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where page_type = #{pageType} and status = '0'
        limit 1
    </select>
        
    <insert id="insertMiniPageContent" parameterType="MiniPageContent" useGeneratedKeys="true" keyProperty="pageId">
        insert into mini_page_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pageType != null and pageType != ''">page_type,</if>
            <if test="pageTitle != null and pageTitle != ''">page_title,</if>
            <if test="pageContent != null and pageContent != ''">page_content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pageType != null and pageType != ''">#{pageType},</if>
            <if test="pageTitle != null and pageTitle != ''">#{pageTitle},</if>
            <if test="pageContent != null and pageContent != ''">#{pageContent},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniPageContent" parameterType="MiniPageContent">
        update mini_page_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="pageType != null and pageType != ''">page_type = #{pageType},</if>
            <if test="pageTitle != null and pageTitle != ''">page_title = #{pageTitle},</if>
            <if test="pageContent != null and pageContent != ''">page_content = #{pageContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where page_id = #{pageId}
    </update>

    <delete id="deleteMiniPageContentByPageId" parameterType="Long">
        delete from mini_page_content where page_id = #{pageId}
    </delete>

    <delete id="deleteMiniPageContentByPageIds" parameterType="String">
        delete from mini_page_content where page_id in 
        <foreach item="pageId" collection="array" open="(" separator="," close=")">
            #{pageId}
        </foreach>
    </delete>

    <select id="selectEnabledMiniPageContentList" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectMiniPageContentByPageKey" parameterType="String" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where page_key = #{pageKey}
    </select>

</mapper> 