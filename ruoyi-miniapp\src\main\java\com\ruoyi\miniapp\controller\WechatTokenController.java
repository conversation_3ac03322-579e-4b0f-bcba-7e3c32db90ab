package com.ruoyi.miniapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.task.WechatNewsSyncTask;
import com.ruoyi.system.service.ISysConfigService;

/**
 * 微信Token管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/miniapp/wechat/token")
public class WechatTokenController extends BaseController {

    @Autowired
    private WechatNewsSyncTask wechatNewsSyncTask;

    /**
     * 获取新闻同步状态
     */
    @PreAuthorize("@ss.hasPermi('miniapp:wechat:sync:view')")
    @GetMapping("/sync/status")
    public AjaxResult getSyncStatus() {
        try {
            String status = wechatNewsSyncTask.getSyncStatus();
            return AjaxResult.success(status);
        } catch (Exception e) {
            return AjaxResult.error("获取同步状态失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发新闻同步
     */
    @PreAuthorize("@ss.hasPermi('miniapp:wechat:sync:manual')")
    @Log(title = "微信新闻同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync/manual")
    public AjaxResult manualSync() {
        try {
            String result = wechatNewsSyncTask.manualSync();
            return AjaxResult.success("手动同步完成").put("result", result);
        } catch (Exception e) {
            return AjaxResult.error("手动同步失败: " + e.getMessage());
        }
    }
}
