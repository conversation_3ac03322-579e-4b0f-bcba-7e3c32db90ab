package com.ruoyi.mq.consumer;

import com.rabbitmq.client.Channel;
import com.ruoyi.mq.config.RabbitMQConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * RabbitMQ消息消费者
 * 
 * <AUTHOR>
 */
@Component
public class RabbitMQConsumer {
    private static final Logger log = LoggerFactory.getLogger(RabbitMQConsumer.class);

    /**
     * 消费默认队列的消息
     * 
     * @param message 消息
     * @param channel 信道
     */
    @RabbitListener(queues = RabbitMQConfig.DEFAULT_QUEUE)
    public void consumeDefaultQueue(Message message, Channel channel) throws IOException {
        try {
            // 获取消息内容
            String messageBody = new String(message.getBody());
            log.info("接收到默认队列消息: {}", messageBody);
            
            // 处理业务逻辑
            // TODO: 根据业务需求处理消息
            
            // 确认消息已被消费，第二个参数表示是否批量确认
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("处理消息失败", e);
            // 拒绝消息，第二个参数表示是否重新入队
            // 如果重新入队，可能会导致消息被重复消费，根据业务情况选择是否重新入队
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 消费示例队列的消息
     * 
     * @param message 消息
     * @param channel 信道
     */
    @RabbitListener(queues = RabbitMQConfig.DEMO_QUEUE)
    public void consumeDemoQueue(Message message, Channel channel) throws IOException {
        try {
            // 获取消息内容
            String messageBody = new String(message.getBody());
            log.info("接收到示例队列消息: {}", messageBody);
            
            // 处理业务逻辑
            // TODO: 根据业务需求处理消息
            
            // 确认消息已被消费
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("处理消息失败", e);
            // 拒绝消息，不重新入队
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
} 