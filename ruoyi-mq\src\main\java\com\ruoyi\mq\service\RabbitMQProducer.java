package com.ruoyi.mq.service;

import com.ruoyi.mq.config.RabbitMQConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * RabbitMQ生产者服务
 * 
 * <AUTHOR>
 */
@Service
public class RabbitMQProducer {
    private static final Logger log = LoggerFactory.getLogger(RabbitMQProducer.class);

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息到指定交换机
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param message    消息内容
     * @return 消息ID
     */
    public String sendMessage(String exchange, String routingKey, Object message) {
        String messageId = UUID.randomUUID().toString();
        CorrelationData correlationData = new CorrelationData(messageId);
        
        rabbitTemplate.convertAndSend(exchange, routingKey, message, correlationData);
        log.info("消息发送成功: exchange={}, routingKey={}, message={}, messageId={}", 
                exchange, routingKey, message, messageId);
                
        return messageId;
    }

    /**
     * 发送延迟消息
     * 使用TTL和死信队列实现延迟消息
     *
     * @param message    消息内容
     * @param delayTime  延迟时间（毫秒）
     * @return 消息ID
     */
    public String sendDelayMessage(Object message, long delayTime) {
        String messageId = UUID.randomUUID().toString();
        CorrelationData correlationData = new CorrelationData(messageId);
        
        // 发送消息到延迟队列，设置消息过期时间
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.DELAYED_EXCHANGE, 
                RabbitMQConfig.DELAYED_ROUTING_KEY, 
                message, 
                msg -> {
                    MessageProperties props = msg.getMessageProperties();
                    // 设置消息过期时间
                    props.setExpiration(String.valueOf(delayTime));
                    // 设置消息持久化
                    props.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    return msg;
                }, 
                correlationData);
        
        log.info("延迟消息发送成功: message={}, delayTime={}, messageId={}", 
                message, delayTime, messageId);
                
        return messageId;
    }
} 