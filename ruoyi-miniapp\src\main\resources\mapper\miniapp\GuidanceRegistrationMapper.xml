<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.GuidanceRegistrationMapper">
    
    <resultMap type="GuidanceRegistration" id="GuidanceRegistrationResult">
        <result property="registrationId"    column="registration_id"    />
        <result property="guidanceId"    column="guidance_id"    />
        <result property="userId"    column="user_id"    />
        <result property="formData"    column="form_data"    />
        <result property="registrationTime"    column="registration_time"    />
        <result property="guidanceTitle"    column="guidance_title"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectGuidanceRegistrationVo">
        select 
            gr.registration_id, 
            gr.guidance_id, 
            gr.user_id, 
            gr.form_data, 
            gr.registration_time, 
            gr.create_by, 
            gr.create_time, 
            gr.update_by, 
            gr.update_time, 
            gr.remark,
            pg.title as guidance_title,
            su.nick_name as user_name,
            su.phonenumber as user_phone
        from mini_guidance_registration gr
        left join mini_project_guidance pg on gr.guidance_id = pg.guidance_id
        left join sys_user su on gr.user_id = su.user_id
    </sql>

    <select id="selectGuidanceRegistrationList" parameterType="GuidanceRegistration" resultMap="GuidanceRegistrationResult">
        <include refid="selectGuidanceRegistrationVo"/>
        <where>  
            <if test="guidanceId != null "> and gr.guidance_id = #{guidanceId}</if>
            <if test="userId != null "> and gr.user_id = #{userId}</if>
            <if test="guidanceTitle != null and guidanceTitle != ''"> and pg.title like concat('%', #{guidanceTitle}, '%')</if>
            <if test="userName != null and userName != ''"> and su.nick_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null and userPhone != ''"> and su.phonenumber like concat('%', #{userPhone}, '%')</if>
            <if test="registrationTime != null "> and date_format(gr.registration_time,'%y%m%d') = date_format(#{registrationTime},'%y%m%d')</if>
        </where>
        order by gr.create_time desc
    </select>
    
    <select id="selectGuidanceRegistrationByRegistrationId" parameterType="Long" resultMap="GuidanceRegistrationResult">
        <include refid="selectGuidanceRegistrationVo"/>
        where gr.registration_id = #{registrationId}
    </select>
        
    <insert id="insertGuidanceRegistration" parameterType="GuidanceRegistration" useGeneratedKeys="true" keyProperty="registrationId">
        insert into mini_guidance_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guidanceId != null">guidance_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="formData != null">form_data,</if>
            <if test="registrationTime != null">registration_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guidanceId != null">#{guidanceId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="formData != null">#{formData},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateGuidanceRegistration" parameterType="GuidanceRegistration">
        update mini_guidance_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="guidanceId != null">guidance_id = #{guidanceId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="formData != null">form_data = #{formData},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where registration_id = #{registrationId}
    </update>

    <delete id="deleteGuidanceRegistrationByRegistrationId" parameterType="Long">
        delete from mini_guidance_registration where registration_id = #{registrationId}
    </delete>

    <delete id="deleteGuidanceRegistrationByRegistrationIds" parameterType="String">
        delete from mini_guidance_registration where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>
</mapper> 