package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniBanner;
import com.ruoyi.miniapp.mapper.MiniBannerMapper;
import com.ruoyi.miniapp.service.IMiniBannerService;

/**
 * 轮播图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniBannerServiceImpl implements IMiniBannerService 
{
    @Autowired
    private MiniBannerMapper miniBannerMapper;

    /**
     * 查询轮播图
     * 
     * @param bannerId 轮播图主键
     * @return 轮播图
     */
    @Override
    public MiniBanner selectMiniBannerByBannerId(Long bannerId)
    {
        return miniBannerMapper.selectMiniBannerByBannerId(bannerId);
    }

    /**
     * 查询轮播图列表
     * 
     * @param miniBanner 轮播图
     * @return 轮播图
     */
    @Override
    public List<MiniBanner> selectMiniBannerList(MiniBanner miniBanner)
    {
        return miniBannerMapper.selectMiniBannerList(miniBanner);
    }

    /**
     * 新增轮播图
     * 
     * @param miniBanner 轮播图
     * @return 结果
     */
    @Override
    public int insertMiniBanner(MiniBanner miniBanner)
    {
        miniBanner.setCreateTime(DateUtils.getNowDate());
        return miniBannerMapper.insertMiniBanner(miniBanner);
    }

    /**
     * 修改轮播图
     * 
     * @param miniBanner 轮播图
     * @return 结果
     */
    @Override
    public int updateMiniBanner(MiniBanner miniBanner)
    {
        miniBanner.setUpdateTime(DateUtils.getNowDate());
        return miniBannerMapper.updateMiniBanner(miniBanner);
    }

    /**
     * 批量删除轮播图
     * 
     * @param bannerIds 需要删除的轮播图主键
     * @return 结果
     */
    @Override
    public int deleteMiniBannerByBannerIds(Long[] bannerIds)
    {
        return miniBannerMapper.deleteMiniBannerByBannerIds(bannerIds);
    }

    /**
     * 删除轮播图信息
     * 
     * @param bannerId 轮播图主键
     * @return 结果
     */
    @Override
    public int deleteMiniBannerByBannerId(Long bannerId)
    {
        return miniBannerMapper.deleteMiniBannerByBannerId(bannerId);
    }

    /**
     * 查询启用的轮播图列表（小程序端调用）
     * 
     * @return 轮播图集合
     */
    @Override
    public List<MiniBanner> selectEnabledMiniBannerList()
    {
        return miniBannerMapper.selectEnabledMiniBannerList();
    }
} 