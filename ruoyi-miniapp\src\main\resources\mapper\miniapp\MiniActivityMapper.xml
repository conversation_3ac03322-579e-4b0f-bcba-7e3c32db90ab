<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniActivityMapper">
    
    <resultMap type="MiniActivity" id="MiniActivityResult">
        <result property="activityId"    column="activity_id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="articleUrl"    column="article_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="wechatArticleId"    column="wechat_article_id"    />
        <result property="wechatSource"    column="wechat_source"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniActivityVo">
        select activity_id, title, description, cover_image, article_url, sort_order, status, wechat_article_id, wechat_source, create_by, create_time, update_by, update_time, remark from mini_activity
    </sql>

    <select id="selectMiniActivityList" parameterType="MiniActivity" resultMap="MiniActivityResult">
        <include refid="selectMiniActivityVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="articleUrl != null  and articleUrl != ''"> and article_url like concat('%', #{articleUrl}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="wechatArticleId != null  and wechatArticleId != ''"> and wechat_article_id = #{wechatArticleId}</if>
            <if test="wechatSource != null  and wechatSource != ''"> and wechat_source like concat('%', #{wechatSource}, '%')</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniActivityByActivityId" parameterType="Long" resultMap="MiniActivityResult">
        <include refid="selectMiniActivityVo"/>
        where activity_id = #{activityId}
    </select>

    <select id="selectEnabledMiniActivityList" resultMap="MiniActivityResult">
        <include refid="selectMiniActivityVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectRecommendedMiniActivityList" resultMap="MiniActivityResult">
        <include refid="selectMiniActivityVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
        limit 10
    </select>
        
    <insert id="insertMiniActivity" parameterType="MiniActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into mini_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="articleUrl != null and articleUrl != ''">article_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">wechat_article_id,</if>
            <if test="wechatSource != null and wechatSource != ''">wechat_source,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="articleUrl != null and articleUrl != ''">#{articleUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">#{wechatArticleId},</if>
            <if test="wechatSource != null and wechatSource != ''">#{wechatSource},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniActivity" parameterType="MiniActivity">
        update mini_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="coverImage != null and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="articleUrl != null and articleUrl != ''">article_url = #{articleUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">wechat_article_id = #{wechatArticleId},</if>
            <if test="wechatSource != null and wechatSource != ''">wechat_source = #{wechatSource},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where activity_id = #{activityId}
    </update>

    <delete id="deleteMiniActivityByActivityId" parameterType="Long">
        delete from mini_activity where activity_id = #{activityId}
    </delete>

    <delete id="deleteMiniActivityByActivityIds" parameterType="String">
        delete from mini_activity where activity_id in 
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>

</mapper> 