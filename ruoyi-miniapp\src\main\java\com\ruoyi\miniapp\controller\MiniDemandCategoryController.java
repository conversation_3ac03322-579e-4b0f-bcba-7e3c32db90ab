package com.ruoyi.miniapp.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniDemandCategory;
import com.ruoyi.miniapp.service.IMiniDemandCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 需求类型管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "需求类型管理")
@RestController
@RequestMapping("/miniapp/demandcategory")
public class MiniDemandCategoryController extends BaseController
{
    @Autowired
    private IMiniDemandCategoryService miniDemandCategoryService;

    /**
     * 查询需求类型列表
     */
    @ApiOperation("查询需求类型列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniDemandCategory miniDemandCategory)
    {
        startPage();
        List<MiniDemandCategory> list = miniDemandCategoryService.selectMiniDemandCategoryList(miniDemandCategory);
        return getDataTable(list);
    }

    /**
     * 导出需求类型列表
     */
    @ApiOperation("导出需求类型列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:export')")
    @Log(title = "需求类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniDemandCategory miniDemandCategory)
    {
        List<MiniDemandCategory> list = miniDemandCategoryService.selectMiniDemandCategoryList(miniDemandCategory);
        ExcelUtil<MiniDemandCategory> util = new ExcelUtil<MiniDemandCategory>(MiniDemandCategory.class);
        util.exportExcel(response, list, "需求类型数据");
    }

    /**
     * 获取需求类型详细信息
     */
    @ApiOperation("获取需求类型详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("需求类型ID") @RequestBody Long categoryId)
    {
        return AjaxResult.success(miniDemandCategoryService.selectMiniDemandCategoryByCategoryId(categoryId));
    }

    /**
     * 新增需求类型
     */
    @ApiOperation("新增需求类型")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:add')")
    @Log(title = "需求类型", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("需求类型信息") @RequestBody MiniDemandCategory miniDemandCategory)
    {
        return toAjax(miniDemandCategoryService.insertMiniDemandCategory(miniDemandCategory));
    }

    /**
     * 修改需求类型（包括排序修改）
     */
    @ApiOperation("修改需求类型")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:edit')")
    @Log(title = "需求类型", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("需求类型信息") @RequestBody MiniDemandCategory miniDemandCategory)
    {
        return toAjax(miniDemandCategoryService.updateMiniDemandCategory(miniDemandCategory));
    }

    /**
     * 删除需求类型
     */
    @ApiOperation("删除需求类型")
    @PreAuthorize("@ss.hasPermi('miniapp:demandcategory:remove')")
    @Log(title = "需求类型", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("需求类型ID数组") @RequestBody Long[] categoryIds)
    {
        return toAjax(miniDemandCategoryService.deleteMiniDemandCategoryByCategoryIds(categoryIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的需求类型列表
     */
    @ApiOperation("获取启用的需求类型列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniDemandCategory> list = miniDemandCategoryService.selectEnabledMiniDemandCategoryList();
        return AjaxResult.success(list);
    }

    /**
     * 获取需求类型的表单配置（小程序端，过滤隐藏字段）
     */
    @ApiOperation("获取需求类型的表单配置")
    @PostMapping("/app/getFormConfig")
    public AjaxResult getFormConfig(@ApiParam("需求类型ID") @RequestBody Long categoryId)
    {
        MiniDemandCategory category = miniDemandCategoryService.selectMiniDemandCategoryByCategoryId(categoryId);
        if (category == null) {
            return AjaxResult.error("需求类型不存在");
        }

        // 过滤隐藏字段
        String filteredFormFields = miniDemandCategoryService.getFilteredFormFields(category.getFormFields());

        Map<String, Object> result = new HashMap<>();
        result.put("categoryId", category.getCategoryId());
        result.put("categoryName", category.getCategoryName());
        result.put("categoryDesc", category.getCategoryDesc());
        result.put("formFields", filteredFormFields);

        return AjaxResult.success(result);
    }
}