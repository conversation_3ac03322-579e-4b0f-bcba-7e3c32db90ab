package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniCollege;

/**
 * 学院信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IMiniCollegeService 
{
    /**
     * 查询学院信息
     * 
     * @param collegeId 学院信息主键
     * @return 学院信息
     */
    public MiniCollege selectMiniCollegeByCollegeId(Long collegeId);

    /**
     * 查询学院信息列表
     * 
     * @param miniCollege 学院信息
     * @return 学院信息集合
     */
    public List<MiniCollege> selectMiniCollegeList(MiniCollege miniCollege);

    /**
     * 新增学院信息
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    public int insertMiniCollege(MiniCollege miniCollege);

    /**
     * 修改学院信息
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    public int updateMiniCollege(MiniCollege miniCollege);

    /**
     * 批量删除学院信息
     * 
     * @param collegeIds 需要删除的学院信息主键集合
     * @return 结果
     */
    public int deleteMiniCollegeByCollegeIds(Long[] collegeIds);

    /**
     * 删除学院信息信息
     * 
     * @param collegeId 学院信息主键
     * @return 结果
     */
    public int deleteMiniCollegeByCollegeId(Long collegeId);

    /**
     * 查询启用的学院信息列表
     * 
     * @return 学院信息集合
     */
    public List<MiniCollege> selectEnabledMiniCollegeList();

    /**
     * 根据学院代码查询学院信息
     * 
     * @param collegeCode 学院代码
     * @return 学院信息
     */
    public MiniCollege selectMiniCollegeByCollegeCode(String collegeCode);

    /**
     * 校验学院代码是否唯一
     * 
     * @param miniCollege 学院信息
     * @return 结果
     */
    public boolean checkCollegeCodeUnique(MiniCollege miniCollege);
}
