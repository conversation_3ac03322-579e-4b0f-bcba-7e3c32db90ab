<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动名称" prop="eventName">
        <el-input
          v-model="queryParams.eventName"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="userPhone">
        <el-input
          v-model="queryParams.userPhone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报名时间">
        <el-date-picker
          v-model="daterangeRegistrationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:registration:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="registrationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报名ID" align="center" prop="registrationId" />
      <el-table-column label="活动名称" align="center" prop="eventName" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="用户手机号" align="center" prop="userPhone" />
      <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名表单数据" align="center" prop="formData" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleViewFormData(scope.row)"
          >查看表单</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:registration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />



    <!-- 查看表单数据对话框 -->
    <el-dialog title="报名表单数据" :visible.sync="formDataVisible" width="800px" append-to-body>
      <div v-if="formDataList && formDataList.length > 0" class="form-data-container">
        <el-descriptions :column="1" border>
          <el-descriptions-item
            v-for="(item, index) in formDataList"
            :key="index"
            :label="item.label"
            :label-style="{ width: '120px', fontWeight: 'bold' }"
          >
            <template v-if="item.type === 'textarea'">
              <div class="textarea-content">{{ item.value || '未填写' }}</div>
            </template>
            <template v-else-if="item.type === 'select' || item.type === 'radio'">
              <el-tag type="primary" size="small">{{ item.value || '未选择' }}</el-tag>
            </template>
            <template v-else-if="item.type === 'tel'">
              <span class="phone-number">{{ item.value || '未填写' }}</span>
            </template>
            <template v-else-if="item.type === 'email'">
              <span class="email-address">{{ item.value || '未填写' }}</span>
            </template>
            <template v-else>
              <span>{{ item.value || '未填写' }}</span>
            </template>
            <el-tag v-if="item.required" type="danger" size="mini" style="margin-left: 8px;">必填</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="no-data">
        <el-empty description="暂无表单数据"></el-empty>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDataVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRegistration, getRegistration, delRegistration, addRegistration, updateRegistration } from "@/api/miniapp/registration";

export default {
  name: "Registration",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户报名记录表格数据
      registrationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示表单数据弹出层
      formDataVisible: false,
      // 表单数据内容
      formDataText: "",
      // 解析后的表单数据列表
      formDataList: [],
      // 报名时间时间范围
      daterangeRegistrationTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventId: null,
        userId: null,
        eventName: null,
        userName: null,
        userPhone: null,
        registrationTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        eventId: [
          { required: true, message: "活动ID不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        registrationTime: [
          { required: true, message: "报名时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户报名记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeRegistrationTime && '' != this.daterangeRegistrationTime) {
        this.queryParams.params["beginRegistrationTime"] = this.daterangeRegistrationTime[0];
        this.queryParams.params["endRegistrationTime"] = this.daterangeRegistrationTime[1];
      }
      listRegistration(this.queryParams).then(response => {
        this.registrationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        registrationId: null,
        eventId: null,
        userId: null,
        formData: null,
        registrationTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeRegistrationTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registrationId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户报名记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const registrationId = row.registrationId || this.ids
      getRegistration(registrationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户报名记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.registrationId != null) {
            updateRegistration(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRegistration(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registrationIds = row.registrationId || this.ids;
      this.$modal.confirm('是否确认删除用户报名记录编号为"' + registrationIds + '"的数据项？').then(function() {
        return delRegistration(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/registration/export', {
        ...this.queryParams
      }, `registration_${new Date().getTime()}.xlsx`)
    },
    /** 查看表单数据 */
    handleViewFormData(row) {
      this.formDataText = row.formData ? JSON.stringify(JSON.parse(row.formData), null, 2) : '无表单数据';

      // 解析表单数据为可读格式
      if (row.formData) {
        try {
          this.formDataList = JSON.parse(row.formData);
        } catch (e) {
          console.error('解析表单数据失败:', e);
          this.formDataList = [];
        }
      } else {
        this.formDataList = [];
      }

      this.formDataVisible = true;
    }
  }
};
</script>

<style scoped>
.form-data-container {
  max-height: 500px;
  overflow-y: auto;
}

.textarea-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.phone-number {
  color: #409eff;
  font-weight: 500;
}

.email-address {
  color: #67c23a;
  font-weight: 500;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.el-descriptions {
  margin-top: 10px;
}

.el-descriptions-item__label {
  background-color: #fafafa !important;
}
</style>