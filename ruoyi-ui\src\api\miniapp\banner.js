import request from '@/utils/request'

// 查询轮播图列表
export function listBanner(query) {
  return request({
    url: '/miniapp/banner/list',
    method: 'post',
    data: query
  })
}

// 查询轮播图详细
export function getBanner(bannerId) {
  return request({
    url: '/miniapp/banner/getInfo',
    method: 'post',
    data: bannerId
  })
}

// 新增轮播图
export function addBanner(data) {
  return request({
    url: '/miniapp/banner/add',
    method: 'post',
    data: data
  })
}

// 修改轮播图
export function updateBanner(data) {
  return request({
    url: '/miniapp/banner/edit',
    method: 'post',
    data: data
  })
}

// 删除轮播图
export function delBanner(bannerId) {
  return request({
    url: '/miniapp/banner/remove',
    method: 'post',
    data: bannerId
  })
}

// 导出轮播图
export function exportBanner(query) {
  return request({
    url: '/miniapp/banner/export',
    method: 'post',
    data: query
  })
}

 