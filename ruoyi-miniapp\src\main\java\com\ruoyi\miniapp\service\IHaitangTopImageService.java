package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.HaitangTopImage;

/**
 * 海棠杯顶图Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IHaitangTopImageService 
{
    /**
     * 查询海棠杯顶图
     * 
     * @param topImageId 海棠杯顶图主键
     * @return 海棠杯顶图
     */
    public HaitangTopImage selectHaitangTopImageByTopImageId(Long topImageId);

    /**
     * 查询海棠杯顶图列表
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 海棠杯顶图集合
     */
    public List<HaitangTopImage> selectHaitangTopImageList(HaitangTopImage haitangTopImage);

    /**
     * 查询启用的海棠杯顶图
     * 
     * @return 海棠杯顶图
     */
    public HaitangTopImage selectEnabledHaitangTopImage();

    /**
     * 新增海棠杯顶图
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 结果
     */
    public int insertHaitangTopImage(HaitangTopImage haitangTopImage);

    /**
     * 修改海棠杯顶图
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 结果
     */
    public int updateHaitangTopImage(HaitangTopImage haitangTopImage);

    /**
     * 批量删除海棠杯顶图
     * 
     * @param topImageIds 需要删除的海棠杯顶图主键
     * @return 结果
     */
    public int deleteHaitangTopImageByTopImageIds(Long[] topImageIds);

    /**
     * 删除海棠杯顶图信息
     * 
     * @param topImageId 海棠杯顶图主键
     * @return 结果
     */
    public int deleteHaitangTopImageByTopImageId(Long topImageId);
}
