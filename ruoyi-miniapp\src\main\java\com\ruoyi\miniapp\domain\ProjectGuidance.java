package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目指导活动对象 mini_project_guidance
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProjectGuidance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指导活动ID */
    private Long guidanceId;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String title;

    /** 封面图片 */
    @Excel(name = "封面图片")
    private String coverImage;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String description;

    /** 活动地点 */
    @Excel(name = "活动地点")
    private String location;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationDeadline;

    /** 表单字段配置 */
    @Excel(name = "表单字段配置")
    private String formFields;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0停用 1正常） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setGuidanceId(Long guidanceId) 
    {
        this.guidanceId = guidanceId;
    }

    public Long getGuidanceId() 
    {
        return guidanceId;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setCoverImage(String coverImage) 
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage() 
    {
        return coverImage;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setRegistrationDeadline(Date registrationDeadline) 
    {
        this.registrationDeadline = registrationDeadline;
    }

    public Date getRegistrationDeadline() 
    {
        return registrationDeadline;
    }
    public void setFormFields(String formFields) 
    {
        this.formFields = formFields;
    }

    public String getFormFields() 
    {
        return formFields;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("guidanceId", getGuidanceId())
            .append("title", getTitle())
            .append("coverImage", getCoverImage())
            .append("description", getDescription())
            .append("location", getLocation())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("registrationDeadline", getRegistrationDeadline())
            .append("formFields", getFormFields())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 