package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniEventRegistrationMapper;
import com.ruoyi.miniapp.domain.MiniEventRegistration;
import com.ruoyi.miniapp.service.IMiniEventRegistrationService;

/**
 * 用户报名记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class MiniEventRegistrationServiceImpl implements IMiniEventRegistrationService 
{
    @Autowired
    private MiniEventRegistrationMapper miniEventRegistrationMapper;

    /**
     * 查询用户报名记录
     * 
     * @param registrationId 用户报名记录主键
     * @return 用户报名记录
     */
    @Override
    public MiniEventRegistration selectMiniEventRegistrationByRegistrationId(Long registrationId)
    {
        return miniEventRegistrationMapper.selectMiniEventRegistrationByRegistrationId(registrationId);
    }

    /**
     * 查询用户报名记录列表
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 用户报名记录
     */
    @Override
    public List<MiniEventRegistration> selectMiniEventRegistrationList(MiniEventRegistration miniEventRegistration)
    {
        return miniEventRegistrationMapper.selectMiniEventRegistrationList(miniEventRegistration);
    }

    /**
     * 新增用户报名记录
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 结果
     */
    @Override
    public int insertMiniEventRegistration(MiniEventRegistration miniEventRegistration)
    {
        miniEventRegistration.setCreateTime(DateUtils.getNowDate());
        miniEventRegistration.setRegistrationTime(DateUtils.getNowDate());
        return miniEventRegistrationMapper.insertMiniEventRegistration(miniEventRegistration);
    }

    /**
     * 修改用户报名记录
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 结果
     */
    @Override
    public int updateMiniEventRegistration(MiniEventRegistration miniEventRegistration)
    {
        miniEventRegistration.setUpdateTime(DateUtils.getNowDate());
        return miniEventRegistrationMapper.updateMiniEventRegistration(miniEventRegistration);
    }

    /**
     * 批量删除用户报名记录
     * 
     * @param registrationIds 需要删除的用户报名记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniEventRegistrationByRegistrationIds(Long[] registrationIds)
    {
        return miniEventRegistrationMapper.deleteMiniEventRegistrationByRegistrationIds(registrationIds);
    }

    /**
     * 删除用户报名记录信息
     * 
     * @param registrationId 用户报名记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniEventRegistrationByRegistrationId(Long registrationId)
    {
        return miniEventRegistrationMapper.deleteMiniEventRegistrationByRegistrationId(registrationId);
    }

    /**
     * 根据活动ID查询报名记录列表
     * 
     * @param eventId 活动ID
     * @return 报名记录集合
     */
    @Override
    public List<MiniEventRegistration> selectMiniEventRegistrationByEventId(Long eventId)
    {
        return miniEventRegistrationMapper.selectMiniEventRegistrationByEventId(eventId);
    }

    /**
     * 根据用户ID查询报名记录列表
     * 
     * @param userId 用户ID
     * @return 报名记录集合
     */
    @Override
    public List<MiniEventRegistration> selectMiniEventRegistrationByUserId(Long userId)
    {
        return miniEventRegistrationMapper.selectMiniEventRegistrationByUserId(userId);
    }

    /**
     * 查询用户是否已报名某活动
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @return 报名记录
     */
    @Override
    public MiniEventRegistration selectRegistrationByEventAndUser(Long eventId, Long userId)
    {
        return miniEventRegistrationMapper.selectRegistrationByEventAndUser(eventId, userId);
    }

    /**
     * 统计活动报名人数
     * 
     * @param eventId 活动ID
     * @return 报名人数
     */
    @Override
    public int countRegistrationByEventId(Long eventId)
    {
        return miniEventRegistrationMapper.countRegistrationByEventId(eventId);
    }
} 