<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniJobMapper">
    
    <resultMap type="MiniJob" id="MiniJobResult">
        <result property="jobId"    column="job_id"    />
        <result property="jobTitle"    column="job_title"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyScale"    column="company_scale"    />
        <result property="salaryRange"    column="salary_range"    />
        <result property="jobTags"    column="job_tags"    />
        <result property="workLocation"    column="work_location"    />
        <result property="jobDescription"    column="job_description"    />
        <result property="requirements"    column="requirements"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniJobVo">
        select job_id, job_title, company_name, company_scale, salary_range, job_tags, work_location, job_description, requirements, contact_info, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_job
    </sql>

    <select id="selectMiniJobList" parameterType="MiniJob" resultMap="MiniJobResult">
        <include refid="selectMiniJobVo"/>
        <where>  
            <if test="jobTitle != null  and jobTitle != ''"> and job_title like concat('%', #{jobTitle}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="companyScale != null  and companyScale != ''"> and company_scale = #{companyScale}</if>
            <if test="workLocation != null  and workLocation != ''"> and work_location like concat('%', #{workLocation}, '%')</if>
            <if test="jobTags != null  and jobTags != ''"> and job_tags like concat('%', #{jobTags}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniJobByJobId" parameterType="Long" resultMap="MiniJobResult">
        <include refid="selectMiniJobVo"/>
        where job_id = #{jobId}
    </select>

    <select id="selectEnabledMiniJobList" resultMap="MiniJobResult">
        <include refid="selectMiniJobVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectRecommendedMiniJobList" resultMap="MiniJobResult">
        <include refid="selectMiniJobVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
        limit 10
    </select>
        
    <insert id="insertMiniJob" parameterType="MiniJob" useGeneratedKeys="true" keyProperty="jobId">
        insert into mini_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">job_title,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="companyScale != null and companyScale != ''">company_scale,</if>
            <if test="salaryRange != null and salaryRange != ''">salary_range,</if>
            <if test="jobTags != null and jobTags != ''">job_tags,</if>
            <if test="workLocation != null and workLocation != ''">work_location,</if>
            <if test="jobDescription != null and jobDescription != ''">job_description,</if>
            <if test="requirements != null and requirements != ''">requirements,</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">#{jobTitle},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="companyScale != null and companyScale != ''">#{companyScale},</if>
            <if test="salaryRange != null and salaryRange != ''">#{salaryRange},</if>
            <if test="jobTags != null and jobTags != ''">#{jobTags},</if>
            <if test="workLocation != null and workLocation != ''">#{workLocation},</if>
            <if test="jobDescription != null and jobDescription != ''">#{jobDescription},</if>
            <if test="requirements != null and requirements != ''">#{requirements},</if>
            <if test="contactInfo != null and contactInfo != ''">#{contactInfo},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniJob" parameterType="MiniJob">
        update mini_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">job_title = #{jobTitle},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="companyScale != null and companyScale != ''">company_scale = #{companyScale},</if>
            <if test="salaryRange != null and salaryRange != ''">salary_range = #{salaryRange},</if>
            <if test="jobTags != null and jobTags != ''">job_tags = #{jobTags},</if>
            <if test="workLocation != null and workLocation != ''">work_location = #{workLocation},</if>
            <if test="jobDescription != null and jobDescription != ''">job_description = #{jobDescription},</if>
            <if test="requirements != null and requirements != ''">requirements = #{requirements},</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info = #{contactInfo},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where job_id = #{jobId}
    </update>



    <delete id="deleteMiniJobByJobId" parameterType="Long">
        delete from mini_job where job_id = #{jobId}
    </delete>

    <delete id="deleteMiniJobByJobIds" parameterType="String">
        delete from mini_job where job_id in 
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>

</mapper> 