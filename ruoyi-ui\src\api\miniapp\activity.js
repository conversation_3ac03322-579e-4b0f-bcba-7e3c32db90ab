import request from '@/utils/request'

// 查询精彩活动列表
export function listActivity(query) {
  return request({
    url: '/miniapp/activity/list',
    method: 'post',
    data: query
  })
}

// 查询精彩活动详细
export function getActivity(activityId) {
  return request({
    url: '/miniapp/activity/getInfo',
    method: 'post',
    data: activityId
  })
}

// 新增精彩活动
export function addActivity(data) {
  return request({
    url: '/miniapp/activity/add',
    method: 'post',
    data: data
  })
}

// 修改精彩活动
export function updateActivity(data) {
  return request({
    url: '/miniapp/activity/edit',
    method: 'post',
    data: data
  })
}

// 删除精彩活动
export function delActivity(activityIds) {
  return request({
    url: '/miniapp/activity/remove',
    method: 'post',
    data: activityIds
  })
}

// 获取启用的精彩活动列表（小程序端）
export function getEnabledActivityList() {
  return request({
    url: '/miniapp/activity/app/getEnabledList',
    method: 'post'
  })
}

// 获取推荐的精彩活动列表（小程序端）
export function getRecommendedActivityList() {
  return request({
    url: '/miniapp/activity/app/getRecommendedList',
    method: 'post'
  })
}

// 获取精彩活动详情（小程序端）
export function getActivityDetail(activityId) {
  return request({
    url: '/miniapp/activity/app/getDetail',
    method: 'post',
    data: activityId
  })
}

// ================================ 微信公众号配置接口 ================================

// 获取微信公众号配置列表
export function getWechatConfigs() {
  return request({
    url: '/miniapp/activity/wechat/getConfigs',
    method: 'post'
  })
}

// 保存微信公众号配置
export function saveWechatConfig(data) {
  return request({
    url: '/miniapp/activity/wechat/saveConfig',
    method: 'post',
    data: data
  })
}

// 删除微信公众号配置
export function deleteWechatConfig(configId) {
  return request({
    url: '/miniapp/activity/wechat/deleteConfig',
    method: 'post',
    params: {
      configId: configId
    }
  })
}

// 测试微信公众号配置
export function testWechatConfig(data) {
  return request({
    url: '/miniapp/activity/wechat/testConfig',
    method: 'post',
    data: data
  })
}

// 从微信公众号同步活动
export function syncActivitiesFromWechat() {
  return request({
    url: '/miniapp/activity/wechat/syncActivities',
    method: 'post'
  })
}

// 从指定微信公众号同步活动
export function syncActivitiesFromSingleWechat(configId) {
  return request({
    url: '/miniapp/activity/wechat/syncFromSingle',
    method: 'post',
    params: {
      configId: configId
    }
  })
}
