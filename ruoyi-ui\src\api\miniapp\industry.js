import request from '@/utils/request'

// 获取完整产业树（仅启用的）
export function getIndustryTree() {
  return request({
    url: '/miniapp/industry/tree',
    method: 'get'
  })
}

// 获取所有产业树（包括禁用的，用于后台管理）
export function getAllIndustryTree() {
  return request({
    url: '/miniapp/industry/tree/all',
    method: 'get'
  })
}

// 新增节点
export function addIndustryNode(data) {
  return request({
    url: '/miniapp/industry/add',
    method: 'post',
    data
  })
}

// 修改节点
export function editIndustryNode(data) {
  return request({
    url: '/miniapp/industry/edit',
    method: 'post',
    data
  })
}

// 删除节点
export function removeIndustryNode(ids) {
  return request({
    url: '/miniapp/industry/remove',
    method: 'post',
    data: ids
  })
}

// 查询节点详情
export function getIndustryNodeInfo(id) {
  return request({
    url: '/miniapp/industry/info',
    method: 'get',
    params: { id }
  })
}

// 查询子节点
export function getChildrenByParentId(parentId) {
  return request({
    url: '/miniapp/industry/children',
    method: 'get',
    params: { parentId }
  })
}

// 按层级查询节点
export function getNodesByLevel(level) {
  return request({
    url: `/miniapp/industry/level/${level}`,
    method: 'get'
  })
}

// 获取三层级结构树
export function getThreeLevelTree() {
  return request({
    url: '/miniapp/industry/threeLevel',
    method: 'get'
  })
}

// 获取上中下游选项
export function getStreamTypes() {
  return request({
    url: '/miniapp/industry/streamTypes',
    method: 'get'
  })
}

// 批量查询行业节点信息
export function getBatchIndustryInfo(industryIds) {
  return request({
    url: '/miniapp/industry/batchInfo',
    method: 'post',
    data: industryIds
  })
}

// 根据上中下游类型查询第二层级节点
export function getNodesByStreamType(streamType) {
  return request({
    url: '/miniapp/industry/streamType',
    method: 'get',
    params: { streamType }
  })
}
