package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniJob;

/**
 * 招聘职位Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniJobMapper 
{
    /**
     * 查询招聘职位
     * 
     * @param jobId 招聘职位主键
     * @return 招聘职位
     */
    public MiniJob selectMiniJobByJobId(Long jobId);

    /**
     * 查询招聘职位列表
     * 
     * @param miniJob 招聘职位
     * @return 招聘职位集合
     */
    public List<MiniJob> selectMiniJobList(MiniJob miniJob);

    /**
     * 新增招聘职位
     * 
     * @param miniJob 招聘职位
     * @return 结果
     */
    public int insertMiniJob(MiniJob miniJob);

    /**
     * 修改招聘职位
     * 
     * @param miniJob 招聘职位
     * @return 结果
     */
    public int updateMiniJob(MiniJob miniJob);

    /**
     * 删除招聘职位
     * 
     * @param jobId 招聘职位主键
     * @return 结果
     */
    public int deleteMiniJobByJobId(Long jobId);

    /**
     * 批量删除招聘职位
     * 
     * @param jobIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniJobByJobIds(Long[] jobIds);

    /**
     * 查询启用的招聘职位列表（小程序端调用）
     * 
     * @return 招聘职位集合
     */
    public List<MiniJob> selectEnabledMiniJobList();

    /**
     * 查询推荐的招聘职位列表
     * 
     * @return 招聘职位集合
     */
    public List<MiniJob> selectRecommendedMiniJobList();
} 