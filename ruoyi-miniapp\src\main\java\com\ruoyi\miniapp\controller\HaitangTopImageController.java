package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.HaitangTopImage;
import com.ruoyi.miniapp.service.IHaitangTopImageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 海棠杯顶图Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "海棠杯顶图管理")
@RestController
@RequestMapping("/miniapp/haitang/topimage")
public class HaitangTopImageController extends BaseController
{
    @Autowired
    private IHaitangTopImageService haitangTopImageService;

    /**
     * 查询海棠杯顶图列表
     */
    @ApiOperation("查询海棠杯顶图列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:list')")
    @GetMapping("/list")
    public TableDataInfo list(HaitangTopImage haitangTopImage)
    {
        startPage();
        List<HaitangTopImage> list = haitangTopImageService.selectHaitangTopImageList(haitangTopImage);
        return getDataTable(list);
    }

    /**
     * 导出海棠杯顶图列表
     */
    @ApiOperation("导出海棠杯顶图列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:export')")
    @Log(title = "海棠杯顶图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HaitangTopImage haitangTopImage)
    {
        List<HaitangTopImage> list = haitangTopImageService.selectHaitangTopImageList(haitangTopImage);
        ExcelUtil<HaitangTopImage> util = new ExcelUtil<HaitangTopImage>(HaitangTopImage.class);
        util.exportExcel(response, list, "海棠杯顶图数据");
    }

    /**
     * 获取海棠杯顶图详细信息
     */
    @ApiOperation("获取海棠杯顶图详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:query')")
    @GetMapping(value = "/{topImageId}")
    public AjaxResult getInfo(@ApiParam("顶图ID") @PathVariable("topImageId") Long topImageId)
    {
        return success(haitangTopImageService.selectHaitangTopImageByTopImageId(topImageId));
    }

    /**
     * 新增海棠杯顶图
     */
    @ApiOperation("新增海棠杯顶图")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:add')")
    @Log(title = "海棠杯顶图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("海棠杯顶图信息") @RequestBody HaitangTopImage haitangTopImage)
    {
        return toAjax(haitangTopImageService.insertHaitangTopImage(haitangTopImage));
    }

    /**
     * 修改海棠杯顶图
     */
    @ApiOperation("修改海棠杯顶图")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:edit')")
    @Log(title = "海棠杯顶图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("海棠杯顶图信息") @RequestBody HaitangTopImage haitangTopImage)
    {
        return toAjax(haitangTopImageService.updateHaitangTopImage(haitangTopImage));
    }

    /**
     * 删除海棠杯顶图
     */
    @ApiOperation("删除海棠杯顶图")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:topimage:remove')")
    @Log(title = "海棠杯顶图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{topImageIds}")
    public AjaxResult remove(@ApiParam("顶图ID数组") @PathVariable Long[] topImageIds)
    {
        return toAjax(haitangTopImageService.deleteHaitangTopImageByTopImageIds(topImageIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的海棠杯顶图
     */
    @ApiOperation("获取启用的海棠杯顶图")
    @PostMapping("/app/getEnabledTopImage")
    public AjaxResult getEnabledTopImage()
    {
        HaitangTopImage topImage = haitangTopImageService.selectEnabledHaitangTopImage();
        return AjaxResult.success(topImage);
    }
}
