# 小程序登录认证流程设计

## 接口设计方案

根据您的需求，设计了以下三个接口来实现完整的登录认证流程：

### 1. 获取当前用户信息接口（需要token验证）

**接口路径**: `GET /miniapp/user/getCurrentUser`
**权限要求**: 需要有效token
**用途**: 前端每次进入小程序时调用此接口

#### 响应情况
- **token有效**: 返回200状态码和用户完整信息，前端直接进入小程序
- **token无效**: 返回401状态码，前端跳转到注册页面

#### 响应示例
```json
{
  "code": 200,
  "msg": "获取用户信息成功",
  "data": {
    "userInfo": {
      "userId": 123,
      "userName": "miniapp_user_123",
      "nickName": "张三",
      "weixinNickname": "微信昵称",
      "avatar": "头像URL",
      "weixinAvatar": "微信头像URL",
      "realName": "张三",
      "phonenumber": "13800138000",
      "sex": "0",
      "totalPoints": 100,
      "email": "<EMAIL>",
      "birthDate": "1990-01-01",
      "region": "北京市",
      "graduateSchool": "清华大学",
      "graduationYear": "2012",
      "major": "计算机科学与技术",
      "college": "计算机学院",
      "currentCompany": "某科技公司",
      "industryField": "1,2,3",
      "positionTitle": "软件工程师",
      "profileCompletionRate": 85
    },
    "isInfoComplete": true,
    "permissions": ["miniapp:user:view", "miniapp:content:view"]
  }
}
```

### 2. 登录注册接口（匿名访问）

**接口路径**: `POST /miniapp/user/weixinLogin`
**权限要求**: 匿名访问（@Anonymous）
**用途**: 用户在注册页面完成登录后调用

#### 请求参数
```json
{
  "code": "微信登录凭证",
  "weixinNickname": "微信昵称",
  "weixinAvatar": "微信头像",
  "nickName": "用户昵称",
  "avatar": "用户头像",
  "phonenumber": "手机号",
  "realName": "真实姓名",
  "sex": "性别",
  "encryptedData": "加密数据（可选）",
  "iv": "初始向量（可选）"
}
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "JWT令牌",
    "userId": 123,
    "userName": "miniapp_user_123",
    "nickName": "张三",
    "weixinNickname": "微信昵称",
    "avatar": "头像URL",
    "weixinAvatar": "微信头像URL",
    "realName": "张三",
    "phonenumber": "13800138000",
    "sex": "0",
    "totalPoints": 100,
    "isNewUser": true
  }
}
```

### 3. 用户注册状态预检查接口（可选，匿名访问）

**接口路径**: `POST /miniapp/user/checkUserStatus`
**权限要求**: 匿名访问（@Anonymous）
**用途**: 注册页面显示不同的UI状态

#### 请求参数
```json
{
  "code": "微信登录凭证"
}
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "检查成功",
  "data": {
    "isRegistered": true,
    "isInfoComplete": false,
    "userInfo": {
      "userId": 123,
      "nickName": "张三",
      "avatar": "头像URL",
      "weixinNickname": "微信昵称",
      "weixinAvatar": "微信头像URL",
      "sex": "0",
      "totalPoints": 100
    }
  }
}
```

## 前端使用流程

### 1. 小程序启动流程

```javascript
// app.js 或页面 onLoad
async function checkAuthStatus() {
  try {
    // 尝试获取当前用户信息
    const res = await wx.request({
      url: `${API_BASE_URL}/miniapp/user/getCurrentUser`,
      method: 'GET',
      header: {
        'Authorization': wx.getStorageSync('token')
      }
    });
    
    if (res.data.code === 200) {
      // token有效，保存用户信息，直接进入小程序
      wx.setStorageSync('userInfo', res.data.data.userInfo);
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  } catch (error) {
    // token无效或网络错误，跳转到登录页
    wx.redirectTo({
      url: '/pages/login/login'
    });
  }
}
```

### 2. 登录页面流程

```javascript
// 登录页面
async function handleLogin() {
  try {
    // 1. 获取微信登录凭证
    const loginRes = await wx.login();
    
    // 2. 可选：预检查用户状态（用于UI展示）
    const statusRes = await wx.request({
      url: `${API_BASE_URL}/miniapp/user/checkUserStatus`,
      method: 'POST',
      data: { code: loginRes.code }
    });
    
    if (statusRes.data.data.isRegistered) {
      // 用户已注册，显示登录UI
      showLoginUI(statusRes.data.data.userInfo);
    } else {
      // 新用户，显示注册UI
      showRegisterUI();
    }
    
    // 3. 执行登录注册
    const authRes = await wx.request({
      url: `${API_BASE_URL}/miniapp/user/weixinLogin`,
      method: 'POST',
      data: {
        code: loginRes.code,
        // 其他用户信息...
      }
    });
    
    if (authRes.data.code === 200) {
      // 保存token和用户信息
      wx.setStorageSync('token', authRes.data.data.token);
      wx.setStorageSync('userInfo', authRes.data.data);
      
      // 跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  } catch (error) {
    wx.showToast({
      title: '登录失败',
      icon: 'error'
    });
  }
}
```

## 设计优势

### 1. 安全性
- ✅ 使用微信官方code换取openid流程
- ✅ 不在前端暴露敏感信息（openid等）
- ✅ JWT token自动过期机制
- ✅ 登录时清除旧token，防止多端登录安全问题

### 2. 用户体验
- ✅ 自动检测登录状态，无需重复登录
- ✅ 401状态码明确指示需要重新登录
- ✅ 支持新用户自动注册
- ✅ 可选的状态预检查优化UI展示

### 3. 开发友好
- ✅ 职责分离清晰
- ✅ 错误处理完善
- ✅ 支持渐进式信息收集
- ✅ 避免微信code重复使用问题

## 注意事项

1. **微信code使用**: 每个code只能使用一次，建议前端在一个流程中只调用一次登录接口
2. **Token管理**: 建议设置合理的token过期时间，平衡安全性和用户体验
3. **错误处理**: 前端需要正确处理401状态码，引导用户重新登录
4. **信息完整性**: 可以根据`isInfoComplete`字段引导用户完善个人信息
