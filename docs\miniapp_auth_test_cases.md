# 小程序登录注册接口测试用例

## 测试环境

- **基础URL**: `http://localhost:8080`
- **数据库**: tjuhaitang_miniapp_db
- **小程序用户角色ID**: 101

## 测试用例

### 1. 新用户注册测试

**测试目标**: 验证新用户通过OpenID注册功能

**请求**:
```bash
curl -X POST http://localhost:8080/miniapp/user/app/weixinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "openid": "test_openid_001",
    "nickName": "测试用户001",
    "avatar": "https://example.com/avatar001.jpg",
    "weixinNickname": "微信测试用户001",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/test001.jpg",
    "phonenumber": "13800138001",
    "realName": "张测试",
    "sex": "0"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "userId": 新生成的用户ID,
    "userName": "mini_test_001_xxxx",
    "nickName": "测试用户001",
    "weixinNickname": "微信测试用户001",
    "avatar": "https://example.com/avatar001.jpg",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/test001.jpg",
    "realName": "张测试",
    "phonenumber": "13800138001",
    "sex": "0",
    "totalPoints": 0,
    "isNewUser": true
  }
}
```

**验证点**:
- [x] 返回状态码200
- [x] isNewUser为true
- [x] 用户名格式正确（mini_开头）
- [x] 昵称使用自定义昵称（优先级高）
- [x] 头像使用自定义头像（优先级高）
- [x] 数据库中创建了新用户记录（user_type='01'）
- [x] 数据库中创建了角色关联记录（role_id=101）

### 2. 已存在用户登录测试

**测试目标**: 验证已存在用户登录并更新信息

**前置条件**: 使用测试用例1中的openid

**请求**:
```bash
curl -X POST http://localhost:8080/miniapp/user/app/weixinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "openid": "test_openid_001",
    "nickName": "更新后的昵称",
    "avatar": "https://example.com/new-avatar.jpg",
    "weixinNickname": "更新的微信昵称",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/new.jpg"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "userId": 与测试用例1相同的用户ID,
    "userName": "mini_test_001_xxxx",
    "nickName": "更新后的昵称",
    "weixinNickname": "更新的微信昵称",
    "avatar": "https://example.com/new-avatar.jpg",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/new.jpg",
    "isNewUser": false
  }
}
```

**验证点**:
- [x] 返回状态码200
- [x] isNewUser为false
- [x] 用户ID与之前相同
- [x] 昵称和头像已更新
- [x] 数据库中用户信息已更新

### 3. 只使用微信信息登录测试

**测试目标**: 验证只提供微信信息时的处理逻辑

**请求**:
```bash
curl -X POST http://localhost:8080/miniapp/user/app/weixinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "openid": "test_openid_002",
    "weixinNickname": "微信用户002",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/wx002.jpg"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "nickName": "微信用户002",
    "avatar": "https://wx.qlogo.cn/mmopen/vi_32/wx002.jpg",
    "weixinNickname": "微信用户002",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/wx002.jpg",
    "isNewUser": true
  }
}
```

**验证点**:
- [x] 昵称使用微信昵称
- [x] 头像使用微信头像

### 4. 获取用户信息测试

**测试目标**: 验证通过token获取用户信息

**前置条件**: 使用测试用例1或2返回的token

**请求**:
```bash
curl -X GET http://localhost:8080/miniapp/user/app/getUserInfo \
  -H "Authorization: Bearer {从登录接口获取的token}"
```

**预期响应**:
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "userId": 用户ID,
    "userName": "mini_test_001_xxxx",
    "nickName": "用户昵称",
    "avatar": "用户头像URL",
    "totalPoints": 0
  }
}
```

**验证点**:
- [x] 返回状态码200
- [x] 返回正确的用户信息
- [x] token验证正常

### 5. 错误情况测试

#### 5.1 缺少OpenID

**请求**:
```bash
curl -X POST http://localhost:8080/miniapp/user/app/weixinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "nickName": "测试用户"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "msg": "OpenID不能为空"
}
```

#### 5.2 无效Token获取用户信息

**请求**:
```bash
curl -X GET http://localhost:8080/miniapp/user/app/getUserInfo \
  -H "Authorization: Bearer invalid_token"
```

**预期响应**:
```json
{
  "code": 401,
  "msg": "用户未登录"
}
```

## 数据库验证

### 验证用户表记录

```sql
-- 查看新创建的小程序用户
SELECT user_id, user_name, nick_name, user_type, openid, weixin_nickname, 
       avatar, weixin_avatar, total_points, create_time
FROM sys_user 
WHERE openid IN ('test_openid_001', 'test_openid_002');
```

### 验证角色关联记录

```sql
-- 查看用户角色关联
SELECT ur.user_id, ur.role_id, u.user_name, r.role_name
FROM sys_user_role ur
JOIN sys_user u ON ur.user_id = u.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.openid IN ('test_openid_001', 'test_openid_002');
```

## 性能测试

### 并发登录测试

使用Apache Bench或类似工具测试并发登录：

```bash
# 创建测试数据文件 test_data.json
echo '{"openid":"test_concurrent_001","nickName":"并发测试用户"}' > test_data.json

# 执行并发测试（10个并发，总共100个请求）
ab -n 100 -c 10 -p test_data.json -T application/json \
   http://localhost:8080/miniapp/user/app/weixinLogin
```

**验证点**:
- [x] 响应时间在可接受范围内
- [x] 没有数据库死锁或重复记录
- [x] 所有请求都能正确处理

## 清理测试数据

测试完成后清理测试数据：

```sql
-- 删除测试用户的角色关联
DELETE FROM sys_user_role 
WHERE user_id IN (
    SELECT user_id FROM sys_user 
    WHERE openid LIKE 'test_%'
);

-- 删除测试用户
DELETE FROM sys_user 
WHERE openid LIKE 'test_%';
```

## 测试检查清单

- [ ] 新用户注册功能正常
- [ ] 已存在用户登录功能正常
- [ ] 头像和昵称优先级处理正确
- [ ] JWT token生成和验证正常
- [ ] 用户信息获取功能正常
- [ ] 错误处理机制正常
- [ ] 数据库记录创建正确
- [ ] 角色关联设置正确
- [ ] 并发处理能力正常
- [ ] 测试数据清理完成
