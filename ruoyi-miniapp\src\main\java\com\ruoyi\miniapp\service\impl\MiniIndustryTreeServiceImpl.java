package com.ruoyi.miniapp.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniIndustryTreeMapper;
import com.ruoyi.miniapp.domain.MiniIndustryTree;
import com.ruoyi.miniapp.service.IMiniIndustryTreeService;
import com.ruoyi.miniapp.mapper.MiniEnterpriseMapper;

/**
 * 产业树状结构Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MiniIndustryTreeServiceImpl implements IMiniIndustryTreeService 
{
    @Autowired
    private MiniIndustryTreeMapper miniIndustryTreeMapper;

    @Autowired
    private MiniEnterpriseMapper miniEnterpriseMapper;

    /**
     * 查询产业树状结构
     * 
     * @param id 产业树状结构主键
     * @return 产业树状结构
     */
    @Override
    public MiniIndustryTree selectMiniIndustryTreeById(Long id)
    {
        return miniIndustryTreeMapper.selectMiniIndustryTreeById(id);
    }

    /**
     * 查询产业树状结构列表
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 产业树状结构
     */
    @Override
    public List<MiniIndustryTree> selectMiniIndustryTreeList(MiniIndustryTree miniIndustryTree)
    {
        return miniIndustryTreeMapper.selectMiniIndustryTreeList(miniIndustryTree);
    }

    /**
     * 查询根节点列表（产业类型）
     * 
     * @return 产业类型列表
     */
    @Override
    public List<MiniIndustryTree> selectIndustryTypeList()
    {
        return miniIndustryTreeMapper.selectIndustryTypeList();
    }

    /**
     * 根据父节点ID查询子节点列表
     * 
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    @Override
    public List<MiniIndustryTree> selectChildrenByParentId(Long parentId)
    {
        return miniIndustryTreeMapper.selectChildrenByParentId(parentId);
    }

    /**
     * 根据节点类型和父节点ID查询节点列表
     * 
     * @param nodeType 节点类型
     * @param parentId 父节点ID
     * @return 节点列表
     */
    @Override
    public List<MiniIndustryTree> selectByNodeTypeAndParentId(String nodeType, Long parentId)
    {
        return miniIndustryTreeMapper.selectByNodeTypeAndParentId(nodeType, parentId);
    }

    /**
     * 新增产业树状结构
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    @Override
    public int insertMiniIndustryTree(MiniIndustryTree miniIndustryTree)
    {
        miniIndustryTree.setCreateTime(DateUtils.getNowDate());
        return miniIndustryTreeMapper.insertMiniIndustryTree(miniIndustryTree);
    }

    /**
     * 修改产业树状结构
     *
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    @Override
    public int updateMiniIndustryTree(MiniIndustryTree miniIndustryTree)
    {
        miniIndustryTree.setUpdateTime(DateUtils.getNowDate());

        // 获取原始节点信息，检查状态是否发生变化
        MiniIndustryTree originalNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(miniIndustryTree.getId());

        int result = miniIndustryTreeMapper.updateMiniIndustryTree(miniIndustryTree);

        // 如果节点状态发生变化，需要级联更新所有子节点状态
        if (result > 0 && originalNode != null &&
            !originalNode.getStatus().equals(miniIndustryTree.getStatus())) {
            // 从启用改为停用，级联停用所有子节点
            if ("0".equals(originalNode.getStatus()) && "1".equals(miniIndustryTree.getStatus())) {
                updateChildrenStatusRecursively(miniIndustryTree.getId(), "1");
            }
            // 从停用改为启用，级联启用所有子节点
            else if ("1".equals(originalNode.getStatus()) && "0".equals(miniIndustryTree.getStatus())) {
                updateChildrenStatusRecursively(miniIndustryTree.getId(), "0");
            }
        }

        return result;
    }

    /**
     * 级联更新子节点状态
     *
     * @param parentId 父节点ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateChildrenStatus(Long parentId, String status)
    {
        return miniIndustryTreeMapper.updateChildrenStatus(parentId, status);
    }

    /**
     * 递归更新所有子节点状态
     *
     * @param parentId 父节点ID
     * @param status 状态
     */
    private void updateChildrenStatusRecursively(Long parentId, String status)
    {
        // 更新直接子节点状态
        miniIndustryTreeMapper.updateChildrenStatus(parentId, status);

        // 获取所有子节点，递归更新其子节点
        List<MiniIndustryTree> children = miniIndustryTreeMapper.selectChildrenByParentId(parentId);
        for (MiniIndustryTree child : children) {
            updateChildrenStatusRecursively(child.getId(), status);
        }
    }

    /**
     * 批量删除产业树状结构（逻辑删除）
     *
     * @param ids 需要删除的产业树状结构主键
     * @return 结果
     */
    @Override
    public int deleteMiniIndustryTreeByIds(Long[] ids)
    {
        return miniIndustryTreeMapper.deleteMiniIndustryTreeByIds(ids);
    }

    /**
     * 级联删除产业树节点及其子节点（逻辑删除）
     *
     * @param id 节点主键
     * @return 结果
     */
    @Override
    public int deleteMiniIndustryTreeCascade(Long id)
    {
        return miniIndustryTreeMapper.deleteMiniIndustryTreeCascade(id);
    }

    /**
     * 检查节点删除前的约束条件
     *
     * @param id 节点主键
     * @return 检查结果消息，null表示可以删除
     */
    @Override
    public String checkDeleteConstraints(Long id)
    {
        // 检查当前节点是否被企业使用
        int currentNodeUsed = miniIndustryTreeMapper.checkNodeUsedByEnterprise(id);
        if (currentNodeUsed > 0) {
            return "该节点已被 " + currentNodeUsed + " 个企业使用，无法删除";
        }

        // 检查子节点是否被企业使用
        int childNodesUsed = miniIndustryTreeMapper.checkChildNodesUsedByEnterprise(id);
        if (childNodesUsed > 0) {
            return "该节点的子节点已被 " + childNodesUsed + " 个企业使用，无法删除";
        }

        return null; // 可以删除
    }

    /**
     * 删除产业树状结构信息
     * 
     * @param id 产业树状结构主键
     * @return 结果
     */
    @Override
    public int deleteMiniIndustryTreeById(Long id)
    {
        return miniIndustryTreeMapper.deleteMiniIndustryTreeById(id);
    }

    /**
     * 构建树状结构
     * 
     * @param nodes 节点列表
     * @return 树状结构
     */
    @Override
    public List<MiniIndustryTree> buildTree(List<MiniIndustryTree> nodes)
    {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 将节点按parentId分组
        Map<Long, List<MiniIndustryTree>> parentNodeMap = nodes.stream()
            .collect(Collectors.groupingBy(MiniIndustryTree::getParentId));

        // 递归构建树
        List<MiniIndustryTree> rootNodes = parentNodeMap.get(0L);
        if (rootNodes != null) {
            for (MiniIndustryTree rootNode : rootNodes) {
                buildChildren(rootNode, parentNodeMap);
            }
        }

        return rootNodes != null ? rootNodes : new ArrayList<>();
    }

    /**
     * 递归构建子节点
     * 
     * @param parent 父节点
     * @param parentNodeMap 节点映射
     */
    private void buildChildren(MiniIndustryTree parent, Map<Long, List<MiniIndustryTree>> parentNodeMap)
    {
        List<MiniIndustryTree> children = parentNodeMap.get(parent.getId());
        if (children != null && !children.isEmpty()) {
            parent.setChildren(children);
            for (MiniIndustryTree child : children) {
                buildChildren(child, parentNodeMap);
            }
        }
    }

    /**
     * 查询完整的产业树（仅启用的）
     *
     * @return 产业树
     */
    @Override
    public List<MiniIndustryTree> selectIndustryTree()
    {
        MiniIndustryTree query = new MiniIndustryTree();
        query.setStatus("0"); // 只查询启用的节点
        List<MiniIndustryTree> allNodes = miniIndustryTreeMapper.selectMiniIndustryTreeList(query);
        return buildTree(allNodes);
    }

    /**
     * 查询所有产业树（包括禁用的，用于后台管理）
     *
     * @return 产业树
     */
    @Override
    public List<MiniIndustryTree> selectAllIndustryTree()
    {
        MiniIndustryTree query = new MiniIndustryTree();
        // 不设置status条件，查询所有状态的节点
        List<MiniIndustryTree> allNodes = miniIndustryTreeMapper.selectMiniIndustryTreeList(query);
        return buildTree(allNodes);
    }

    /**
     * 判断行业类型是否被企业绑定
     * @param industryTypeIds 行业类型ID数组
     * @return 被绑定数量
     */
    @Override
    public int countEnterpriseBindIndustryTypes(Long[] industryTypeIds) {
        return miniEnterpriseMapper.countEnterpriseBindIndustryTypes(industryTypeIds);
    }

    /**
     * 按层级查询节点
     * @param level 层级
     * @return 节点列表
     */
    @Override
    public List<MiniIndustryTree> selectNodesByLevel(Integer level) {
        MiniIndustryTree query = new MiniIndustryTree();
        query.setNodeLevel(level);
        query.setStatus("0"); // 只查询启用的节点
        return miniIndustryTreeMapper.selectMiniIndustryTreeList(query);
    }

    /**
     * 获取三层级结构树
     * @return 三层级树结构
     */
    @Override
    public List<MiniIndustryTree> selectThreeLevelTree() {
        MiniIndustryTree query = new MiniIndustryTree();
        query.setStatus("0"); // 只查询启用的节点
        List<MiniIndustryTree> allNodes = miniIndustryTreeMapper.selectMiniIndustryTreeList(query);

        // 过滤只保留三层级内的节点
        List<MiniIndustryTree> threeLevelNodes = allNodes.stream()
            .filter(node -> node.getNodeLevel() <= 3)
            .collect(Collectors.toList());

        return buildTree(threeLevelNodes);
    }

    /**
     * 验证节点层级规则
     * @param node 节点信息
     * @return 验证结果
     */
    @Override
    public boolean validateNodeLevelRules(MiniIndustryTree node) {
        // 验证层级范围
        if (node.getNodeLevel() == null || node.getNodeLevel() < 1 || node.getNodeLevel() > 3) {
            return false;
        }

        // 验证父节点层级关系
        if (node.getParentId() != null && node.getParentId() > 0) {
            MiniIndustryTree parentNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(node.getParentId());
            if (parentNode == null || parentNode.getNodeLevel() != node.getNodeLevel() - 1) {
                return false;
            }
        }

        // 验证节点类型与层级的对应关系
        String expectedNodeType = getNodeTypeByLevel(node.getNodeLevel());
        if (!expectedNodeType.equals(node.getNodeType())) {
            return false;
        }

        // 验证第二层级的上中下游标识
        if (node.getNodeLevel() == 2) {
            // 获取父节点信息，检查是否需要上中下游
            if (node.getParentId() != null && node.getParentId() > 0) {
                MiniIndustryTree parentNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(node.getParentId());
                if (parentNode != null && "1".equals(parentNode.getHasStreamType())) {
                    // 父节点要求有上中下游，验证streamType
                    if (node.getStreamType() == null || node.getStreamType().trim().isEmpty()) {
                        return false;
                    }
                    if (!"upstream".equals(node.getStreamType()) &&
                        !"midstream".equals(node.getStreamType()) &&
                        !"downstream".equals(node.getStreamType())) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 根据上中下游类型查询第二层级节点
     * @param streamType 上中下游类型
     * @return 节点列表
     */
    @Override
    public List<MiniIndustryTree> selectNodesByStreamType(String streamType) {
        MiniIndustryTree query = new MiniIndustryTree();
        query.setNodeLevel(2);
        query.setStreamType(streamType);
        query.setStatus("0"); // 只查询启用的节点
        return miniIndustryTreeMapper.selectMiniIndustryTreeList(query);
    }

    /**
     * 获取节点的完整路径
     * @param nodeId 节点ID
     * @return 完整路径
     */
    @Override
    public String getNodeFullPath(Long nodeId) {
        if (nodeId == null) {
            return "";
        }

        MiniIndustryTree node = miniIndustryTreeMapper.selectMiniIndustryTreeById(nodeId);
        if (node == null) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        buildNodePath(node, path);
        return path.toString();
    }

    /**
     * 递归构建节点路径
     * @param node 当前节点
     * @param path 路径构建器
     */
    private void buildNodePath(MiniIndustryTree node, StringBuilder path) {
        if (node.getParentId() != null && node.getParentId() > 0) {
            MiniIndustryTree parentNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(node.getParentId());
            if (parentNode != null) {
                buildNodePath(parentNode, path);
                path.append(" > ");
            }
        }
        path.append(node.getNodeName());

        // 如果是第二层级，添加上中下游标识
        if (node.getNodeLevel() == 2 && node.getStreamType() != null) {
            String streamName = getStreamTypeName(node.getStreamType());
            path.append("(").append(streamName).append(")");
        }
    }

    /**
     * 根据层级获取节点类型
     * @param level 层级
     * @return 节点类型
     */
    private String getNodeTypeByLevel(Integer level) {
        switch (level) {
            case 1: return "type";
            case 2: return "position";
            case 3: return "segment";
            default: return "type";
        }
    }

    /**
     * 获取上中下游类型名称
     * @param streamType 上中下游类型
     * @return 类型名称
     */
    private String getStreamTypeName(String streamType) {
        switch (streamType) {
            case "upstream": return "上游";
            case "midstream": return "中游";
            case "downstream": return "下游";
            default: return streamType;
        }
    }

    /**
     * 批量查询行业节点信息（包含一级节点信息）
     */
    @Override
    public List<Map<String, Object>> selectBatchIndustryWithRoot(List<Long> industryIds) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (industryIds == null || industryIds.isEmpty()) {
            return result;
        }

        try {
            // 批量查询所有相关节点
            MiniIndustryTree query = new MiniIndustryTree();
            List<MiniIndustryTree> allNodes = miniIndustryTreeMapper.selectMiniIndustryTreeList(query);

            // 构建ID到节点的映射
            Map<Long, MiniIndustryTree> nodeMap = new HashMap<>();
            for (MiniIndustryTree node : allNodes) {
                nodeMap.put(node.getId(), node);
            }

            // 处理每个请求的行业ID
            for (Long industryId : industryIds) {
                MiniIndustryTree industry = nodeMap.get(industryId);
                if (industry != null) {
                    Map<String, Object> industryInfo = new HashMap<>();
                    industryInfo.put("id", industry.getId());
                    industryInfo.put("nodeName", industry.getNodeName());
                    industryInfo.put("nodeType", industry.getNodeType());
                    industryInfo.put("nodeLevel", industry.getNodeLevel());
                    industryInfo.put("streamType", industry.getStreamType());

                    // 获取一级节点信息
                    MiniIndustryTree rootNode = findRootNodeFromMap(industry, nodeMap);
                    if (rootNode != null) {
                        Map<String, Object> rootInfo = new HashMap<>();
                        rootInfo.put("id", rootNode.getId());
                        rootInfo.put("nodeName", rootNode.getNodeName());
                        rootInfo.put("nodeType", rootNode.getNodeType());
                        industryInfo.put("rootNode", rootInfo);
                    }

                    result.add(industryInfo);
                }
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("批量查询行业信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从节点映射中查找根节点
     */
    private MiniIndustryTree findRootNodeFromMap(MiniIndustryTree node, Map<Long, MiniIndustryTree> nodeMap) {
        if (node == null) {
            return null;
        }

        // 如果已经是一级节点，直接返回
        if (node.getNodeLevel() == 1) {
            return node;
        }

        // 递归向上查找
        if (node.getParentId() != null && node.getParentId() > 0) {
            MiniIndustryTree parentNode = nodeMap.get(node.getParentId());
            return findRootNodeFromMap(parentNode, nodeMap);
        }

        return null;
    }
}