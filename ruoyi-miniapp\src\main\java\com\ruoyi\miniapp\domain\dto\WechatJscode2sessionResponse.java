package com.ruoyi.miniapp.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 微信小程序jscode2session接口响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
public class WechatJscode2sessionResponse
{
    /** 用户唯一标识 */
    private String openid;

    /** 会话密钥 */
    @JsonProperty("session_key")
    private String sessionKey;

    /** 用户在开放平台的唯一标识符 */
    private String unionid;

    /** 错误码 */
    private Integer errcode;

    /** 错误信息 */
    private String errmsg;

    public String getOpenid()
    {
        return openid;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }

    public String getSessionKey()
    {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey)
    {
        this.sessionKey = sessionKey;
    }

    public String getUnionid()
    {
        return unionid;
    }

    public void setUnionid(String unionid)
    {
        this.unionid = unionid;
    }

    public Integer getErrcode()
    {
        return errcode;
    }

    public void setErrcode(Integer errcode)
    {
        this.errcode = errcode;
    }

    public String getErrmsg()
    {
        return errmsg;
    }

    public void setErrmsg(String errmsg)
    {
        this.errmsg = errmsg;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess()
    {
        return errcode == null || errcode == 0;
    }

    @Override
    public String toString()
    {
        return "WechatJscode2sessionResponse{" +
                "openid='" + openid + '\'' +
                ", sessionKey='[HIDDEN]'" +
                ", unionid='" + unionid + '\'' +
                ", errcode=" + errcode +
                ", errmsg='" + errmsg + '\'' +
                '}';
    }
}
