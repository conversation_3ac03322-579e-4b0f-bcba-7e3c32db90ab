# 新闻中心模块使用说明

## 📋 模块概述

新闻中心模块是海棠杯创新创业创赛专区的重要组成部分，主要功能是与微信公众号进行文章同步，实现新闻内容的自动化管理。

## 🔧 功能特性

### 1. 微信公众号同步
- **自动同步**：通过微信公众号API自动获取文章列表
- **去重机制**：基于微信文章ID防止重复数据
- **批量处理**：支持批量插入或更新文章数据

### 2. 文章管理
- **查看功能**：查看文章详情、缩略图、摘要等信息
- **状态管理**：支持文章状态控制（正常/停用）
- **链接跳转**：直接跳转到微信公众号文章
- **封面图显示**：直接使用API获取的图片链接，通过 `referrerpolicy="no-referrer"` 解决跨域问题

### 3. 数据导出
- **Excel导出**：支持将新闻数据导出为Excel文件
- **条件筛选**：支持按标题、作者、状态等条件筛选

## 🗂️ 数据库结构

### mini_news_center表结构
- `id`: 主键ID
- `title`: 文章标题
- `author`: 作者
- `thumb_url`: 缩略图URL
- `digest`: 文章摘要
- `wechat_article_id`: 微信文章ID（唯一标识）
- `wechat_article_url`: 微信文章URL
- `status`: 状态（0正常 1停用）
- `wechat_create_time`: 微信创建时间
- `sync_time`: 同步时间
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 🔌 微信API配置

### API接口
- **接口地址**: `https://api.weixin.qq.com/cgi-bin/freepublish/batchget`
- **请求方式**: POST
- **ACCESS_TOKEN**: 支持动态配置，可在后台系统管理→参数设置中修改

### 请求参数
```json
{
  "offset": 0,        // 偏移量
  "count": 20,        // 获取数量
  "no_content": 1     // 不获取全文
}
```

## 📱 使用方法

### 1. 手动同步
1. 进入新闻中心管理页面
2. 点击"同步微信"按钮
3. 在弹出的对话框中设置同步参数：
   - **同步数量**: 1-50篇文章
   - **偏移量**: 0-1000（用于分页）
4. 点击"确定"开始同步

### 2. 查看文章
- 点击表格中的"查看"按钮查看文章详情
- 点击"打开链接"直接跳转到微信公众号文章

### 3. 数据导出
- 点击"导出"按钮导出Excel文件
- 支持按条件筛选导出

## ⏰ 定时任务配置

### 定时任务类
- **类名**: `com.ruoyi.quartz.task.NewsTask`
- **方法**: `syncWechatNews()` 或 `syncWechatNewsWithParams(String params)`

### 配置示例
在系统管理 → 定时任务中添加：
- **任务名称**: 同步微信公众号文章
- **任务组名**: DEFAULT
- **调用目标字符串**: `newsTask.syncWechatNews()`
- **cron表达式**: `0 0 */2 * * ?` (每2小时执行一次)

### 带参数的定时任务
- **调用目标字符串**: `newsTask.syncWechatNewsWithParams('20,0')`
- **参数格式**: `count,offset`（如：20,0 表示获取20篇文章，偏移量为0）

## 🔑 微信公众号配置

### 配置方式
1. **基础配置**：
   - 进入系统管理 → 参数设置
   - 配置 `wechat.appid` - 微信公众号AppID
   - 配置 `wechat.secret` - 微信公众号Secret
   - 系统会在每次同步前自动获取最新的AccessToken

2. **独立配置**（可选）：
   - 配置 `news.wechat.independent = true` - 启用新闻中心独立配置
   - 配置 `news.wechat.appid` - 新闻中心专用AppID
   - 配置 `news.wechat.secret` - 新闻中心专用Secret
   - 支持为新闻中心使用不同的微信公众号

### 配置详情
- **全局配置**: `wechat.appid` 和 `wechat.secret`
- **独立配置**: `news.wechat.appid` 和 `news.wechat.secret`
- **Token获取**: 每次同步前自动获取最新AccessToken
- **配置优先级**: 独立配置 > 全局配置

## 🔒 权限配置

### 菜单权限
- **新闻中心**: `miniapp:news:list`

### 操作权限
- **查询**: `miniapp:news:query`
- **新增**: `miniapp:news:add`
- **修改**: `miniapp:news:edit`
- **删除**: `miniapp:news:remove`
- **导出**: `miniapp:news:export`
- **同步**: `miniapp:news:sync`

## 🛠️ 技术架构

### 后端架构
- **Controller**: `MiniNewsCenterController`
- **Service**: `IMiniNewsCenterService` / `MiniNewsCenterServiceImpl`
- **Mapper**: `MiniNewsCenterMapper`
- **Entity**: `MiniNewsCenter`

### 前端架构
- **页面组件**: `ruoyi-ui/src/views/miniapp/haitang/news/index.vue`
- **API接口**: `ruoyi-ui/src/api/miniapp/haitang/news.js`

### 图片处理机制
- **存储方式**: 直接存储微信API返回的原始图片URL
- **显示方式**: 前端使用 `<img referrerpolicy="no-referrer" />` 标签直接显示
- **跨域处理**: 通过 `referrerpolicy="no-referrer"` 解决微信图片的跨域访问问题
- **预览功能**: 点击图片可放大预览，支持缩放效果

## 📊 菜单结构

```
创赛路演 (competition)
└── 海棠杯创新创业创赛专区 (haitang)
    ├── 海棠杯轮播图 (carousel)
    ├── 项目报名 (project)
    ├── 项目辅导报名 (guidance)
    ├── 大赛介绍 (competition)
    ├── 视频展播 (video)
    ├── 专家矩阵 (expert)
    └── 新闻中心 (news) ← 新增模块
        ├── 新闻中心查询
        ├── 新闻中心新增
        ├── 新闻中心修改
        ├── 新闻中心删除
        ├── 新闻中心导出
        └── 新闻中心同步
```

## 🔍 注意事项

1. **AccessToken管理**: 系统会在每次同步前自动获取最新的AccessToken，无需手动管理
2. **API调用限制**: 微信API有调用频次限制，建议设置合理的定时任务间隔
3. **数据去重**: 基于`wechat_article_id`字段进行去重，确保不会重复同步同一篇文章
4. **错误处理**: 同步过程中的错误会记录到日志中，建议定期检查日志
5. **权限控制**: 确保只有授权用户才能执行同步操作
6. **字段映射**: 使用`article_id`作为唯一标识，`content.create_time`作为文章创建时间
7. **配置灵活性**: 支持全局配置和独立配置两种模式，可根据需要选择

## 🆘 常见问题

### Q1: 同步失败怎么办？
A1: 检查ACCESS_TOKEN是否有效，网络连接是否正常，查看后台日志获取详细错误信息。

### Q2: 如何配置微信公众号？
A2: 在后台系统管理→参数设置中，配置`wechat.appid`和`wechat.secret`。系统会自动获取最新的AccessToken，无需手动管理。

### Q3: 可以同步多少篇文章？
A3: 每次同步最多支持50篇文章，可以通过调整偏移量实现分页同步。

### Q4: 定时任务不执行怎么办？
A4: 检查定时任务是否启用，cron表达式是否正确，确保`ruoyi-quartz`模块已正确依赖`ruoyi-miniapp`模块。

## 📈 后续优化建议

1. **自动TOKEN刷新**: 实现ACCESS_TOKEN的自动获取和刷新机制
2. **增量同步**: 基于时间戳实现增量同步，提高效率
3. **同步状态监控**: 添加同步状态监控和告警机制
4. **多公众号支持**: 支持多个微信公众号的文章同步
5. **内容过滤**: 添加关键词过滤功能，只同步相关内容
6. **同步日志**: 增加详细的同步日志记录和查询功能 