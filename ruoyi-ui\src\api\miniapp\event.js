import request from '@/utils/request'

// 查询活动报名列表
export function listEvent(query) {
  // 添加活动类型过滤，只查询活动类型的数据
  const params = { ...query, eventType: 'activity' }
  return request({
    url: '/miniapp/event/list',
    method: 'post',
    data: params
  })
}

// 查询活动报名详细
export function getEvent(eventId) {
  return request({
    url: '/miniapp/event/getInfo',
    method: 'post',
    data: eventId
  })
}

// 新增活动报名
export function addEvent(data) {
  // 设置活动类型为活动
  const eventData = { ...data, eventType: 'activity' }
  return request({
    url: '/miniapp/event/add',
    method: 'post',
    data: eventData
  })
}

// 修改活动报名
export function updateEvent(data) {
  // 确保活动类型为活动
  const eventData = { ...data, eventType: 'activity' }
  return request({
    url: '/miniapp/event/edit',
    method: 'post',
    data: eventData
  })
}

// 删除活动报名
export function delEvent(eventIds) {
  return request({
    url: '/miniapp/event/remove',
    method: 'post',
    data: eventIds
  })
}

// 获取正在进行的活动列表（小程序端）
export function getActiveEventList() {
  return request({
    url: '/miniapp/event/app/getActiveList',
    method: 'post'
  })
}

// 获取启用的活动列表（小程序端）
export function getEnabledEventList() {
  return request({
    url: '/miniapp/event/app/getEnabledList',
    method: 'post'
  })
}

// 获取活动详情（小程序端）
export function getEventDetail(eventId) {
  return request({
    url: '/miniapp/event/app/getDetail',
    method: 'post',
    data: eventId
  })
} 