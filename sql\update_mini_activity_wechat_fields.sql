-- 为mini_activity表添加微信公众号相关字段
-- 执行时间：2025-01-20
-- 目的：支持从多个微信公众号同步活动数据

-- 1. 添加微信文章ID字段
ALTER TABLE `mini_activity`
ADD COLUMN `wechat_article_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信文章ID' AFTER `status`;

-- 2. 添加公众号来源字段
ALTER TABLE `mini_activity`
ADD COLUMN `wechat_source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '公众号来源' AFTER `wechat_article_id`;

-- 3. 为微信文章ID添加索引（用于去重查询）
ALTER TABLE `mini_activity` 
ADD INDEX `idx_wechat_article_id`(`wechat_article_id`) USING BTREE;

-- 4. 为公众号来源添加索引（用于筛选查询）
ALTER TABLE `mini_activity` 
ADD INDEX `idx_wechat_source`(`wechat_source`) USING BTREE;

-- 5. 添加微信公众号配置到sys_config表（如果不存在）
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `remark`) 
SELECT '微信公众号活动配置', 'wechat.activity.configs', '[]', 'N', 'admin', NOW(), '微信公众号活动同步配置，JSON格式存储多个公众号配置'
WHERE NOT EXISTS (SELECT 1 FROM `sys_config` WHERE `config_key` = 'wechat.activity.configs');

-- 6. 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'mini_activity' 
    AND COLUMN_NAME IN ('wechat_article_id', 'wechat_source')
ORDER BY 
    ORDINAL_POSITION;
