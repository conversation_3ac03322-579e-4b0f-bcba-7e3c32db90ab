package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 科技之星对象 mini_tech_star
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniTechStar extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 科技之星ID */
    private Long starId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 照片地址 */
    @Excel(name = "照片地址")
    private String photoUrl;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 简介 */
    @Excel(name = "简介")
    private String description;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setStarId(Long starId) 
    {
        this.starId = starId;
    }

    public Long getStarId() 
    {
        return starId;
    }
    
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    
    public void setPhotoUrl(String photoUrl) 
    {
        this.photoUrl = photoUrl;
    }

    public String getPhotoUrl() 
    {
        return photoUrl;
    }
    
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("starId", getStarId())
            .append("name", getName())
            .append("photoUrl", getPhotoUrl())
            .append("companyName", getCompanyName())
            .append("description", getDescription())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 