package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniTopImage;
import com.ruoyi.miniapp.mapper.MiniTopImageMapper;
import com.ruoyi.miniapp.service.IMiniTopImageService;

/**
 * top图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class MiniTopImageServiceImpl implements IMiniTopImageService 
{
    @Autowired
    private MiniTopImageMapper miniTopImageMapper;

    /**
     * 查询top图片
     * 
     * @param topImageId top图片主键
     * @return top图片
     */
    @Override
    public MiniTopImage selectMiniTopImageByTopImageId(Long topImageId)
    {
        return miniTopImageMapper.selectMiniTopImageByTopImageId(topImageId);
    }

    /**
     * 查询top图片列表
     * 
     * @param miniTopImage top图片
     * @return top图片
     */
    @Override
    public List<MiniTopImage> selectMiniTopImageList(MiniTopImage miniTopImage)
    {
        return miniTopImageMapper.selectMiniTopImageList(miniTopImage);
    }

    /**
     * 新增top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    @Override
    public int insertMiniTopImage(MiniTopImage miniTopImage)
    {
        miniTopImage.setCreateTime(DateUtils.getNowDate());
        return miniTopImageMapper.insertMiniTopImage(miniTopImage);
    }

    /**
     * 修改top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    @Override
    public int updateMiniTopImage(MiniTopImage miniTopImage)
    {
        miniTopImage.setUpdateTime(DateUtils.getNowDate());
        return miniTopImageMapper.updateMiniTopImage(miniTopImage);
    }

    /**
     * 批量删除top图片
     * 
     * @param topImageIds 需要删除的top图片主键
     * @return 结果
     */
    @Override
    public int deleteMiniTopImageByTopImageIds(Long[] topImageIds)
    {
        return miniTopImageMapper.deleteMiniTopImageByTopImageIds(topImageIds);
    }

    /**
     * 删除top图片信息
     * 
     * @param topImageId top图片主键
     * @return 结果
     */
    @Override
    public int deleteMiniTopImageByTopImageId(Long topImageId)
    {
        return miniTopImageMapper.deleteMiniTopImageByTopImageId(topImageId);
    }

    /**
     * 查询启用的top图片列表（小程序端调用）
     * 
     * @return top图片集合
     */
    @Override
    public List<MiniTopImage> selectEnabledMiniTopImageList()
    {
        return miniTopImageMapper.selectEnabledMiniTopImageList();
    }

    /**
     * 根据页面标识查询启用的top图片列表（小程序端调用）
     * 
     * @param pageCode 页面标识
     * @return top图片集合
     */
    @Override
    public List<MiniTopImage> selectEnabledMiniTopImageListByPage(String pageCode)
    {
        return miniTopImageMapper.selectEnabledMiniTopImageListByPage(pageCode);
    }
} 