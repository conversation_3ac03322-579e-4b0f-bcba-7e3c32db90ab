package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 页面内容管理对象 mini_page_content
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniPageContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 内容ID */
    private Long contentId;

    /** 页面编码 */
    @Excel(name = "页面编码")
    private String pageCode;

    /** 页面标题 */
    @Excel(name = "页面标题")
    private String pageTitle;

    /** 页面内容（支持富文本） */
    @Excel(name = "页面内容")
    private String pageContent;

    public void setContentId(Long contentId) 
    {
        this.contentId = contentId;
    }

    public Long getContentId() 
    {
        return contentId;
    }
    
    public void setPageCode(String pageCode) 
    {
        this.pageCode = pageCode;
    }

    public String getPageCode() 
    {
        return pageCode;
    }
    
    public void setPageTitle(String pageTitle) 
    {
        this.pageTitle = pageTitle;
    }

    public String getPageTitle() 
    {
        return pageTitle;
    }
    
    public void setPageContent(String pageContent) 
    {
        this.pageContent = pageContent;
    }

    public String getPageContent() 
    {
        return pageContent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("contentId", getContentId())
            .append("pageCode", getPageCode())
            .append("pageTitle", getPageTitle())
            .append("pageContent", getPageContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 