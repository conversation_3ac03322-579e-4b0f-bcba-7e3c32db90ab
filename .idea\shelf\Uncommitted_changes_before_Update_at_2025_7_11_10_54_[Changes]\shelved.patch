Index: .cursor/rules/project-overview.mdc
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>---\r\ndescription: \r\nglobs: \r\nalwaysApply: false\r\n---\r\n# RuoYi-Vue项目概述\r\n\r\nRuoYi-Vue是一个基于SpringBoot+Vue的前后端分离的Java快速开发框架，版本v3.9.0。\r\n\r\n## 技术栈\r\n\r\n### 前端\r\n- Vue\r\n- Element UI\r\n- Vue Router\r\n- Vuex\r\n- Axios\r\n\r\n### 后端\r\n- Spring Boot\r\n- Spring Security\r\n- MyBatis\r\n- Redis\r\n- JWT认证\r\n\r\n## 项目结构\r\n\r\n项目分为前端和后端两部分：\r\n\r\n### 前端部分\r\n前端代码位于[ruoyi-ui](mdc:ruoyi-ui)目录，使用Vue.js框架开发。\r\n\r\n### 后端部分\r\n后端采用多模块结构，主要包括：\r\n- [ruoyi-admin](mdc:ruoyi-admin)：后台管理入口\r\n- [ruoyi-framework](mdc:ruoyi-framework)：框架核心\r\n- [ruoyi-system](mdc:ruoyi-system)：系统管理\r\n- [ruoyi-common](mdc:ruoyi-common)：公共模块\r\n- [ruoyi-generator](mdc:ruoyi-generator)：代码生成\r\n- [ruoyi-quartz](mdc:ruoyi-quartz)：定时任务\r\n\r\n## 主要功能\r\n\r\n该系统包含了完整的后台管理功能，包括：用户管理、部门管理、角色管理、菜单管理、权限控制、数据字典、参数配置、通知公告、日志管理、定时任务、代码生成等功能。\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.cursor/rules/project-overview.mdc b/.cursor/rules/project-overview.mdc
--- a/.cursor/rules/project-overview.mdc	(revision 827fddf704cee4ed1851712dd10e8894e986e9c2)
+++ b/.cursor/rules/project-overview.mdc	(date 1752198590650)
@@ -38,8 +38,16 @@
 - [ruoyi-common](mdc:ruoyi-common)：公共模块
 - [ruoyi-generator](mdc:ruoyi-generator)：代码生成
 - [ruoyi-quartz](mdc:ruoyi-quartz)：定时任务
+- [ruoyi-miniapp](mdc:ruoyi-miniapp)：小程序管理模块
 
 ## 主要功能
 
 该系统包含了完整的后台管理功能，包括：用户管理、部门管理、角色管理、菜单管理、权限控制、数据字典、参数配置、通知公告、日志管理、定时任务、代码生成等功能。
 
+### 小程序管理功能
+- **活动报名系统**: 支持动态表单配置的活动报名功能
+- **用户管理**: 小程序用户信息和行为管理
+- **内容管理**: 轮播图、活动信息、公告管理
+- **统计分析**: 用户活跃度、活动参与度等数据统计
+- **权限集成**: 与若依框架权限系统无缝集成
+
Index: .cursor/rules/project-overview.mdc
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>---\r\ndescription: \r\nglobs: \r\nalwaysApply: false\r\n---\r\n# RuoYi-Vue项目概述\r\n\r\nRuoYi-Vue是一个基于SpringBoot+Vue的前后端分离的Java快速开发框架，版本v3.9.0。\r\n\r\n## 技术栈\r\n\r\n### 前端\r\n- Vue\r\n- Element UI\r\n- Vue Router\r\n- Vuex\r\n- Axios\r\n\r\n### 后端\r\n- Spring Boot\r\n- Spring Security\r\n- MyBatis\r\n- Redis\r\n- JWT认证\r\n\r\n## 项目结构\r\n\r\n项目分为前端和后端两部分：\r\n\r\n### 前端部分\r\n前端代码位于[ruoyi-ui](mdc:ruoyi-ui)目录，使用Vue.js框架开发。\r\n\r\n### 后端部分\r\n后端采用多模块结构，主要包括：\r\n- [ruoyi-admin](mdc:ruoyi-admin)：后台管理入口\r\n- [ruoyi-framework](mdc:ruoyi-framework)：框架核心\r\n- [ruoyi-system](mdc:ruoyi-system)：系统管理\r\n- [ruoyi-common](mdc:ruoyi-common)：公共模块\r\n- [ruoyi-generator](mdc:ruoyi-generator)：代码生成\r\n- [ruoyi-quartz](mdc:ruoyi-quartz)：定时任务\r\n\r\n## 主要功能\r\n\r\n该系统包含了完整的后台管理功能，包括：用户管理、部门管理、角色管理、菜单管理、权限控制、数据字典、参数配置、通知公告、日志管理、定时任务、代码生成等功能。\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.cursor/rules/project-overview.mdc b/.cursor/rules/project-overview.mdc
--- a/.cursor/rules/project-overview.mdc	(revision 827fddf704cee4ed1851712dd10e8894e986e9c2)
+++ b/.cursor/rules/project-overview.mdc	(date 1752198590650)
@@ -38,8 +38,16 @@
 - [ruoyi-common](mdc:ruoyi-common)：公共模块
 - [ruoyi-generator](mdc:ruoyi-generator)：代码生成
 - [ruoyi-quartz](mdc:ruoyi-quartz)：定时任务
+- [ruoyi-miniapp](mdc:ruoyi-miniapp)：小程序管理模块
 
 ## 主要功能
 
 该系统包含了完整的后台管理功能，包括：用户管理、部门管理、角色管理、菜单管理、权限控制、数据字典、参数配置、通知公告、日志管理、定时任务、代码生成等功能。
 
+### 小程序管理功能
+- **活动报名系统**: 支持动态表单配置的活动报名功能
+- **用户管理**: 小程序用户信息和行为管理
+- **内容管理**: 轮播图、活动信息、公告管理
+- **统计分析**: 用户活跃度、活动参与度等数据统计
+- **权限集成**: 与若依框架权限系统无缝集成
+
