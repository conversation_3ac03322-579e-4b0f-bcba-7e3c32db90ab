<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniEventMapper">
    
    <resultMap type="MiniEvent" id="MiniEventResult">
        <result property="eventId"    column="event_id"    />
        <result property="title"    column="title"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="description"    column="description"    />
        <result property="location"    column="location"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="registrationDeadline"    column="registration_deadline"    />
        <result property="formFields"    column="form_fields"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="eventType"    column="event_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniEventVo">
        select event_id, title, cover_image, description, location, start_time, end_time, registration_deadline, form_fields, sort_order, status, event_type, create_by, create_time, update_by, update_time, remark from mini_event
    </sql>

    <select id="selectMiniEventList" parameterType="MiniEvent" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
        </where>
        order by sort_order asc, start_time desc
    </select>
    
    <select id="selectMiniEventByEventId" parameterType="Long" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        where event_id = #{eventId}
    </select>
        
    <insert id="insertMiniEvent" parameterType="MiniEvent" useGeneratedKeys="true" keyProperty="eventId">
        insert into mini_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="description != null">description,</if>
            <if test="location != null">location,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="registrationDeadline != null">registration_deadline,</if>
            <if test="formFields != null">form_fields,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="eventType != null">event_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="description != null">#{description},</if>
            <if test="location != null">#{location},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="registrationDeadline != null">#{registrationDeadline},</if>
            <if test="formFields != null">#{formFields},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniEvent" parameterType="MiniEvent">
        update mini_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="coverImage != null and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="description != null">description = #{description},</if>
            <if test="location != null">location = #{location},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="registrationDeadline != null">registration_deadline = #{registrationDeadline},</if>
            <if test="formFields != null">form_fields = #{formFields},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where event_id = #{eventId}
    </update>

    <delete id="deleteMiniEventByEventId" parameterType="Long">
        delete from mini_event where event_id = #{eventId}
    </delete>

    <delete id="deleteMiniEventByEventIds" parameterType="String">
        delete from mini_event where event_id in 
        <foreach item="eventId" collection="array" open="(" separator="," close=")">
            #{eventId}
        </foreach>
    </delete>

    <select id="selectEnabledMiniEventList" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        where status = '0'
        order by sort_order asc, start_time desc
    </select>

    <select id="selectOngoingMiniEventList" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        where status = '0' and start_time &lt;= NOW() and end_time >= NOW()
        order by sort_order asc, create_time desc
    </select>

    <select id="selectActiveMiniEventList" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <!-- 小程序端搜索活动 -->
    <select id="searchEventsForApp" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        <where>
            and status = '0'
            <if test="keyword != null and keyword != ''">
                and (title like concat('%', #{keyword}, '%')
                or description like concat('%', #{keyword}, '%')
                or location like concat('%', #{keyword}, '%'))
            </if>
            <if test="eventType != null and eventType != ''">
                and event_type = #{eventType}
            </if>
            <if test="status == 'active'">
                and start_time &lt;= NOW() and end_time >= NOW()
            </if>
            <if test="status == 'ended'">
                and end_time &lt; NOW()
            </if>
            <if test="userId != null">
                and event_id in (
                    select event_id from mini_event_registration where user_id = #{userId}
                )
            </if>
        </where>
        order by sort_order asc, start_time desc
    </select>

    <!-- 查询用户参与的活动列表 -->
    <select id="selectEventsByUserId" parameterType="Long" resultMap="MiniEventResult">
        <include refid="selectMiniEventVo"/>
        where status = '0'
        and event_id in (
            select event_id from mini_event_registration where user_id = #{userId}
        )
        order by start_time desc
    </select>
</mapper>