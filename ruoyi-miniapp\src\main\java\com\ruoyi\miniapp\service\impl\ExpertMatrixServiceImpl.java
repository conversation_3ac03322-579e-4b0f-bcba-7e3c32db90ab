package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.ExpertMatrixMapper;
import com.ruoyi.miniapp.domain.ExpertMatrix;
import com.ruoyi.miniapp.service.IExpertMatrixService;

/**
 * 专家矩阵Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ExpertMatrixServiceImpl implements IExpertMatrixService 
{
    @Autowired
    private ExpertMatrixMapper expertMatrixMapper;

    /**
     * 查询专家矩阵
     * 
     * @param id 专家矩阵主键
     * @return 专家矩阵
     */
    @Override
    public ExpertMatrix selectExpertMatrixById(Long id)
    {
        return expertMatrixMapper.selectExpertMatrixById(id);
    }

    /**
     * 查询专家矩阵列表
     * 
     * @param expertMatrix 专家矩阵
     * @return 专家矩阵
     */
    @Override
    public List<ExpertMatrix> selectExpertMatrixList(ExpertMatrix expertMatrix)
    {
        return expertMatrixMapper.selectExpertMatrixList(expertMatrix);
    }

    /**
     * 新增专家矩阵
     * 
     * @param expertMatrix 专家矩阵
     * @return 结果
     */
    @Override
    public int insertExpertMatrix(ExpertMatrix expertMatrix)
    {
        expertMatrix.setCreateTime(DateUtils.getNowDate());
        return expertMatrixMapper.insertExpertMatrix(expertMatrix);
    }

    /**
     * 修改专家矩阵
     * 
     * @param expertMatrix 专家矩阵
     * @return 结果
     */
    @Override
    public int updateExpertMatrix(ExpertMatrix expertMatrix)
    {
        expertMatrix.setUpdateTime(DateUtils.getNowDate());
        return expertMatrixMapper.updateExpertMatrix(expertMatrix);
    }

    /**
     * 批量删除专家矩阵
     * 
     * @param ids 需要删除的专家矩阵主键
     * @return 结果
     */
    @Override
    public int deleteExpertMatrixByIds(Long[] ids)
    {
        return expertMatrixMapper.deleteExpertMatrixByIds(ids);
    }

    /**
     * 删除专家矩阵信息
     * 
     * @param id 专家矩阵主键
     * @return 结果
     */
    @Override
    public int deleteExpertMatrixById(Long id)
    {
        return expertMatrixMapper.deleteExpertMatrixById(id);
    }
} 