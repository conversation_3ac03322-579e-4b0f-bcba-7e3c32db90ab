package com.ruoyi.miniapp.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.StringUtils;

/**
 * 图片下载服务
 * 用于下载微信公众号封面图并保存到本地
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class ImageDownloadService {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageDownloadService.class);
    
    // 固定的图片保存路径
    private static final String UPLOAD_PATH = "D:/develop/images";

    // 图片保存的子目录
    private static final String IMAGE_PATH = "/news/";
    
    /**
     * 下载图片并保存到本地
     * 
     * @param imageUrl 图片URL
     * @param articleId 文章ID（用于生成文件名）
     * @return 本地图片访问路径，失败返回null
     */
    public String downloadAndSaveImage(String imageUrl, String articleId) {
        logger.info("=== ImageDownloadService.downloadAndSaveImage 开始 ===");
        logger.info("imageUrl: {}", imageUrl);
        logger.info("articleId: {}", articleId);
        logger.info("uploadPath: {}", UPLOAD_PATH);

        if (StringUtils.isEmpty(imageUrl) || StringUtils.isEmpty(articleId)) {
            logger.warn("图片URL或文章ID为空，跳过下载");
            return null;
        }

        try {
            // 创建保存目录
            String saveDir = UPLOAD_PATH + IMAGE_PATH;
            File dir = new File(saveDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 获取图片扩展名
            String extension = getImageExtension(imageUrl);
            if (StringUtils.isEmpty(extension)) {
                extension = "jpg"; // 默认扩展名
            }
            
            // 生成文件名：文章ID_随机UUID.扩展名
            String fileName = articleId + "_" + UUID.randomUUID().toString().replace("-", "") + "." + extension;
            String filePath = saveDir + fileName;
            
            // 下载图片
            boolean success = downloadImage(imageUrl, filePath);
            if (success) {
                // 返回访问路径（用于前端访问的路径）
                String accessPath = "/images" + IMAGE_PATH + fileName;
                logger.info("图片下载成功：{} -> {}", imageUrl, accessPath);
                return accessPath;
            } else {
                logger.warn("图片下载失败：{}", imageUrl);
                return null;
            }
            
        } catch (Exception e) {
            logger.error("下载图片异常：{}", imageUrl, e);
            return null;
        }
    }
    
    /**
     * 下载图片到指定路径
     * 
     * @param imageUrl 图片URL
     * @param filePath 保存路径
     * @return 是否成功
     */
    private boolean downloadImage(String imageUrl, String filePath) {
        try {
            URL url = new URL(imageUrl);
            URLConnection connection = url.openConnection();
            
            // 设置请求头，模拟浏览器访问
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            connection.setRequestProperty("Referer", "https://mp.weixin.qq.com/");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            
            // 检查内容类型
            String contentType = connection.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                logger.warn("URL不是图片类型：{}, contentType: {}", imageUrl, contentType);
                return false;
            }
            
            // 下载并保存
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(filePath)) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                    
                    // 限制文件大小（最大5MB）
                    if (totalBytes > 5 * 1024 * 1024) {
                        logger.warn("图片文件过大，停止下载：{}", imageUrl);
                        return false;
                    }
                }
                
                logger.debug("图片下载完成，大小：{} bytes", totalBytes);
                return true;
            }
            
        } catch (IOException e) {
            logger.error("下载图片IO异常：{}", imageUrl, e);
            return false;
        } catch (Exception e) {
            logger.error("下载图片异常：{}", imageUrl, e);
            return false;
        }
    }
    
    /**
     * 从URL中提取图片扩展名
     * 
     * @param imageUrl 图片URL
     * @return 扩展名（不含点号）
     */
    private String getImageExtension(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl)) {
            return null;
        }
        
        // 移除查询参数
        String url = imageUrl.split("\\?")[0];
        
        // 提取扩展名
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            String extension = url.substring(lastDotIndex + 1).toLowerCase();
            
            // 验证是否为有效的图片扩展名
            if (extension.matches("^(jpg|jpeg|png|gif|bmp|webp)$")) {
                return extension;
            }
        }
        
        return null;
    }
    
    /**
     * 检查是否为微信图片URL
     * 
     * @param url 图片URL
     * @return 是否为微信图片URL
     */
    public boolean isWechatImageUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }
        
        String[] wechatDomains = {
            "mmbiz.qpic.cn",
            "mmbiz.qlogo.cn", 
            "wx.qlogo.cn",
            "thirdwx.qlogo.cn",
            "mp.weixin.qq.com"
        };
        
        for (String domain : wechatDomains) {
            if (url.contains(domain)) {
                return true;
            }
        }
        
        return false;
    }
}
