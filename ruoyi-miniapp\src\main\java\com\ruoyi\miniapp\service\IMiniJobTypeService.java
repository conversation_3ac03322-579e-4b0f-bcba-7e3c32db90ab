package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniJobType;

/**
 * 职位类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IMiniJobTypeService 
{
    /**
     * 查询职位类型
     * 
     * @param jobTypeId 职位类型主键
     * @return 职位类型
     */
    public MiniJobType selectMiniJobTypeByJobTypeId(Long jobTypeId);

    /**
     * 查询职位类型列表
     * 
     * @param miniJobType 职位类型
     * @return 职位类型集合
     */
    public List<MiniJobType> selectMiniJobTypeList(MiniJobType miniJobType);

    /**
     * 新增职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    public int insertMiniJobType(MiniJobType miniJobType);

    /**
     * 修改职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    public int updateMiniJobType(MiniJobType miniJobType);

    /**
     * 批量删除职位类型
     * 
     * @param jobTypeIds 需要删除的职位类型主键集合
     * @return 结果
     */
    public int deleteMiniJobTypeByJobTypeIds(Long[] jobTypeIds);

    /**
     * 删除职位类型信息
     * 
     * @param jobTypeId 职位类型主键
     * @return 结果
     */
    public int deleteMiniJobTypeByJobTypeId(Long jobTypeId);

    /**
     * 查询启用的职位类型列表（小程序端调用）
     * 
     * @return 职位类型集合
     */
    public List<MiniJobType> selectEnabledMiniJobTypeList();
} 