# 需求类型表字段扩展说明

## 概述
为需求类型表 `mini_demand_category` 添加了两个新字段，并完成了相应的前后端代码修改。

## 数据库修改

### 新增字段
1. **category_code** (VARCHAR(50)) - 类型标识代码
   - 用于程序中标识不同的需求类型
   - 例如：financing、technology、scenario等

2. **category_short_name** (VARCHAR(100)) - 类型名称简称
   - 用于在界面上显示简短的类型名称
   - 例如：融资、技术、场景等

### SQL语句
```sql
ALTER TABLE mini_demand_category 
ADD COLUMN category_code VARCHAR(50) COMMENT '类型标识代码' AFTER category_name,
ADD COLUMN category_short_name VARCHAR(100) COMMENT '类型名称简称' AFTER category_code;
```

## 后端代码修改

### 1. 实体类修改 (MiniDemandCategory.java)
- 添加了 `categoryCode` 和 `categoryShortName` 属性
- 添加了对应的 getter 和 setter 方法
- 添加了 @Excel 注解用于导出功能

### 2. Mapper XML修改 (MiniDemandCategoryMapper.xml)
- 更新了 resultMap 映射
- 更新了查询语句 selectMiniDemandCategoryVo
- 更新了插入语句 insertMiniDemandCategory
- 更新了更新语句 updateMiniDemandCategory

## 前端代码修改

### 1. 表格显示修改 (index.vue)
- 在表格中添加了"类型标识"和"简称"两列
- 调整了列宽以适应新字段

### 2. 表单修改
- 在新增/编辑对话框中添加了两个新字段的输入框
- 添加了表单验证规则，特别是对类型标识的格式验证

### 3. 数据初始化修改
- 更新了表单重置方法，包含新字段的初始化

## 表单字段隐藏功能

同时还实现了表单字段的隐藏功能：

### 1. 前端功能
- 在表单配置中为每个字段添加了"是否隐藏"开关
- 隐藏的字段在预览时会显示特殊标识
- 使用Vue的响应式数据绑定确保开关正常工作

### 2. 后端API
- 添加了 `/app/getFormConfig` 接口用于小程序端获取过滤后的表单配置
- 实现了 `getFilteredFormFields` 方法过滤隐藏字段
- 隐藏字段不会返回给小程序端

## 示例数据
已为现有数据添加了示例值：
- 融资: financing
- 技术: technology  
- 场景: scenario
- 资质: qualification
- 办公: office
- 厂房: factory
- 曝光: exposure
- 其他: other

## 使用说明

### 管理端
1. 在需求类型管理页面可以看到新增的"类型标识"和"简称"列
2. 新增/编辑需求类型时需要填写类型标识（必填）和简称（可选）
3. 类型标识必须以字母开头，只能包含字母、数字和下划线
4. 在表单配置中可以设置字段是否隐藏

### 小程序端
1. 通过 `/miniapp/demandcategory/app/getFormConfig` 接口获取表单配置
2. 隐藏的字段不会在返回的配置中出现
3. 可以通过 `categoryCode` 字段在程序中识别不同的需求类型

## UI界面优化

### 对话框宽度调整
- 将新增/编辑对话框宽度从 600px 调整为 900px
- 更好地适应富文本编辑器和多字段布局

### 表单布局优化
- 采用两列布局，提高空间利用率
- 基础信息字段（类型名称、类型标识）并排显示
- 次要信息字段（类型简称、排序）并排显示
- 类型描述独占一行，给富文本编辑器充足空间
- 状态和备注字段并排显示

### 样式细节优化
- 调整表单项间距为18px，提高视觉舒适度
- 统一输入框宽度为100%
- 增加对话框内边距，提升整体美观度

## 注意事项
1. 类型标识字段建议使用英文，便于程序处理
2. 类型简称可以使用中文，用于界面显示
3. 修改现有数据的类型标识时要谨慎，可能影响小程序端的逻辑
4. 隐藏字段功能主要用于灵活控制小程序端显示的表单项
5. 新的UI布局在宽屏显示器上效果更佳，提升了用户体验
