# 微信小程序登录功能完整实现

## 🎯 实现概述

本文档详细说明了微信小程序登录功能的完整实现，包括通过code换取openid的标准流程。

## 🔄 登录流程

### 标准微信小程序登录流程

1. **小程序端**: 调用`wx.login()`获取临时登录凭证`code`
2. **发送请求**: 将`code`和用户信息发送到后端
3. **后端处理**: 
   - 调用微信接口`jscode2session`，使用`code + appid + secret`换取`openid`和`session_key`
   - 根据`openid`查询或创建用户
   - 生成JWT token
4. **返回结果**: 返回用户信息和token给前端

## 📋 核心实现

### 1. 微信配置

#### application.yml
```yaml
# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID
    appid: your_miniapp_appid
    # 小程序AppSecret
    secret: your_miniapp_secret
    # 微信API基础URL
    api-base-url: https://api.weixin.qq.com
    # 获取access_token的URL
    token-url: /cgi-bin/token
    # code换取openid的URL
    jscode2session-url: /sns/jscode2session
```

#### WechatMiniappConfig.java
配置类用于读取微信小程序相关配置信息。

### 2. 微信API服务

#### WechatMiniappService.java
核心服务类，负责：
- 调用微信`jscode2session`接口
- 通过code换取openid和session_key
- 处理微信API响应和异常

### 3. 登录请求DTO

#### MiniappLoginRequest.java
更新后的登录请求参数：
```java
public class MiniappLoginRequest {
    @ApiModelProperty("微信登录凭证code")
    private String code;  // 新增：必填字段
    
    @ApiModelProperty("微信OpenID（可选，通过code获取）")
    private String openid;  // 修改：变为可选字段
    
    // 其他用户信息字段...
}
```

### 4. 登录接口

#### MiniUserController.java
更新后的登录逻辑：
```java
@PostMapping("/app/weixinLogin")
public AjaxResult weixinLogin(@RequestBody MiniappLoginRequest loginRequest) {
    // 1. 验证code参数
    if (StringUtils.isEmpty(loginRequest.getCode())) {
        return error("微信登录凭证code不能为空");
    }
    
    // 2. 验证微信配置
    if (!wechatMiniappService.isConfigValid()) {
        return error("微信小程序配置不完整，请联系管理员");
    }
    
    // 3. 通过code换取openid
    WechatJscode2sessionResponse wechatResponse = 
        wechatMiniappService.jscode2session(loginRequest.getCode());
    
    // 4. 设置微信信息到登录请求
    loginRequest.setOpenid(wechatResponse.getOpenid());
    loginRequest.setSessionKey(wechatResponse.getSessionKey());
    
    // 5. 后续用户处理逻辑...
}
```

## 🔧 配置说明

### 获取微信小程序配置

1. **登录微信公众平台**: https://mp.weixin.qq.com/
2. **进入小程序管理后台**
3. **获取AppID和AppSecret**:
   - 开发 -> 开发管理 -> 开发设置
   - AppID: 小程序的唯一标识
   - AppSecret: 小程序的密钥（需要重置获取）

### 配置步骤

1. **修改application.yml**:
   ```yaml
   wechat:
     miniapp:
       appid: wx1234567890abcdef  # 替换为实际的AppID
       secret: abcdef1234567890abcdef1234567890  # 替换为实际的AppSecret
   ```

2. **服务器域名配置**:
   - 在微信公众平台配置服务器域名
   - 开发 -> 开发管理 -> 开发设置 -> 服务器域名
   - 添加你的后端API域名到request合法域名

## 🚀 前端调用示例

### 完整登录流程
```javascript
// 1. 获取微信登录凭证
wx.login({
  success: function(loginRes) {
    if (loginRes.code) {
      // 2. 可选：获取用户信息
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function(userRes) {
          // 3. 发送登录请求
          wx.request({
            url: 'https://your-api-domain.com/miniapp/user/app/weixinLogin',
            method: 'POST',
            data: {
              code: loginRes.code,
              weixinNickname: userRes.userInfo.nickName,
              weixinAvatar: userRes.userInfo.avatarUrl
            },
            success: function(res) {
              if (res.data.code === 200) {
                // 4. 保存token
                wx.setStorageSync('token', res.data.data.token);
                console.log('登录成功', res.data.data);
              }
            }
          });
        }
      });
    }
  }
});
```

### 简化登录（仅code）
```javascript
wx.login({
  success: function(res) {
    if (res.code) {
      wx.request({
        url: 'https://your-api-domain.com/miniapp/user/app/weixinLogin',
        method: 'POST',
        data: {
          code: res.code
        },
        success: function(res) {
          if (res.data.code === 200) {
            wx.setStorageSync('token', res.data.data.token);
          }
        }
      });
    }
  }
});
```

## ⚠️ 注意事项

1. **AppSecret安全**: 
   - AppSecret必须保存在服务器端，不能暴露给前端
   - 定期更换AppSecret以提高安全性

2. **code有效期**: 
   - 微信登录凭证code有效期为5分钟
   - 每个code只能使用一次

3. **网络请求**: 
   - 确保服务器能够访问微信API（api.weixin.qq.com）
   - 配置合适的超时时间和重试机制

4. **错误处理**: 
   - 妥善处理微信API返回的错误码
   - 提供友好的错误提示给用户

## 🔍 常见问题

### Q: 提示"微信小程序配置不完整"
A: 检查application.yml中的appid和secret是否正确配置，不能使用默认的占位符值。

### Q: 调用微信接口失败
A: 
1. 检查网络连接是否正常
2. 验证AppID和AppSecret是否正确
3. 确认code是否已过期或被使用过

### Q: 用户信息获取失败
A: 
1. 检查是否正确调用了wx.getUserProfile()
2. 确认用户是否授权了用户信息
3. 可以实现降级方案，仅使用code登录

## 📊 API响应示例

### 成功响应
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "userId": 123,
    "userName": "mini_12345678_9012",
    "nickName": "微信用户",
    "avatar": "https://wx.qlogo.cn/mmopen/vi_32/...",
    "isNewUser": true
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "登录失败：微信接口调用失败: invalid code"
}
```
