<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="活动名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公众号来源" prop="wechatSource">
        <el-input
          v-model="queryParams.wechatSource"
          placeholder="请输入公众号来源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleWechatConfig"
        >公众号配置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleSyncFromWechat"
        >同步活动</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="activityList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="activityId" width="80" />
      <el-table-column label="活动标题" align="center" prop="title" />
      <el-table-column label="活动描述" align="center" prop="description" show-overflow-tooltip />
      <el-table-column label="封面图片" align="center" prop="coverImage" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.coverImage" style="display: flex; justify-content: center;">
            <img
              :src="getImageUrl(scope.row.coverImage)"
              alt="封面图片"
              referrerpolicy="no-referrer"
              style="width: 100px; height: 60px; object-fit: cover; border-radius: 4px; cursor: pointer; transition: all 0.3s;"
              @click="previewImage(scope.row.coverImage)"
              @error="handleImageError($event)"
              @load="handleImageLoad($event)"
            />
          </div>
          <span v-else style="color: #999;">无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="公众号来源" align="center" prop="wechatSource" width="120" />
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="活动标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入活动标题" />
        </el-form-item>
        <el-form-item label="活动描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入活动描述" :rows="4" />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImage">
          <image-upload v-model="form.coverImage"/>
        </el-form-item>
        <el-form-item label="文章链接" prop="articleUrl">
          <el-input v-model="form.articleUrl" placeholder="请输入公众号文章链接" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 微信公众号配置对话框 -->
    <el-dialog title="微信公众号配置" :visible.sync="wechatConfigOpen" width="1000px" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddWechatConfig"
          >新增配置</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="wechatConfigLoading" :data="wechatConfigList">
        <el-table-column label="配置名称" align="center" prop="name" />
        <el-table-column label="AppID" align="center" prop="appId" />
        <el-table-column label="AppSecret" align="center" prop="appSecret" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.appSecret ? scope.row.appSecret.substring(0, 10) + '...' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center" prop="enabled" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最后同步时间" align="center" prop="lastSyncTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.lastSyncTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEditWechatConfig(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-connection"
              @click="handleTestWechatConfig(scope.row)"
            >测试</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleSyncFromSingleWechat(scope.row.configId)"
            >同步</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteWechatConfig(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="wechatConfigOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 微信公众号配置编辑对话框 -->
    <el-dialog :title="wechatConfigTitle" :visible.sync="wechatConfigFormOpen" width="600px" append-to-body>
      <el-form ref="wechatConfigForm" :model="wechatConfigForm" :rules="wechatConfigRules" label-width="120px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="wechatConfigForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="AppID" prop="appId">
          <el-input v-model="wechatConfigForm.appId" placeholder="请输入微信公众号AppID" />
        </el-form-item>
        <el-form-item label="AppSecret" prop="appSecret">
          <el-input v-model="wechatConfigForm.appSecret" placeholder="请输入微信公众号AppSecret" type="password" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-radio-group v-model="wechatConfigForm.enabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="wechatConfigForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitWechatConfigForm">确 定</el-button>
        <el-button @click="cancelWechatConfig">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listActivity,
  getActivity,
  delActivity,
  addActivity,
  updateActivity,
  getWechatConfigs,
  saveWechatConfig,
  deleteWechatConfig,
  testWechatConfig,
  syncActivitiesFromWechat,
  syncActivitiesFromSingleWechat
} from "@/api/miniapp/activity";

export default {
  name: "MiniActivity",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      loading: true,
      ids: [],
      multiple: true,
      showSearch: true,
      total: 0,
      activityList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        status: null,
        wechatSource: null
      },
      form: {},
      rules: {
        title: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        coverImage: [
          { required: true, message: "封面图片不能为空", trigger: "blur" }
        ],
        articleUrl: [
          { required: true, message: "文章链接不能为空", trigger: "blur" }
        ]
      },
      // 微信公众号配置相关
      wechatConfigOpen: false,
      wechatConfigFormOpen: false,
      wechatConfigLoading: false,
      wechatConfigList: [],
      wechatConfigTitle: "",
      wechatConfigForm: {},
      wechatConfigRules: {
        name: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ],
        appId: [
          { required: true, message: "AppID不能为空", trigger: "blur" }
        ],
        appSecret: [
          { required: true, message: "AppSecret不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listActivity(this.queryParams).then(response => {
        this.activityList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取活动列表失败:', error);
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        activityId: null,
        title: null,
        description: null,
        coverImage: null,
        articleUrl: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.activityId);
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加活动";
    },
    handleUpdate(row) {
      this.reset();
      const activityId = row.activityId || this.ids;
      getActivity(activityId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改活动";
      }).catch(error => {
        console.error('获取活动详情失败:', error);
        this.$modal.msgError('获取活动详情失败');
      });
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.activityId != null) {
            updateActivity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改活动失败:', error);
              this.$modal.msgError('修改活动失败');
            });
          } else {
            addActivity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增活动失败:', error);
              this.$modal.msgError('新增活动失败');
            });
          }
        }
      });
    },
    handleDelete(row) {
      const activityIds = row.activityId ? [row.activityId] : this.ids;
      this.$modal.confirm('是否确认删除活动编号为"' + activityIds + '"的数据项？').then(() => {
        return delActivity(activityIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // ================================ 微信公众号配置相关方法 ================================
    handleWechatConfig() {
      this.wechatConfigOpen = true;
      this.getWechatConfigList();
    },
    getWechatConfigList() {
      this.wechatConfigLoading = true;
      getWechatConfigs().then(response => {
        this.wechatConfigList = response.data;
        this.wechatConfigLoading = false;
      }).catch(error => {
        console.error('获取微信配置失败:', error);
        this.wechatConfigLoading = false;
      });
    },
    handleAddWechatConfig() {
      this.resetWechatConfigForm();
      this.wechatConfigFormOpen = true;
      this.wechatConfigTitle = "新增微信公众号配置";
    },
    handleEditWechatConfig(row) {
      this.resetWechatConfigForm();
      this.wechatConfigForm = Object.assign({}, row);
      this.wechatConfigFormOpen = true;
      this.wechatConfigTitle = "修改微信公众号配置";
    },
    resetWechatConfigForm() {
      this.wechatConfigForm = {
        configId: null,
        name: null,
        appId: null,
        appSecret: null,
        enabled: true,
        remark: null
      };
      if (this.$refs.wechatConfigForm) {
        this.$refs.wechatConfigForm.resetFields();
      }
    },
    cancelWechatConfig() {
      this.wechatConfigFormOpen = false;
      this.resetWechatConfigForm();
    },
    submitWechatConfigForm() {
      this.$refs.wechatConfigForm.validate(valid => {
        if (valid) {
          saveWechatConfig(this.wechatConfigForm).then(response => {
            this.$modal.msgSuccess("保存成功");
            this.wechatConfigFormOpen = false;
            this.getWechatConfigList();
          }).catch(error => {
            console.error('保存微信配置失败:', error);
            this.$modal.msgError('保存失败');
          });
        }
      });
    },
    handleTestWechatConfig(row) {
      this.$modal.loading("正在测试连接...");
      testWechatConfig(row).then(response => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("连接测试成功");
      }).catch(error => {
        this.$modal.closeLoading();
        console.error('测试连接失败:', error);
        this.$modal.msgError('连接测试失败');
      });
    },
    handleDeleteWechatConfig(row) {
      this.$modal.confirm('是否确认删除配置"' + row.name + '"？').then(() => {
        return deleteWechatConfig(row.configId);
      }).then(() => {
        this.getWechatConfigList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleSyncFromWechat() {
      this.$modal.loading("正在同步活动...");
      syncActivitiesFromWechat().then(response => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess(response.msg);
        this.getList();
      }).catch(error => {
        this.$modal.closeLoading();
        console.error('同步活动失败:', error);
        this.$modal.msgError('同步活动失败');
      });
    },
    handleSyncFromSingleWechat(configId) {
      this.$modal.loading("正在同步活动...");
      syncActivitiesFromSingleWechat(configId).then(response => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("同步成功");
        this.getList();
        this.getWechatConfigList();
      }).catch(error => {
        this.$modal.closeLoading();
        console.error('同步活动失败:', error);
        this.$modal.msgError('同步活动失败');
      });
    },
    // 图片预览
    previewImage(imageUrl) {
      if (imageUrl) {
        // 创建一个新窗口显示图片
        const img = new Image();
        img.referrerPolicy = 'no-referrer';
        img.onload = () => {
          const newWindow = window.open('', '_blank');
          newWindow.document.write(`
            <html>
              <head><title>图片预览</title></head>
              <body style="margin:0;padding:20px;text-align:center;background:#f5f5f5;">
                <img src="${imageUrl}" referrerpolicy="no-referrer" style="max-width:100%;max-height:100%;object-fit:contain;" alt="图片预览" />
              </body>
            </html>
          `);
        };
        img.onerror = () => {
          this.$modal.msgError('图片加载失败');
        };
        img.src = imageUrl;
      }
    },
    // 图片加载错误处理
    handleImageError(event) {
      const originalSrc = event.target.getAttribute('data-original-src') || event.target.src;
      console.error('图片加载失败:', originalSrc);

      // 如果还没有尝试过代理，则尝试使用代理
      if (!event.target.hasAttribute('data-proxy-tried')) {
        event.target.setAttribute('data-proxy-tried', 'true');
        event.target.setAttribute('data-original-src', originalSrc);

        // 使用后端代理接口
        const proxyUrl = `/miniapp/activity/imageProxy?url=${encodeURIComponent(originalSrc)}`;
        event.target.src = proxyUrl;
        return;
      }

      // 代理也失败了，显示默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMTAwIDYwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAyNUw0NSAzNUw1NSAyNUw2NSAzNVY0NUgzNVYyNVoiIGZpbGw9IiNEOUQ5RDkiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEuNjY2NjdDNS4zOTk5MSAxLjY2NjY3IDEuNjY2NTggNS4zOTk5OSAxLjY2NjU4IDEwQzEuNjY2NTggMTQuNiA1LjM5OTkxIDE4LjMzMzMgMTAgMTguMzMzM0MxNC42IDE4LjMzMzMgMTguMzMzMyAxNC42IDE4LjMzMzMgMTBDMTguMzMzMyA1LjM5OTk5IDE0LjYgMS42NjY2NyAxMCAxLjY2NjY3Wk0xMCA1QzExLjM4MDcgNSAxMi41IDYuMTE5MjkgMTIuNSA3LjVDMTIuNSA4Ljg4MDcxIDExLjM4MDcgMTAgMTAgMTBDOC42MTkyOSAxMCA3LjUgOC44ODA3MSA3LjUgNy41QzcuNSA2LjExOTI5IDguNjE5MjkgNSAxMCA1Wk0xMCAxNS44MzMzQzguMzMzMjUgMTUuODMzMyA2Ljg3NDkxIDE0Ljk5MTcgNi4wNDE1OCAxMy43NUM2LjA0MTU4IDEyLjUgOC4zMzMyNSAxMS42NjY3IDEwIDExLjY2NjdDMTEuNjY2NyAxMS42NjY3IDEzLjk1ODMgMTIuNSAxMy45NTgzIDEzLjc1QzEzLjEyNSAxNC45OTE3IDExLjY2NjcgMTUuODMzMyAxMCAxNS44MzMzWiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4KPC9zdmc+';
      event.target.style.backgroundColor = '#f5f5f5';
      event.target.style.border = '1px dashed #ddd';
      event.target.title = '图片加载失败';
    },
    // 获取图片URL（优先使用原图，失败时自动切换到代理）
    getImageUrl(originalUrl) {
      if (!originalUrl) return '';

      // 如果是微信图片，直接返回原URL，让浏览器尝试加载
      // 失败时会触发@error事件，然后切换到代理
      return originalUrl;
    },
    // 图片加载成功处理
    handleImageLoad(event) {
      // 图片加载成功，移除错误样式
      event.target.style.backgroundColor = '';
      event.target.style.border = '';
      event.target.title = '点击预览';
    }
  }
};
</script>
