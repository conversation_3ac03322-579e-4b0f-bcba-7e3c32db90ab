package com.ruoyi.miniapp.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * 微信小程序数据解密工具类
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
public class WechatDecryptUtils
{
    private static final Logger logger = LoggerFactory.getLogger(WechatDecryptUtils.class);
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String CHARSET = "UTF-8";

    /**
     * 解密微信小程序数据
     * 
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 解密后的数据
     */
    public static String decrypt(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            // Base64解码
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            byte[] sessionKeyBytes = Base64.getDecoder().decode(sessionKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);

            // 创建密钥和初始向量
            SecretKeySpec secretKeySpec = new SecretKeySpec(sessionKeyBytes, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

            // 解密
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            String decryptedData = new String(decryptedBytes, StandardCharsets.UTF_8);

            logger.info("微信数据解密成功");
            return decryptedData;
        }
        catch (Exception e)
        {
            logger.error("微信数据解密失败", e);
            throw new RuntimeException("数据解密失败: " + e.getMessage());
        }
    }

    /**
     * 解密手机号信息
     * 
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 手机号信息
     */
    public static WechatPhoneInfo decryptPhoneNumber(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            String decryptedData = decrypt(encryptedData, sessionKey, iv);
            
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> dataMap = objectMapper.readValue(decryptedData, Map.class);
            
            WechatPhoneInfo phoneInfo = new WechatPhoneInfo();
            phoneInfo.setPhoneNumber((String) dataMap.get("phoneNumber"));
            phoneInfo.setPurePhoneNumber((String) dataMap.get("purePhoneNumber"));
            phoneInfo.setCountryCode((String) dataMap.get("countryCode"));
            
            return phoneInfo;
        }
        catch (Exception e)
        {
            logger.error("解密手机号失败", e);
            throw new RuntimeException("解密手机号失败: " + e.getMessage());
        }
    }

    /**
     * 解密用户信息
     * 
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 用户信息
     */
    public static WechatUserInfo decryptUserInfo(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            String decryptedData = decrypt(encryptedData, sessionKey, iv);
            
            // 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> dataMap = objectMapper.readValue(decryptedData, Map.class);
            
            WechatUserInfo userInfo = new WechatUserInfo();
            userInfo.setOpenId((String) dataMap.get("openId"));
            userInfo.setNickName((String) dataMap.get("nickName"));
            userInfo.setGender((Integer) dataMap.get("gender"));
            userInfo.setCity((String) dataMap.get("city"));
            userInfo.setProvince((String) dataMap.get("province"));
            userInfo.setCountry((String) dataMap.get("country"));
            userInfo.setAvatarUrl((String) dataMap.get("avatarUrl"));
            userInfo.setUnionId((String) dataMap.get("unionId"));
            
            return userInfo;
        }
        catch (Exception e)
        {
            logger.error("解密用户信息失败", e);
            throw new RuntimeException("解密用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 微信手机号信息
     */
    public static class WechatPhoneInfo
    {
        private String phoneNumber;      // 包含区号的完整手机号
        private String purePhoneNumber;  // 不包含区号的手机号
        private String countryCode;      // 区号

        public String getPhoneNumber()
        {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber)
        {
            this.phoneNumber = phoneNumber;
        }

        public String getPurePhoneNumber()
        {
            return purePhoneNumber;
        }

        public void setPurePhoneNumber(String purePhoneNumber)
        {
            this.purePhoneNumber = purePhoneNumber;
        }

        public String getCountryCode()
        {
            return countryCode;
        }

        public void setCountryCode(String countryCode)
        {
            this.countryCode = countryCode;
        }

        @Override
        public String toString()
        {
            return "WechatPhoneInfo{" +
                    "phoneNumber='" + phoneNumber + '\'' +
                    ", purePhoneNumber='" + purePhoneNumber + '\'' +
                    ", countryCode='" + countryCode + '\'' +
                    '}';
        }
    }

    /**
     * 微信用户信息
     */
    public static class WechatUserInfo
    {
        private String openId;
        private String nickName;
        private Integer gender;
        private String city;
        private String province;
        private String country;
        private String avatarUrl;
        private String unionId;

        public String getOpenId()
        {
            return openId;
        }

        public void setOpenId(String openId)
        {
            this.openId = openId;
        }

        public String getNickName()
        {
            return nickName;
        }

        public void setNickName(String nickName)
        {
            this.nickName = nickName;
        }

        public Integer getGender()
        {
            return gender;
        }

        public void setGender(Integer gender)
        {
            this.gender = gender;
        }

        public String getCity()
        {
            return city;
        }

        public void setCity(String city)
        {
            this.city = city;
        }

        public String getProvince()
        {
            return province;
        }

        public void setProvince(String province)
        {
            this.province = province;
        }

        public String getCountry()
        {
            return country;
        }

        public void setCountry(String country)
        {
            this.country = country;
        }

        public String getAvatarUrl()
        {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl)
        {
            this.avatarUrl = avatarUrl;
        }

        public String getUnionId()
        {
            return unionId;
        }

        public void setUnionId(String unionId)
        {
            this.unionId = unionId;
        }

        @Override
        public String toString()
        {
            return "WechatUserInfo{" +
                    "openId='" + openId + '\'' +
                    ", nickName='" + nickName + '\'' +
                    ", gender=" + gender +
                    ", city='" + city + '\'' +
                    ", province='" + province + '\'' +
                    ", country='" + country + '\'' +
                    ", avatarUrl='" + avatarUrl + '\'' +
                    ", unionId='" + unionId + '\'' +
                    '}';
        }
    }
}
