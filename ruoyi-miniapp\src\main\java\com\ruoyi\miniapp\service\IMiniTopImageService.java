package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniTopImage;

/**
 * top图片Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IMiniTopImageService 
{
    /**
     * 查询top图片
     * 
     * @param topImageId top图片主键
     * @return top图片
     */
    public MiniTopImage selectMiniTopImageByTopImageId(Long topImageId);

    /**
     * 查询top图片列表
     * 
     * @param miniTopImage top图片
     * @return top图片集合
     */
    public List<MiniTopImage> selectMiniTopImageList(MiniTopImage miniTopImage);

    /**
     * 新增top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    public int insertMiniTopImage(MiniTopImage miniTopImage);

    /**
     * 修改top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    public int updateMiniTopImage(MiniTopImage miniTopImage);

    /**
     * 批量删除top图片
     * 
     * @param topImageIds 需要删除的top图片主键集合
     * @return 结果
     */
    public int deleteMiniTopImageByTopImageIds(Long[] topImageIds);

    /**
     * 删除top图片信息
     * 
     * @param topImageId top图片主键
     * @return 结果
     */
    public int deleteMiniTopImageByTopImageId(Long topImageId);

    /**
     * 查询启用的top图片列表（小程序端调用）
     * 
     * @return top图片集合
     */
    public List<MiniTopImage> selectEnabledMiniTopImageList();

    /**
     * 根据页面标识查询启用的top图片列表（小程序端调用）
     * 
     * @param pageCode 页面标识
     * @return top图片集合
     */
    public List<MiniTopImage> selectEnabledMiniTopImageListByPage(String pageCode);
} 