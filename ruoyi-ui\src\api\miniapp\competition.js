import request from '@/utils/request'

// 查询大赛介绍列表
export function listCompetition(query) {
  return request({
    url: '/miniapp/competition/list',
    method: 'get',
    params: query
  })
}

// 查询大赛介绍详细
export function getCompetition(id) {
  return request({
    url: '/miniapp/competition/' + id,
    method: 'get'
  })
}

// 新增大赛介绍
export function addCompetition(data) {
  return request({
    url: '/miniapp/competition',
    method: 'post',
    data: data
  })
}

// 修改大赛介绍
export function updateCompetition(data) {
  return request({
    url: '/miniapp/competition',
    method: 'put',
    data: data
  })
}

// 删除大赛介绍
export function delCompetition(id) {
  return request({
    url: '/miniapp/competition/' + id,
    method: 'delete'
  })
}

// 获取当前启用的大赛介绍
export function getEnabledCompetition() {
  return request({
    url: '/miniapp/competition/enabled',
    method: 'get'
  })
}