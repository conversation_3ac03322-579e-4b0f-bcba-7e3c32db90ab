import request from '@/utils/request'

// 查询需求类型列表
export function listDemandCategory(query) {
  return request({
    url: '/miniapp/demandcategory/list',
    method: 'post',
    data: query
  })
}

// 查询需求类型详细
export function getDemandCategory(categoryId) {
  return request({
    url: '/miniapp/demandcategory/getInfo',
    method: 'post',
    data: categoryId
  })
}

// 新增需求类型
export function addDemandCategory(data) {
  return request({
    url: '/miniapp/demandcategory/add',
    method: 'post',
    data: data
  })
}

// 修改需求类型
export function updateDemandCategory(data) {
  return request({
    url: '/miniapp/demandcategory/edit',
    method: 'post',
    data: data
  })
}

// 删除需求类型
export function delDemandCategory(categoryIds) {
  return request({
    url: '/miniapp/demandcategory/remove',
    method: 'post',
    data: categoryIds
  })
}

// 获取启用的需求类型列表（小程序端）
export function getEnabledDemandCategoryList() {
  return request({
    url: '/miniapp/demandcategory/app/getEnabledList',
    method: 'post'
  })
}

// 获取需求类型的表单配置（小程序端，过滤隐藏字段）
export function getDemandCategoryFormConfig(categoryId) {
  return request({
    url: '/miniapp/demandcategory/app/getFormConfig',
    method: 'post',
    data: categoryId
  })
}