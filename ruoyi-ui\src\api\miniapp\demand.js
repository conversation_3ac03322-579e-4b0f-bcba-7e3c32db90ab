import request from '@/utils/request'

// 查询需求列表
export function listDemand(query) {
  return request({
    url: '/miniapp/demand/list',
    method: 'post',
    data: query
  })
}

// 查询需求详细
export function getDemand(demandId) {
  return request({
    url: '/miniapp/demand/getInfo',
    method: 'post',
    data: demandId
  })
}

// 新增需求
export function addDemand(data) {
  return request({
    url: '/miniapp/demand/add',
    method: 'post',
    data: data
  })
}

// 修改需求
export function updateDemand(data) {
  return request({
    url: '/miniapp/demand/edit',
    method: 'post',
    data: data
  })
}

// 删除需求
export function delDemand(demandIds) {
  // 确保传递的是数组格式
  const ids = Array.isArray(demandIds) ? demandIds : [demandIds];
  return request({
    url: '/miniapp/demand/remove',
    method: 'post',
    data: ids
  })
}

// 导出需求
export function exportDemand(query) {
  return request({
    url: '/miniapp/demand/export',
    method: 'post',
    data: query
  })
}

// 下架需求
export function offShelfDemand(demandId) {
  return request({
    url: '/miniapp/demand/offShelf',
    method: 'post',
    data: demandId
  })
}

// 上架需求
export function onShelfDemand(demandId) {
  return request({
    url: '/miniapp/demand/onShelf',
    method: 'post',
    data: demandId
  })
}

// 增加浏览次数
export function increaseViewCount(demandId) {
  return request({
    url: '/miniapp/demand/app/increaseViewCount',
    method: 'post',
    data: demandId
  })
}