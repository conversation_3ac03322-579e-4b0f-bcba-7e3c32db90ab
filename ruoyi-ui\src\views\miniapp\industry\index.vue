<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>产业树管理</span>
        <el-button style="float: right;" type="primary" size="small" @click="handleAddRoot">新增根节点</el-button>
      </div>
      <el-tree
        :data="industryTree"
        node-key="id"
        :props="treeProps"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <div class="node-content">
            <div class="node-info">
              <span class="level-badge" :class="'level-' + data.nodeLevel">L{{ data.nodeLevel }}</span>
              <span class="node-name">{{ data.nodeName }}</span>
              <span v-if="shouldShowStreamTypeInTree(data)" class="stream-badge" :class="'stream-' + data.streamType">
                {{ getStreamTypeName(data.streamType) }}
              </span>
              <span class="status-badge" :class="data.status === '0' ? 'status-normal' : 'status-disabled'">
                {{ data.status === '0' ? '正常' : '停用' }}
              </span>
            </div>
            <div class="node-actions">
              <el-button v-if="data.nodeLevel < 3" type="text" size="mini" @click.stop="handleAdd(data)">新增</el-button>
              <el-button type="text" size="mini" @click.stop="handleEdit(data)">编辑</el-button>
              <el-button type="text" size="mini" @click.stop="handleDelete(data)">删除</el-button>
            </div>
          </div>
        </span>
      </el-tree>
    </el-card>
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点名称" prop="nodeName">
              <el-input v-model="form.nodeName" placeholder="请输入节点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点层级" prop="nodeLevel">
              <el-input-number v-model="form.nodeLevel" :min="1" :max="3" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" style="width: 100%">
                <el-option label="正常" value="0" />
                <el-option label="停用" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.nodeLevel === 2 && shouldShowStreamType">
          <el-col :span="12">
            <el-form-item label="产业链位置" prop="streamType">
              <el-select v-model="form.streamType" placeholder="请选择产业链位置" style="width: 100%">
                <el-option label="上游" value="upstream" />
                <el-option label="中游" value="midstream" />
                <el-option label="下游" value="downstream" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点类型" prop="nodeType">
              <el-input v-model="form.nodeType" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.nodeLevel === 2 && !shouldShowStreamType">
          <el-col :span="12">
            <el-form-item label="节点类型" prop="nodeType">
              <el-input v-model="form.nodeType" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.nodeLevel === 1">
          <el-col :span="12">
            <el-form-item label="是否有上中下游" prop="hasStreamType">
              <el-select v-model="form.hasStreamType" placeholder="请选择是否有上中下游" style="width: 100%">
                <el-option label="否" value="0" />
                <el-option label="是" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点类型" prop="nodeType">
              <el-input v-model="form.nodeType" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.nodeLevel === 3">
          <el-col :span="12">
            <el-form-item label="节点类型" prop="nodeType">
              <el-input v-model="form.nodeType" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 节点描述 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="节点描述" prop="nodeDescription">
              <el-input
                v-model="form.nodeDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入节点描述（选填）"
                maxlength="500"
                show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 层级说明 -->
        <el-alert
          :title="getLevelDescription(form.nodeLevel)"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;">
        </el-alert>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAllIndustryTree,
  addIndustryNode,
  editIndustryNode,
  removeIndustryNode,
  getIndustryNodeInfo
} from '@/api/miniapp/industry'

export default {
  name: 'IndustryTree',
  data() {
    return {
      industryTree: [],
      treeProps: {
        children: 'children',
        label: 'nodeName'
      },
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        parentId: 0,
        nodeName: '',
        nodeDescription: '',
        sortOrder: 0,
        status: '0',
        nodeLevel: 1,
        streamType: '',
        hasStreamType: '0',
        nodeType: 'type'
      },
      rules: {
        nodeName: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
        sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        streamType: [
          {
            validator: (_, value, callback) => {
              if (this.shouldShowStreamType && !value) {
                callback(new Error('请选择产业链位置'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      currentNode: null,
      isEdit: false,
      parentNodeInfo: null
    }
  },
  computed: {
    shouldShowStreamType() {
      // 如果是第二层级，检查父节点是否有上中下游
      if (this.form.nodeLevel === 2 && this.parentNodeInfo) {
        return this.parentNodeInfo.hasStreamType === '1'
      }
      return false
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    loadTree() {
      getAllIndustryTree().then(res => {
        this.industryTree = res.data || []
      })
    },
    handleNodeClick(data) {
      this.currentNode = data
    },
    handleAddRoot() {
      this.isEdit = false
      this.dialogTitle = '新增根节点'
      this.parentNodeInfo = null
      this.form = {
        id: null,
        parentId: 0,
        nodeName: '',
        nodeDescription: '',
        sortOrder: 0,
        status: '0',
        nodeLevel: 1,
        streamType: '',
        hasStreamType: '0',
        nodeType: 'type'
      }
      this.dialogVisible = true
    },
    handleAdd(data) {
      this.isEdit = false
      this.dialogTitle = '新增子节点'
      const nextLevel = (data.nodeLevel || 1) + 1

      // 限制最多三层级
      if (nextLevel > 3) {
        this.$message.warning('最多支持三层级结构')
        return
      }

      // 如果是添加第二层级节点，需要获取父节点的完整信息
      if (nextLevel === 2) {
        getIndustryNodeInfo(data.id).then(res => {
          this.parentNodeInfo = res.data
          this.setupAddForm(data, nextLevel)
        })
      } else {
        this.parentNodeInfo = data
        this.setupAddForm(data, nextLevel)
      }
    },
    setupAddForm(data, nextLevel) {
      let nodeType = this.getNodeTypeByLevel(nextLevel)
      this.form = {
        id: null,
        parentId: data.id,
        nodeName: '',
        nodeDescription: '',
        sortOrder: 0,
        status: '0',
        nodeLevel: nextLevel,
        streamType: '',
        hasStreamType: '0',
        nodeType: nodeType
      }
      this.dialogVisible = true
    },
    handleEdit(data) {
      getIndustryNodeInfo(data.id).then(res => {
        this.isEdit = true
        this.dialogTitle = '编辑节点'

        // 如果是第二层级，需要获取父节点信息
        if (res.data.nodeLevel === 2 && res.data.parentId) {
          this.parentNodeInfo = this.findNodeById(this.industryTree, res.data.parentId)
        } else {
          this.parentNodeInfo = null
        }

        // 自动补全nodeType
        let nodeType = res.data.nodeType
        if (!nodeType) {
          if (res.data.nodeLevel === 1) nodeType = 'type'
          else if (res.data.nodeLevel === 2) nodeType = 'position'
          else if (res.data.nodeLevel === 3) nodeType = 'segment'
        }
        this.form = Object.assign({}, res.data, { nodeType: nodeType })
        this.dialogVisible = true
      })
    },
    handleDelete(data) {
      let confirmMessage = '确定要删除该节点吗？'
      if (data.nodeLevel < 3) {
        confirmMessage = '删除该节点将同时删除其所有子节点，确定要删除吗？'
      }

      this.$confirm(confirmMessage, '删除确认', {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }).then(() => {
        removeIndustryNode([data.id]).then(() => {
          this.$message.success('删除成功')
          this.loadTree()
        }).catch(error => {
          this.$message.error(error.msg || '删除失败')
        })
      })
    },
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        // 如果是编辑模式且状态发生变化，需要提示用户
        if (this.isEdit) {
          const originalNode = this.findNodeById(this.industryTree, this.form.id)
          const hasChildren = this.hasChildrenNodes(this.form.id)

          // 状态从启用改为停用
          if (originalNode && originalNode.status === '0' && this.form.status === '1' && hasChildren) {
            this.$confirm('停用该节点将同时停用其所有子节点，确定要继续吗？', '停用确认', {
              type: 'warning',
              confirmButtonText: '确定停用',
              cancelButtonText: '取消'
            }).then(() => {
              this.doSubmit()
            })
            return
          }

          // 状态从停用改为启用
          if (originalNode && originalNode.status === '1' && this.form.status === '0' && hasChildren) {
            this.$confirm('启用该节点将同时启用其所有子节点，确定要继续吗？', '启用确认', {
              type: 'info',
              confirmButtonText: '确定启用',
              cancelButtonText: '取消'
            }).then(() => {
              this.doSubmit()
            })
            return
          }
        }

        this.doSubmit()
      })
    },
    doSubmit() {
      if (this.isEdit) {
        editIndustryNode(this.form).then(() => {
          this.$message.success('修改成功')
          this.dialogVisible = false
          this.loadTree()
        })
      } else {
        addIndustryNode(this.form).then(() => {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.loadTree()
        })
      }
    },
    hasChildrenNodes(nodeId) {
      // 在树形结构中查找节点，检查是否有子节点
      const node = this.findNodeById(this.industryTree, nodeId)
      return node && node.children && node.children.length > 0
    },
    getNodeTypeByLevel(level) {
      switch (level) {
        case 1: return 'type'
        case 2: return 'position'
        case 3: return 'segment'
        default: return 'type'
      }
    },
    getStreamTypeName(streamType) {
      switch (streamType) {
        case 'upstream': return '上游'
        case 'midstream': return '中游'
        case 'downstream': return '下游'
        default: return streamType
      }
    },
    getLevelDescription(level) {
      switch (level) {
        case 1: return '第一层级：行业大类（如：新能源、硬科技等）'
        case 2:
          if (this.shouldShowStreamType) {
            return '第二层级：产业位置，需要选择上中下游（如：太阳能发电-上游、储能系统-中游等）'
          } else {
            return '第二层级：产业细分（如：太阳能发电、储能系统等）'
          }
        case 3: return '第三层级：具体细分领域（如：硅片制造、电池组件等）'
        default: return '请选择正确的层级'
      }
    },
    findNodeById(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) {
            return found
          }
        }
      }
      return null
    },
    shouldShowStreamTypeInTree(data) {
      // 只有第二层级且有streamType值才可能显示
      if (data.nodeLevel !== 2 || !data.streamType) {
        return false
      }

      // 查找父节点，检查是否有上中下游标识
      const parentNode = this.findParentNodeInTree(data)
      return parentNode && parentNode.hasStreamType === '1'
    },
    findParentNodeInTree(targetNode) {
      // 在树形结构中查找父节点
      return this.findNodeById(this.industryTree, targetNode.parentId)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-name {
  font-weight: 500;
}

.level-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  color: white;
  min-width: 20px;
  text-align: center;
}

.level-1 {
  background-color: #409EFF;
}

.level-2 {
  background-color: #67C23A;
}

.level-3 {
  background-color: #E6A23C;
}

.stream-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.stream-upstream {
  background-color: #F56C6C;
}

.stream-midstream {
  background-color: #909399;
}

.stream-downstream {
  background-color: #606266;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  color: white;
  font-weight: 500;
  margin-left: 4px;
}

.status-normal {
  background-color: #67C23A;
}

.status-disabled {
  background-color: #F56C6C;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.node-actions .el-button {
  padding: 4px 8px;
  margin-left: 0;
}

.el-tree-node__content {
  height: 40px;
  padding: 0 20px 0 10px;
}

.el-tree-node__content:hover {
  background-color: #f5f7fa;
}
</style>
