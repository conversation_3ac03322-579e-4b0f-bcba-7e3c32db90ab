# 敏感词过滤系统使用指南

## 概述

本系统基于 [sensitive-word](https://github.com/houbb/sensitive-word) 开源项目实现了完整的敏感词过滤功能，支持敏感词的管理、检测、过滤和日志记录。

## 功能特性

### 🔍 **敏感词检测**
- 支持实时敏感词检测
- 基于DFA算法，性能优异
- 支持6W+词库，且可自定义扩展
- 支持全角半角、大小写、繁简体互换
- 支持数字、英文、中文多种形式的互换

### 🛡️ **敏感词过滤**
- 支持敏感词替换（可自定义替换字符）
- 支持敏感词拒绝策略
- 支持仅记录日志不修改内容
- 支持白名单机制

### 📊 **管理功能**
- 敏感词分类管理
- 敏感词增删改查
- 敏感词命中统计
- 检测日志管理
- 缓存实时刷新

## 数据库设计

### 核心表结构

1. **sensitive_word_category** - 敏感词分类表
   - 支持多种分类：政治敏感、色情低俗、暴力血腥等
   - 支持自定义分类

2. **sensitive_word** - 敏感词表
   - 支持敏感词和白名单两种类型
   - 支持严重程度分级（轻微、中等、严重）
   - 支持正则表达式
   - 记录命中次数和最后命中时间

3. **sensitive_word_log** - 敏感词检测日志表
   - 记录检测操作的详细信息
   - 支持按用户、模块、时间等维度查询

## 使用方法

### 1. 数据库初始化

执行SQL脚本初始化数据库表和基础数据：

```sql
-- 执行敏感词表结构创建脚本
source sql/sensitive_word_tables.sql
```

### 2. 后端API使用

#### 敏感词检测
```java
// 检测文本是否包含敏感词
POST /miniapp/sensitiveWord/check
参数: text=要检测的文本内容

// 返回示例
{
  "code": 200,
  "data": {
    "contains": true,
    "hitWords": ["敏感词1", "敏感词2"]
  }
}
```

#### 敏感词过滤
```java
// 过滤文本中的敏感词
POST /miniapp/sensitiveWord/filter
参数: text=要过滤的文本内容&replacement=*

// 返回示例
{
  "code": 200,
  "data": {
    "originalText": "原始文本包含敏感词",
    "filteredText": "原始文本包含***",
    "hitWords": ["敏感词"]
  }
}
```

### 3. 注解方式集成

在需要敏感词过滤的方法上添加注解：

```java
@SensitiveWordFilter(
    moduleName = "弹幕管理", 
    strategy = SensitiveWordFilter.FilterStrategy.REPLACE,
    replacement = '*'
)
@PostMapping("/add")
public AjaxResult add(@RequestBody MiniBarrage miniBarrage) {
    return toAjax(miniBarrageService.insertMiniBarrage(miniBarrage));
}
```

#### 过滤策略说明

- **REPLACE**: 替换敏感词为指定字符
- **REJECT**: 拒绝包含敏感词的操作
- **LOG_ONLY**: 仅记录日志，不修改内容

### 4. 前端管理界面

#### 敏感词分类管理
- 路径: `/miniapp/sensitiveWord/category`
- 功能: 分类的增删改查、启用禁用

#### 敏感词管理
- 路径: `/miniapp/sensitiveWord/index`
- 功能: 敏感词的增删改查、批量操作、在线检测

#### 检测日志管理
- 路径: `/miniapp/sensitiveWord/log`
- 功能: 查看检测日志、统计分析、日志清理

### 5. 编程方式使用

```java
@Autowired
private ISensitiveWordService sensitiveWordService;

// 检测敏感词
boolean contains = sensitiveWordService.containsSensitiveWord("要检测的文本");

// 查找敏感词
List<String> hitWords = sensitiveWordService.findSensitiveWords("要检测的文本");

// 替换敏感词
String filtered = sensitiveWordService.replaceSensitiveWords("要过滤的文本");

// 自定义替换字符
String filtered = sensitiveWordService.replaceSensitiveWords("要过滤的文本", '#');

// 刷新缓存
sensitiveWordService.refreshSensitiveWordCache();
```

## 配置说明

### 敏感词引擎配置

系统默认配置了以下特性：

```java
SensitiveWordBs.newInstance()
    .ignoreCase(true)           // 忽略大小写
    .ignoreWidth(true)          // 忽略全角半角
    .ignoreNumStyle(true)       // 忽略数字写法
    .ignoreChineseStyle(true)   // 忽略中文书写格式
    .ignoreEnglishStyle(true)   // 忽略英文书写格式
    .ignoreRepeat(true)         // 忽略重复词
    .enableNumCheck(true)       // 启用数字检测
    .enableEmailCheck(true)     // 启用邮箱检测
    .enableUrlCheck(true)       // 启用链接检测
    .init();
```

### 权限配置

需要在系统菜单中配置相应的权限：

- `miniapp:sensitiveWord:list` - 查看敏感词列表
- `miniapp:sensitiveWord:add` - 新增敏感词
- `miniapp:sensitiveWord:edit` - 修改敏感词
- `miniapp:sensitiveWord:remove` - 删除敏感词
- `miniapp:sensitiveWord:refresh` - 刷新缓存
- `miniapp:sensitiveWordCategory:*` - 分类管理权限
- `miniapp:sensitiveWordLog:*` - 日志管理权限

## 性能优化

### 1. 缓存机制
- 敏感词数据会缓存在内存中，提高检测性能
- 支持手动刷新缓存，实时生效

### 2. 批量操作
- 支持批量导入敏感词
- 支持批量更新命中次数

### 3. 异步日志
- 敏感词检测日志异步记录，不影响主业务性能

## 最佳实践

### 1. 敏感词分类
建议按照以下维度进行分类：
- 政治敏感
- 色情低俗  
- 暴力血腥
- 违法犯罪
- 赌博诈骗
- 广告垃圾
- 其他敏感

### 2. 严重程度设置
- **轻微**: 一般不当言论，替换处理
- **中等**: 较为敏感内容，需要审核
- **严重**: 严重违规内容，直接拒绝

### 3. 白名单使用
对于可能被误判的正常词汇，及时加入白名单

### 4. 定期维护
- 定期清理过期日志
- 定期更新敏感词库
- 定期分析命中统计，优化词库

## 故障排除

### 1. 敏感词不生效
- 检查敏感词状态是否为启用
- 检查缓存是否已刷新
- 检查敏感词内容是否正确

### 2. 性能问题
- 检查敏感词数量是否过多
- 检查是否有过长的正则表达式
- 考虑优化敏感词库

### 3. 误判问题
- 添加白名单词汇
- 调整敏感词匹配规则
- 优化敏感词内容

## 技术支持

如有问题，请联系技术支持团队或查看相关文档：

- [sensitive-word 官方文档](https://github.com/houbb/sensitive-word)
- 项目技术文档
- 系统日志分析
