package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniPageContent;
import com.ruoyi.miniapp.mapper.MiniPageContentMapper;
import com.ruoyi.miniapp.service.IMiniPageContentService;

/**
 * 页面内容管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniPageContentServiceImpl implements IMiniPageContentService 
{
    @Autowired
    private MiniPageContentMapper miniPageContentMapper;

    /**
     * 查询页面内容管理
     * 
     * @param contentId 页面内容管理主键
     * @return 页面内容管理
     */
    @Override
    public MiniPageContent selectMiniPageContentByContentId(Long contentId)
    {
        return miniPageContentMapper.selectMiniPageContentByContentId(contentId);
    }

    /**
     * 查询页面内容管理列表
     * 
     * @param miniPageContent 页面内容管理
     * @return 页面内容管理
     */
    @Override
    public List<MiniPageContent> selectMiniPageContentList(MiniPageContent miniPageContent)
    {
        return miniPageContentMapper.selectMiniPageContentList(miniPageContent);
    }

    /**
     * 新增页面内容管理
     * 
     * @param miniPageContent 页面内容管理
     * @return 结果
     */
    @Override
    public int insertMiniPageContent(MiniPageContent miniPageContent)
    {
        miniPageContent.setCreateTime(DateUtils.getNowDate());
        return miniPageContentMapper.insertMiniPageContent(miniPageContent);
    }

    /**
     * 修改页面内容管理
     * 
     * @param miniPageContent 页面内容管理
     * @return 结果
     */
    @Override
    public int updateMiniPageContent(MiniPageContent miniPageContent)
    {
        miniPageContent.setUpdateTime(DateUtils.getNowDate());
        return miniPageContentMapper.updateMiniPageContent(miniPageContent);
    }

    /**
     * 批量删除页面内容管理
     * 
     * @param contentIds 需要删除的页面内容管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniPageContentByContentIds(Long[] contentIds)
    {
        return miniPageContentMapper.deleteMiniPageContentByContentIds(contentIds);
    }

    /**
     * 删除页面内容管理信息
     * 
     * @param contentId 页面内容管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniPageContentByContentId(Long contentId)
    {
        return miniPageContentMapper.deleteMiniPageContentByContentId(contentId);
    }

    /**
     * 根据页面编码查询页面内容
     * 
     * @param pageCode 页面编码
     * @return 页面内容管理
     */
    @Override
    public MiniPageContent selectMiniPageContentByPageCode(String pageCode)
    {
        return miniPageContentMapper.selectMiniPageContentByPageCode(pageCode);
    }

    /**
     * 根据页面标识查询页面内容
     * 
     * @param pageKey 页面标识
     * @return 页面内容
     */
    @Override
    public MiniPageContent selectMiniPageContentByPageKey(String pageKey)
    {
        return miniPageContentMapper.selectMiniPageContentByPageKey(pageKey);
    }

    /**
     * 查询启用的页面内容列表（小程序端调用）
     * 
     * @return 页面内容管理集合
     */
    @Override
    public List<MiniPageContent> selectEnabledMiniPageContentList()
    {
        return miniPageContentMapper.selectEnabledMiniPageContentList();
    }
} 