import request from '@/utils/request'

// 查询滚动通知列表
export function listNotice(query) {
  return request({
    url: '/miniapp/notice/list',
    method: 'post',
    data: query
  })
}

// 查询滚动通知详细
export function getNotice(noticeId) {
  return request({
    url: '/miniapp/notice/getInfo',
    method: 'post',
    data: noticeId
  })
}

// 新增滚动通知
export function addNotice(data) {
  return request({
    url: '/miniapp/notice/add',
    method: 'post',
    data: data
  })
}

// 修改滚动通知
export function updateNotice(data) {
  return request({
    url: '/miniapp/notice/edit',
    method: 'post',
    data: data
  })
}

// 删除滚动通知
export function delNotice(noticeIds) {
  return request({
    url: '/miniapp/notice/remove',
    method: 'post',
    data: noticeIds
  })
}

// 获取启用的滚动通知列表（小程序端）
export function getEnabledNoticeList() {
  return request({
    url: '/miniapp/notice/app/getEnabledList',
    method: 'post'
  })
} 