package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.ProjectGuidance;

/**
 * 项目指导活动Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IProjectGuidanceService 
{
    /**
     * 查询项目指导活动
     * 
     * @param guidanceId 项目指导活动主键
     * @return 项目指导活动
     */
    public ProjectGuidance selectProjectGuidanceByGuidanceId(Long guidanceId);

    /**
     * 查询项目指导活动列表
     * 
     * @param projectGuidance 项目指导活动
     * @return 项目指导活动集合
     */
    public List<ProjectGuidance> selectProjectGuidanceList(ProjectGuidance projectGuidance);

    /**
     * 新增项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    public int insertProjectGuidance(ProjectGuidance projectGuidance);

    /**
     * 修改项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    public int updateProjectGuidance(ProjectGuidance projectGuidance);

    /**
     * 批量删除项目指导活动
     * 
     * @param guidanceIds 需要删除的项目指导活动主键集合
     * @return 结果
     */
    public int deleteProjectGuidanceByGuidanceIds(Long[] guidanceIds);

    /**
     * 删除项目指导活动信息
     * 
     * @param guidanceId 项目指导活动主键
     * @return 结果
     */
    public int deleteProjectGuidanceByGuidanceId(Long guidanceId);
} 