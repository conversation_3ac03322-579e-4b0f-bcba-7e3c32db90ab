import request from '@/utils/request'

// 查询弹幕列表
export function listBarrage(query) {
  return request({
    url: '/miniapp/barrage/list',
    method: 'post',
    data: query
  })
}

// 查询弹幕详细
export function getBarrage(barrageId) {
  return request({
    url: '/miniapp/barrage/getInfo',
    method: 'post',
    data: barrageId
  })
}

// 新增弹幕
export function addBarrage(data) {
  return request({
    url: '/miniapp/barrage/add',
    method: 'post',
    data: data
  })
}

// 修改弹幕
export function updateBarrage(data) {
  return request({
    url: '/miniapp/barrage/edit',
    method: 'post',
    data: data
  })
}

// 删除弹幕
export function delBarrage(barrageId) {
  // 确保传递的是数组格式，后端期望 Long[] 类型
  const ids = Array.isArray(barrageId) ? barrageId : [barrageId];
  return request({
    url: '/miniapp/barrage/remove',
    method: 'post',
    data: ids
  })
}

// 导出弹幕
export function exportBarrage(query) {
  return request({
    url: '/miniapp/barrage/export',
    method: 'post',
    data: query
  })
}

// 审核弹幕
export function auditBarrage(data) {
  return request({
    url: '/miniapp/barrage/audit',
    method: 'post',
    data: data
  })
}

// 批量审核通过
export function batchApproveBarrage(barrageIds) {
  return request({
    url: '/miniapp/barrage/batchApprove',
    method: 'post',
    data: barrageIds
  })
}

// 批量审核拒绝
export function batchRejectBarrage(data) {
  return request({
    url: '/miniapp/barrage/batchReject',
    method: 'post',
    data: data
  })
}

// 获取弹幕配置
export function getBarrageConfig() {
  return request({
    url: '/miniapp/barrage/app/getConfig',
    method: 'post'
  })
}

// 更新弹幕配置
export function updateBarrageConfig(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data: data
  })
}

// 根据配置键名获取配置详情
export function getConfigByKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}
