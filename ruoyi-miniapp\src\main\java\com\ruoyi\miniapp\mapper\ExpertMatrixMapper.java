package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.ExpertMatrix;

/**
 * 专家矩阵Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ExpertMatrixMapper 
{
    /**
     * 查询专家矩阵
     * 
     * @param id 专家矩阵主键
     * @return 专家矩阵
     */
    public ExpertMatrix selectExpertMatrixById(Long id);

    /**
     * 查询专家矩阵列表
     * 
     * @param expertMatrix 专家矩阵
     * @return 专家矩阵集合
     */
    public List<ExpertMatrix> selectExpertMatrixList(ExpertMatrix expertMatrix);

    /**
     * 新增专家矩阵
     * 
     * @param expertMatrix 专家矩阵
     * @return 结果
     */
    public int insertExpertMatrix(ExpertMatrix expertMatrix);

    /**
     * 修改专家矩阵
     * 
     * @param expertMatrix 专家矩阵
     * @return 结果
     */
    public int updateExpertMatrix(ExpertMatrix expertMatrix);

    /**
     * 删除专家矩阵
     * 
     * @param id 专家矩阵主键
     * @return 结果
     */
    public int deleteExpertMatrixById(Long id);

    /**
     * 批量删除专家矩阵
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpertMatrixByIds(Long[] ids);
} 