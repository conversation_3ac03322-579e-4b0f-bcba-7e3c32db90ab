<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文章标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入文章标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作者" prop="author">
        <el-input
          v-model="queryParams.author"
          placeholder="请输入作者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleSync"
          v-hasPermi="['miniapp:news:sync']"
        >同步微信</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-refresh-right"
          size="mini"
          @click="handleResync"
          v-hasPermi="['miniapp:news:sync']"
        >重新同步</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:news:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="newsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文章标题" align="center" prop="title" min-width="200" show-overflow-tooltip />
      <el-table-column label="作者" align="center" prop="author" width="100" />
      <el-table-column label="封面图" align="center" prop="thumbUrl" width="100">
        <template slot-scope="scope">
          <img
            v-if="scope.row.thumbUrl"
            :src="scope.row.thumbUrl"
            alt="封面图"
            referrerpolicy="no-referrer"
            style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
            @click="previewImage(scope.row.thumbUrl)"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="文章摘要" align="center" prop="digest" min-width="200" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="微信创建时间" align="center" prop="wechatCreateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.wechatCreateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="同步时间" align="center" prop="syncTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.syncTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:news:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-link"
            @click="handleOpenLink(scope.row)"
          >打开链接</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看新闻详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="文章标题">
          <el-input v-model="form.title" :disabled="true" />
        </el-form-item>
        <el-form-item label="作者">
          <el-input v-model="form.author" :disabled="true" />
        </el-form-item>
        <el-form-item label="封面图">
          <div v-if="form.thumbUrl" class="thumb-preview">
            <img
              :src="form.thumbUrl"
              alt="封面图"
              referrerpolicy="no-referrer"
              style="max-width: 200px; max-height: 150px; object-fit: contain; border-radius: 4px; cursor: pointer;"
              @click="previewImage(form.thumbUrl)"
            />
          </div>
          <div v-else class="no-image">
            <i class="el-icon-picture-outline"></i>
            <div>暂无封面图</div>
          </div>
        </el-form-item>
        <el-form-item label="文章摘要">
          <el-input v-model="form.digest" type="textarea" :rows="4" :disabled="true" />
        </el-form-item>
        <el-form-item label="微信文章ID">
          <el-input v-model="form.wechatArticleId" :disabled="true" />
        </el-form-item>
        <el-form-item label="微信文章URL">
          <el-input v-model="form.wechatArticleUrl" :disabled="true" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.status" :disabled="true">
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="微信创建时间">
          <el-input :value="parseTime(form.wechatCreateTime, '{y}-{m}-{d} {h}:{i}:{s}')" :disabled="true" />
        </el-form-item>
        <el-form-item label="同步时间">
          <el-input :value="parseTime(form.syncTime, '{y}-{m}-{d} {h}:{i}:{s}')" :disabled="true" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 手动同步对话框 -->
    <el-dialog title="手动同步文章" :visible.sync="syncDialogVisible" width="400px" append-to-body>
      <el-form ref="syncForm" :model="syncForm" :rules="syncRules" label-width="80px">
        <el-form-item label="同步数量" prop="count">
          <el-input-number v-model="syncForm.count" :min="1" :max="50" />
        </el-form-item>
        <el-form-item label="偏移量" prop="offset">
          <el-input-number v-model="syncForm.offset" :min="0" :max="1000" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="syncDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSync">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNews, getNews, delNews, exportNews, syncWechatNews, syncWechatNewsWithParams, resyncWechatNews } from "@/api/miniapp/haitang/news";

export default {
  name: "News",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻中心表格数据
      newsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        author: null,
        status: null,
        wechatCreateTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 同步对话框
      syncDialogVisible: false,
      // 同步表单
      syncForm: {
        count: 20,
        offset: 0
      },
      // 同步表单校验
      syncRules: {
        count: [
          { required: true, message: "同步数量不能为空", trigger: "blur" }
        ],
        offset: [
          { required: true, message: "偏移量不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询新闻中心列表 */
    getList() {
      this.loading = true;
      listNews(this.queryParams).then(response => {
        this.newsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        author: null,
        thumbUrl: null,
        digest: null,
        wechatArticleId: null,
        wechatArticleUrl: null,
        status: null,
        wechatCreateTime: null,
        syncTime: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getNews(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看新闻详情";
      });
    },
    /** 打开链接 */
    handleOpenLink(row) {
      if (row.wechatArticleUrl) {
        window.open(row.wechatArticleUrl, '_blank');
      } else {
        this.$modal.msgError("该文章没有链接地址");
      }
    },
    /** 同步微信文章 */
    handleSync() {
      this.syncDialogVisible = true;
    },
    /** 重新同步微信文章 */
    handleResync() {
      this.$modal.confirm('此操作将清空所有现有数据并重新同步，是否继续？').then(() => {
        this.loading = true;
        resyncWechatNews(10).then(response => {
          this.$modal.msgSuccess(response.msg);
          this.getList();
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    /** 提交同步 */
    submitSync() {
      this.$refs["syncForm"].validate(valid => {
        if (valid) {
          this.syncDialogVisible = false;
          this.loading = true;
          syncWechatNewsWithParams(this.syncForm.count, this.syncForm.offset).then(response => {
            this.$modal.msgSuccess(response.msg);
            this.getList();
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/haitang/news/export', {
        ...this.queryParams
      }, `news_${new Date().getTime()}.xlsx`)
    },
    /** 预览图片 */
    previewImage(imageUrl) {
      if (imageUrl) {
        // 先显示加载中的对话框
        const loadingHtml = `
          <div style="text-align: center; padding: 20px;">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <div style="margin-top: 10px; color: #666;">图片加载中...</div>
          </div>
        `;

        const msgboxInstance = this.$msgbox({
          title: '封面图预览',
          dangerouslyUseHTMLString: true,
          message: loadingHtml,
          showCancelButton: false,
          showConfirmButton: true,
          confirmButtonText: '关闭',
          customClass: 'image-preview-dialog'
        });

        // 预加载图片
        const img = new Image();
        img.onload = () => {
          // 图片加载成功后更新对话框内容
          const imgHtml = `
            <img
              src="${imageUrl}"
              alt="封面图预览"
              referrerpolicy="no-referrer"
              style="max-width: 100%; max-height: 500px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);"
            />
          `;

          // 更新对话框内容
          const messageBox = document.querySelector('.image-preview-dialog .el-message-box__message');
          if (messageBox) {
            messageBox.innerHTML = imgHtml;
          }
        };

        img.onerror = () => {
          // 图片加载失败
          const errorHtml = `
            <div style="text-align: center; padding: 20px; color: #F56C6C;">
              <i class="el-icon-picture-outline" style="font-size: 48px; margin-bottom: 10px;"></i>
              <div>图片加载失败</div>
              <div style="font-size: 12px; margin-top: 5px; color: #999;">请检查网络连接或图片链接</div>
            </div>
          `;

          const messageBox = document.querySelector('.image-preview-dialog .el-message-box__message');
          if (messageBox) {
            messageBox.innerHTML = errorHtml;
          }
        };

        img.src = imageUrl;
        img.referrerPolicy = "no-referrer";
      }
    }

  }
};
</script>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
  flex-direction: column;
}

.image-slot i {
  font-size: 20px;
  margin-bottom: 5px;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 150px;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
  flex-direction: column;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.no-image i {
  font-size: 30px;
  margin-bottom: 10px;
}

.thumb-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.thumb-preview img {
  transition: transform 0.2s;
}

.thumb-preview img:hover {
  transform: scale(1.05);
}
</style>

<style>
/* 图片预览对话框样式 */
.image-preview-dialog {
  max-width: 80vw;
}

.image-preview-dialog .el-message-box__content {
  text-align: center;
  padding: 20px;
  max-height: 70vh;
  overflow: auto;
}

.image-preview-dialog .el-message-box__message {
  margin: 0;
  line-height: 1;
}

.image-preview-dialog .el-message-box__message img {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
