package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniNotice;

/**
 * 滚动通知Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniNoticeService 
{
    /**
     * 查询滚动通知
     * 
     * @param noticeId 滚动通知主键
     * @return 滚动通知
     */
    public MiniNotice selectMiniNoticeByNoticeId(Long noticeId);

    /**
     * 查询滚动通知列表
     * 
     * @param miniNotice 滚动通知
     * @return 滚动通知集合
     */
    public List<MiniNotice> selectMiniNoticeList(MiniNotice miniNotice);

    /**
     * 新增滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    public int insertMiniNotice(MiniNotice miniNotice);

    /**
     * 修改滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    public int updateMiniNotice(MiniNotice miniNotice);

    /**
     * 批量删除滚动通知
     * 
     * @param noticeIds 需要删除的滚动通知主键集合
     * @return 结果
     */
    public int deleteMiniNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 删除滚动通知信息
     * 
     * @param noticeId 滚动通知主键
     * @return 结果
     */
    public int deleteMiniNoticeByNoticeId(Long noticeId);

    /**
     * 查询启用的滚动通知列表（小程序端调用）
     * 
     * @return 滚动通知集合
     */
    public List<MiniNotice> selectEnabledMiniNoticeList();
}