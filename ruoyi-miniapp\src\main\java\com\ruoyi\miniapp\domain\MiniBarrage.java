package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 弹幕对象 mini_barrage
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public class MiniBarrage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 弹幕ID */
    private Long barrageId;

    /** 发布用户ID */
    @Excel(name = "发布用户ID")
    private Long userId;

    /** 发布用户昵称（冗余字段） */
    @Excel(name = "发布用户昵称")
    private String userNickName;

    /** 发布用户头像（冗余字段） */
    @Excel(name = "发布用户头像")
    private String userAvatarUrl;

    /** 弹幕内容 */
    @Excel(name = "弹幕内容")
    private String content;

    /** 审核状态（0待审核 1审核通过 2审核拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=审核通过,2=审核拒绝")
    private String auditStatus;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    public void setBarrageId(Long barrageId) 
    {
        this.barrageId = barrageId;
    }

    public Long getBarrageId() 
    {
        return barrageId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserNickName(String userNickName) 
    {
        this.userNickName = userNickName;
    }

    public String getUserNickName() 
    {
        return userNickName;
    }
    public void setUserAvatarUrl(String userAvatarUrl) 
    {
        this.userAvatarUrl = userAvatarUrl;
    }

    public String getUserAvatarUrl() 
    {
        return userAvatarUrl;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setAuditStatus(String auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("barrageId", getBarrageId())
            .append("userId", getUserId())
            .append("userNickName", getUserNickName())
            .append("userAvatarUrl", getUserAvatarUrl())
            .append("content", getContent())
            .append("auditStatus", getAuditStatus())
            .append("auditBy", getAuditBy())
            .append("auditTime", getAuditTime())
            .append("auditRemark", getAuditRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 