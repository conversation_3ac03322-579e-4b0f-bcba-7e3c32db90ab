package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniBanner;
import com.ruoyi.miniapp.service.IMiniBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 轮播图Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "轮播图管理")
@RestController
@RequestMapping("/miniapp/banner")
public class MiniBannerController extends BaseController
{
    @Autowired
    private IMiniBannerService miniBannerService;

    /**
     * 查询轮播图列表
     */
    @ApiOperation("查询轮播图列表")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniBanner miniBanner)
    {
        startPage();
        List<MiniBanner> list = miniBannerService.selectMiniBannerList(miniBanner);
        return getDataTable(list);
    }

    /**
     * 导出轮播图列表
     */
    @ApiOperation("导出轮播图列表")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:export')")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniBanner miniBanner)
    {
        List<MiniBanner> list = miniBannerService.selectMiniBannerList(miniBanner);
        ExcelUtil<MiniBanner> util = new ExcelUtil<MiniBanner>(MiniBanner.class);
        util.exportExcel(response, list, "轮播图数据");
    }

    /**
     * 获取轮播图详细信息
     */
    @ApiOperation("获取轮播图详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("轮播图ID") @RequestBody Long bannerId)
    {
        return AjaxResult.success(miniBannerService.selectMiniBannerByBannerId(bannerId));
    }

    /**
     * 新增轮播图
     */
    @ApiOperation("新增轮播图")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:add')")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("轮播图信息") @RequestBody MiniBanner miniBanner)
    {
        return toAjax(miniBannerService.insertMiniBanner(miniBanner));
    }

    /**
     * 修改轮播图
     */
    @ApiOperation("修改轮播图")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:edit')")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("轮播图信息") @RequestBody MiniBanner miniBanner)
    {
        return toAjax(miniBannerService.updateMiniBanner(miniBanner));
    }

    /**
     * 删除轮播图
     */
    @ApiOperation("删除轮播图")
    @PreAuthorize("@ss.hasPermi('miniapp:banner:remove')")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("轮播图ID数组") @RequestBody Long[] bannerIds)
    {
        return toAjax(miniBannerService.deleteMiniBannerByBannerIds(bannerIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的轮播图列表
     */
    @ApiOperation("获取启用的轮播图列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniBanner> list = miniBannerService.selectEnabledMiniBannerList();
        return AjaxResult.success(list);
    }
} 