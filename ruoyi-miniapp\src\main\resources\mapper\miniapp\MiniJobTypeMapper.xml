<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniJobTypeMapper">
    
    <resultMap type="MiniJobType" id="MiniJobTypeResult">
        <result property="jobTypeId"    column="job_type_id"    />
        <result property="typeName"     column="type_name"      />
        <result property="description"  column="description"    />
        <result property="sortOrder"    column="sort_order"     />
        <result property="status"       column="status"         />
        <result property="createBy"     column="create_by"      />
        <result property="createTime"   column="create_time"    />
        <result property="updateBy"     column="update_by"      />
        <result property="updateTime"   column="update_time"    />
        <result property="remark"       column="remark"         />
    </resultMap>

    <sql id="selectMiniJobTypeVo">
        select job_type_id, type_name, description, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_job_type
    </sql>

    <select id="selectMiniJobTypeList" parameterType="MiniJobType" resultMap="MiniJobTypeResult">
        <include refid="selectMiniJobTypeVo"/>
        <where>  
            <if test="typeName != null and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="description != null and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniJobTypeByJobTypeId" parameterType="Long" resultMap="MiniJobTypeResult">
        <include refid="selectMiniJobTypeVo"/>
        where job_type_id = #{jobTypeId}
    </select>

    <select id="selectMiniJobTypeAll" resultMap="MiniJobTypeResult">
        <include refid="selectMiniJobTypeVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectEnabledMiniJobTypeList" resultMap="MiniJobTypeResult">
        <include refid="selectMiniJobTypeVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniJobType" parameterType="MiniJobType" useGeneratedKeys="true" keyProperty="jobTypeId">
        insert into mini_job_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="description != null">#{description},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniJobType" parameterType="MiniJobType">
        update mini_job_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where job_type_id = #{jobTypeId}
    </update>

    <delete id="deleteMiniJobTypeByJobTypeId" parameterType="Long">
        delete from mini_job_type where job_type_id = #{jobTypeId}
    </delete>

    <delete id="deleteMiniJobTypeByJobTypeIds" parameterType="String">
        delete from mini_job_type where job_type_id in 
        <foreach item="jobTypeId" collection="array" open="(" separator="," close=")">
            #{jobTypeId}
        </foreach>
    </delete>

</mapper> 