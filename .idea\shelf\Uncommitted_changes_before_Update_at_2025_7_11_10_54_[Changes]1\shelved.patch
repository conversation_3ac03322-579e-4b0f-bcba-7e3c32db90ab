Index: .idea/misc.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"ExternalStorageConfigurationManager\" enabled=\"true\" />\r\n  <component name=\"MavenProjectsManager\">\r\n    <option name=\"originalFiles\">\r\n      <list>\r\n        <option value=\"$PROJECT_DIR$/pom.xml\" />\r\n      </list>\r\n    </option>\r\n  </component>\r\n  <component name=\"ProjectRootManager\" version=\"2\" languageLevel=\"JDK_21\" default=\"true\" project-jdk-name=\"corretto-21\" project-jdk-type=\"JavaSDK\" />\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/misc.xml b/.idea/misc.xml
--- a/.idea/misc.xml	(revision 827fddf704cee4ed1851712dd10e8894e986e9c2)
+++ b/.idea/misc.xml	(date 1752028160599)
@@ -8,5 +8,7 @@
       </list>
     </option>
   </component>
-  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="corretto-21" project-jdk-type="JavaSDK" />
+  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" project-jdk-name="17" project-jdk-type="JavaSDK">
+    <output url="file://$PROJECT_DIR$/../../maven/maven/mvn-repository" />
+  </component>
 </project>
\ No newline at end of file
Index: .idea/workspace.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"AutoImportSettings\">\r\n    <option name=\"autoReloadType\" value=\"SELECTIVE\" />\r\n  </component>\r\n  <component name=\"ChangeListManager\">\r\n    <list default=\"true\" id=\"76b702cc-2850-472c-9039-935eb1ffa66c\" name=\"Changes\" comment=\"需求广场over\">\r\n      <change beforePath=\"$PROJECT_DIR$/.idea/workspace.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/.idea/workspace.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-framework/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-framework/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-generator/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-generator/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniIndustryController.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniIndustryController.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustryPosition.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustrySegment.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustryType.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustryPositionMapper.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustrySegmentMapper.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustryTypeMapper.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustryPositionService.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustrySegmentService.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustryTypeService.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustryPositionServiceImpl.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustrySegmentServiceImpl.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustryTypeServiceImpl.java\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustryPositionMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustrySegmentMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustryTypeMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniJobMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniJobMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustryPositionMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustrySegmentMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustryTypeMapper.xml\" beforeDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniJobMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniJobMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-mq/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-mq/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-quartz/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-quartz/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-system/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-system/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/industry.js\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/industry.js\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/router/index.js\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/router/index.js\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/activity/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/activity/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/job/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/job/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/banner/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/banner/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/industry/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/industry/index.vue\" afterDir=\"false\" />\r\n    </list>\r\n    <option name=\"SHOW_DIALOG\" value=\"false\" />\r\n    <option name=\"HIGHLIGHT_CONFLICTS\" value=\"true\" />\r\n    <option name=\"HIGHLIGHT_NON_ACTIVE_CHANGELIST\" value=\"false\" />\r\n    <option name=\"LAST_RESOLUTION\" value=\"IGNORE\" />\r\n  </component>\r\n  <component name=\"Git.Settings\">\r\n    <option name=\"RECENT_GIT_ROOT_PATH\" value=\"$PROJECT_DIR$\" />\r\n  </component>\r\n  <component name=\"MavenImportPreferences\">\r\n    <option name=\"generalSettings\">\r\n      <MavenGeneralSettings>\r\n        <option name=\"customMavenHome\" value=\"D:/maven/maven/apache-maven-3.9.6\" />\r\n        <option name=\"localRepository\" value=\"D:\\maven\\maven\\mvn-repository\" />\r\n        <option name=\"mavenHomeTypeForPersistence\" value=\"CUSTOM\" />\r\n        <option name=\"userSettingsFile\" value=\"D:\\maven\\maven\\apache-maven-3.9.6\\conf\\settings.xml\" />\r\n      </MavenGeneralSettings>\r\n    </option>\r\n  </component>\r\n  <component name=\"ProjectColorInfo\">{\r\n  &quot;customColor&quot;: &quot;&quot;,\r\n  &quot;associatedIndex&quot;: 2\r\n}</component>\r\n  <component name=\"ProjectId\" id=\"2zInxgJ5QRhFOLKqgYyg52o7LSc\" />\r\n  <component name=\"ProjectViewState\">\r\n    <option name=\"showExcludedFiles\" value=\"false\" />\r\n    <option name=\"showLibraryContents\" value=\"true\" />\r\n  </component>\r\n  <component name=\"PropertiesComponent\">{\r\n  &quot;keyToString&quot;: {\r\n    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,\r\n    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,\r\n    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,\r\n    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,\r\n    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,\r\n    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,\r\n    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,\r\n    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Debug&quot;,\r\n    &quot;git-widget-placeholder&quot;: &quot;master&quot;,\r\n    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,\r\n    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/assets/icons/svg&quot;,\r\n    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,\r\n    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,\r\n    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,\r\n    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,\r\n    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,\r\n    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,\r\n    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,\r\n    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,\r\n    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,\r\n    &quot;ts.external.directory.path&quot;: &quot;D:\\\\IntelliJ IDEA 2024.2.3\\\\plugins\\\\javascript-plugin\\\\jsLanguageServicesImpl\\\\external&quot;,\r\n    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;\r\n  }\r\n}</component>\r\n  <component name=\"ReactorSettings\">\r\n    <option name=\"notificationShown\" value=\"true\" />\r\n  </component>\r\n  <component name=\"RecentsManager\">\r\n    <key name=\"CopyFile.RECENT_KEYS\">\r\n      <recent name=\"C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\assets\\icons\\svg\" />\r\n    </key>\r\n  </component>\r\n  <component name=\"RunManager\">\r\n    <configuration default=\"true\" type=\"JetRunConfigurationType\">\r\n      <module name=\"RuoYi-Vue\" />\r\n      <method v=\"2\">\r\n        <option name=\"Make\" enabled=\"true\" />\r\n      </method>\r\n    </configuration>\r\n    <configuration name=\"RuoYiApplication\" type=\"SpringBootApplicationConfigurationType\" factoryName=\"Spring Boot\" nameIsGenerated=\"true\">\r\n      <option name=\"FRAME_DEACTIVATION_UPDATE_POLICY\" value=\"UpdateClassesAndResources\" />\r\n      <module name=\"ruoyi-admin\" />\r\n      <option name=\"SPRING_BOOT_MAIN_CLASS\" value=\"com.ruoyi.RuoYiApplication\" />\r\n      <method v=\"2\">\r\n        <option name=\"Make\" enabled=\"true\" />\r\n      </method>\r\n    </configuration>\r\n  </component>\r\n  <component name=\"SharedIndexes\">\r\n    <attachedChunks>\r\n      <set>\r\n        <option value=\"bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11\" />\r\n        <option value=\"bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11\" />\r\n      </set>\r\n    </attachedChunks>\r\n  </component>\r\n  <component name=\"SpellCheckerSettings\" RuntimeDictionaries=\"0\" Folders=\"0\" CustomDictionaries=\"0\" DefaultDictionary=\"application-level\" UseSingleDictionary=\"true\" transferred=\"true\" />\r\n  <component name=\"TaskManager\">\r\n    <task active=\"true\" id=\"Default\" summary=\"Default task\">\r\n      <changelist id=\"76b702cc-2850-472c-9039-935eb1ffa66c\" name=\"Changes\" comment=\"\" />\r\n      <created>1751427704689</created>\r\n      <option name=\"number\" value=\"Default\" />\r\n      <option name=\"presentableId\" value=\"Default\" />\r\n      <updated>1751427704689</updated>\r\n      <workItem from=\"1751427708659\" duration=\"47875000\" />\r\n      <workItem from=\"1751855410839\" duration=\"795000\" />\r\n      <workItem from=\"1751856240516\" duration=\"21000\" />\r\n      <workItem from=\"1751856270005\" duration=\"37696000\" />\r\n      <workItem from=\"1751960768436\" duration=\"415000\" />\r\n      <workItem from=\"1751961217500\" duration=\"63000\" />\r\n      <workItem from=\"1751961297784\" duration=\"142000\" />\r\n      <workItem from=\"1751961476241\" duration=\"77000\" />\r\n      <workItem from=\"1751961563445\" duration=\"581000\" />\r\n      <workItem from=\"1751962169130\" duration=\"8266000\" />\r\n    </task>\r\n    <task id=\"LOCAL-00001\" summary=\"项目构建\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1751857168179</created>\r\n      <option name=\"number\" value=\"00001\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00001\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1751857168179</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00002\" summary=\"需求广场over\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1751874137094</created>\r\n      <option name=\"number\" value=\"00002\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00002\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1751874137094</updated>\r\n    </task>\r\n    <option name=\"localTasksCounter\" value=\"3\" />\r\n    <servers />\r\n  </component>\r\n  <component name=\"TypeScriptGeneratedFilesManager\">\r\n    <option name=\"version\" value=\"3\" />\r\n  </component>\r\n  <component name=\"VcsManagerConfiguration\">\r\n    <MESSAGE value=\"项目构建\" />\r\n    <MESSAGE value=\"需求广场over\" />\r\n    <option name=\"LAST_COMMIT_MESSAGE\" value=\"需求广场over\" />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/workspace.xml b/.idea/workspace.xml
--- a/.idea/workspace.xml	(revision 827fddf704cee4ed1851712dd10e8894e986e9c2)
+++ b/.idea/workspace.xml	(date 1752202347175)
@@ -4,47 +4,13 @@
     <option name="autoReloadType" value="SELECTIVE" />
   </component>
   <component name="ChangeListManager">
-    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="需求广场over">
+    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="专家矩阵，大赛介绍，视频展播状态异常调整">
+      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28__Changes_.xml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/.cursor/rules/project-overview.mdc" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/rules/project-overview.mdc" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/.cursor/rules/project-structure.mdc" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/rules/project-structure.mdc" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
       <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-framework/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-framework/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-generator/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-generator/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniIndustryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniIndustryController.java" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustryPosition.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustrySegment.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniIndustryType.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustryPositionMapper.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustrySegmentMapper.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniIndustryTypeMapper.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustryPositionService.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustrySegmentService.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniIndustryTypeService.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustryPositionServiceImpl.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustrySegmentServiceImpl.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniIndustryTypeServiceImpl.java" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustryPositionMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustrySegmentMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniIndustryTypeMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniJobMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniJobMapper.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustryPositionMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustrySegmentMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniIndustryTypeMapper.xml" beforeDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniJobMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniJobMapper.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-mq/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-mq/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-quartz/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-quartz/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-system/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/industry.js" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/industry.js" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/router/index.js" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/activity/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/activity/index.vue" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/job/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/job/index.vue" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/banner/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/banner/index.vue" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/industry/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/industry/index.vue" afterDir="false" />
     </list>
     <option name="SHOW_DIALOG" value="false" />
     <option name="HIGHLIGHT_CONFLICTS" value="true" />
@@ -57,7 +23,7 @@
   <component name="MavenImportPreferences">
     <option name="generalSettings">
       <MavenGeneralSettings>
-        <option name="customMavenHome" value="D:/maven/maven/apache-maven-3.9.6" />
+        <option name="customMavenHome" value="$PROJECT_DIR$/../../../maven/maven/apache-maven-3.9.6" />
         <option name="localRepository" value="D:\maven\maven\mvn-repository" />
         <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
         <option name="userSettingsFile" value="D:\maven\maven\apache-maven-3.9.6\conf\settings.xml" />
@@ -82,7 +48,7 @@
     &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
     &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
     &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
-    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Debug&quot;,
+    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Run&quot;,
     &quot;git-widget-placeholder&quot;: &quot;master&quot;,
     &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
     &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/assets/icons/svg&quot;,
@@ -94,7 +60,7 @@
     &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
     &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
     &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
-    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
+    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
     &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2024.2.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
     &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
   }
@@ -126,8 +92,8 @@
   <component name="SharedIndexes">
     <attachedChunks>
       <set>
-        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11" />
-        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11" />
+        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
+        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
       </set>
     </attachedChunks>
   </component>
@@ -142,13 +108,9 @@
       <workItem from="1751427708659" duration="47875000" />
       <workItem from="1751855410839" duration="795000" />
       <workItem from="1751856240516" duration="21000" />
-      <workItem from="1751856270005" duration="37696000" />
-      <workItem from="1751960768436" duration="415000" />
-      <workItem from="1751961217500" duration="63000" />
-      <workItem from="1751961297784" duration="142000" />
-      <workItem from="1751961476241" duration="77000" />
-      <workItem from="1751961563445" duration="581000" />
-      <workItem from="1751962169130" duration="8266000" />
+      <workItem from="1751856270005" duration="11045000" />
+      <workItem from="1752109805677" duration="2792000" />
+      <workItem from="1752130683421" duration="2415000" />
     </task>
     <task id="LOCAL-00001" summary="项目构建">
       <option name="closed" value="true" />
@@ -158,13 +120,13 @@
       <option name="project" value="LOCAL" />
       <updated>1751857168179</updated>
     </task>
-    <task id="LOCAL-00002" summary="需求广场over">
+    <task id="LOCAL-00002" summary="专家矩阵，大赛介绍，视频展播状态异常调整">
       <option name="closed" value="true" />
-      <created>1751874137094</created>
+      <created>1752028501003</created>
       <option name="number" value="00002" />
       <option name="presentableId" value="LOCAL-00002" />
       <option name="project" value="LOCAL" />
-      <updated>1751874137094</updated>
+      <updated>1752028501003</updated>
     </task>
     <option name="localTasksCounter" value="3" />
     <servers />
@@ -174,7 +136,7 @@
   </component>
   <component name="VcsManagerConfiguration">
     <MESSAGE value="项目构建" />
-    <MESSAGE value="需求广场over" />
-    <option name="LAST_COMMIT_MESSAGE" value="需求广场over" />
+    <MESSAGE value="专家矩阵，大赛介绍，视频展播状态异常调整" />
+    <option name="LAST_COMMIT_MESSAGE" value="专家矩阵，大赛介绍，视频展播状态异常调整" />
   </component>
 </project>
\ No newline at end of file
Index: .cursor/rules/project-structure.mdc
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>---\r\ndescription: \r\nglobs: \r\nalwaysApply: false\r\n---\r\n# 若依项目结构规则\r\n\r\n## 项目概览\r\n这是一个基于若依（RuoYi）框架v3.9.0的前后端分离项目，采用Spring Boot + Vue的技术栈。\r\n\r\n## 模块架构\r\n项目采用Maven多模块设计：\r\n\r\n### 后端模块\r\n- **ruoyi-admin**: 主应用模块，包含启动类 [RuoYiApplication.java](mdc:ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java) 和Web控制器\r\n- **ruoyi-framework**: 框架核心模块，包含安全配置、拦截器等核心功能\r\n- **ruoyi-system**: 系统管理模块，用户、角色、菜单等基础功能\r\n- **ruoyi-common**: 通用工具模块，常用工具类和配置\r\n- **ruoyi-quartz**: 定时任务模块\r\n- **ruoyi-generator**: 代码生成模块\r\n- **ruoyi-mq**: 消息队列模块（新增），包含RabbitMQ相关功能\r\n\r\n### 前端模块\r\n- **ruoyi-ui**: Vue前端项目，包含完整的管理界面\r\n\r\n## 主要配置文件\r\n- [pom.xml](mdc:pom.xml): 项目主配置文件，定义了所有模块和依赖\r\n- [application.yml](mdc:ruoyi-admin/src/main/resources/application.yml): Spring Boot主配置文件\r\n- [application-druid.yml](mdc:ruoyi-admin/src/main/resources/application-druid.yml): 数据库配置\r\n- [package.json](mdc:ruoyi-ui/package.json): 前端依赖配置\r\n\r\n## 数据库\r\n- 使用MySQL数据库，初始化脚本在 [sql/](mdc:sql) 目录\r\n- 集成Druid连接池进行数据库监控\r\n\r\n## 开发规范\r\n- 后端使用RESTful API设计\r\n- 前端采用Vue2 + Element UI\r\n- 权限控制使用JWT + Spring Security\r\n- 数据缓存使用Redis\r\n- 消息队列使用RabbitMQ\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.cursor/rules/project-structure.mdc b/.cursor/rules/project-structure.mdc
--- a/.cursor/rules/project-structure.mdc	(revision 827fddf704cee4ed1851712dd10e8894e986e9c2)
+++ b/.cursor/rules/project-structure.mdc	(date 1752198590650)
@@ -19,6 +19,7 @@
 - **ruoyi-quartz**: 定时任务模块
 - **ruoyi-generator**: 代码生成模块
 - **ruoyi-mq**: 消息队列模块（新增），包含RabbitMQ相关功能
+- **ruoyi-miniapp**: 小程序管理模块，包含活动报名、用户管理、内容管理等功能
 
 ### 前端模块
 - **ruoyi-ui**: Vue前端项目，包含完整的管理界面
Index: .idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28__Changes_.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28__Changes_.xml b/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28__Changes_.xml
new file mode 100644
--- /dev/null	(date 1752028180934)
+++ b/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28__Changes_.xml	(date 1752028180934)
@@ -0,0 +1,4 @@
+<changelist name="Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]" date="1752028160635" recycled="true" deleted="true">
+  <option name="PATH" value="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch" />
+  <option name="DESCRIPTION" value="Uncommitted changes before Update at 2025/7/9 10:28 [Changes]" />
+</changelist>
\ No newline at end of file
Index: .idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch b/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch
new file mode 100644
--- /dev/null	(date 1752028102765)
+++ b/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_9_10_28_[Changes]/shelved.patch	(date 1752028102765)
@@ -0,0 +1,255 @@
+Index: .idea/workspace.xml
+IDEA additional info:
+Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
+<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"AutoImportSettings\">\r\n    <option name=\"autoReloadType\" value=\"SELECTIVE\" />\r\n  </component>\r\n  <component name=\"ChangeListManager\">\r\n    <list default=\"true\" id=\"76b702cc-2850-472c-9039-935eb1ffa66c\" name=\"Changes\" comment=\"项目构建\">\r\n      <change afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniDemandCategoryController.java\" afterDir=\"false\" />\r\n      <change afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/assets/icons/svg/picture.svg\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/.idea/workspace.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/.idea/workspace.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniNoticeController.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniNoticeController.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTopImageController.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTopImageController.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniDemand.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniDemand.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTopImage.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTopImage.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTopImageMapper.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTopImageMapper.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTopImageService.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTopImageService.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTopImageServiceImpl.java\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTopImageServiceImpl.java\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandCategoryMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandCategoryMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniNoticeMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniNoticeMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTopImageMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTopImageMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniNoticeMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniNoticeMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTopImageMapper.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTopImageMapper.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/assets/styles/sidebar.scss\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/assets/styles/sidebar.scss\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/assets/styles/variables.scss\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/assets/styles/variables.scss\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/demand/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/demand/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/notice/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/notice/index.vue\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue\" afterDir=\"false\" />\r\n    </list>\r\n    <option name=\"SHOW_DIALOG\" value=\"false\" />\r\n    <option name=\"HIGHLIGHT_CONFLICTS\" value=\"true\" />\r\n    <option name=\"HIGHLIGHT_NON_ACTIVE_CHANGELIST\" value=\"false\" />\r\n    <option name=\"LAST_RESOLUTION\" value=\"IGNORE\" />\r\n  </component>\r\n  <component name=\"Git.Settings\">\r\n    <option name=\"RECENT_GIT_ROOT_PATH\" value=\"$PROJECT_DIR$\" />\r\n  </component>\r\n  <component name=\"MavenImportPreferences\">\r\n    <option name=\"generalSettings\">\r\n      <MavenGeneralSettings>\r\n        <option name=\"customMavenHome\" value=\"D:/maven/maven/apache-maven-3.9.6\" />\r\n        <option name=\"localRepository\" value=\"D:\\maven\\maven\\mvn-repository\" />\r\n        <option name=\"mavenHomeTypeForPersistence\" value=\"CUSTOM\" />\r\n        <option name=\"userSettingsFile\" value=\"D:\\maven\\maven\\apache-maven-3.9.6\\conf\\settings.xml\" />\r\n      </MavenGeneralSettings>\r\n    </option>\r\n  </component>\r\n  <component name=\"ProjectColorInfo\">{\r\n  &quot;customColor&quot;: &quot;&quot;,\r\n  &quot;associatedIndex&quot;: 2\r\n}</component>\r\n  <component name=\"ProjectId\" id=\"2zInxgJ5QRhFOLKqgYyg52o7LSc\" />\r\n  <component name=\"ProjectViewState\">\r\n    <option name=\"showExcludedFiles\" value=\"false\" />\r\n    <option name=\"showLibraryContents\" value=\"true\" />\r\n  </component>\r\n  <component name=\"PropertiesComponent\"><![CDATA[{\r\n  \"keyToString\": {\r\n    \"Maven.ruoyi [clean].executor\": \"Run\",\r\n    \"Maven.ruoyi [install].executor\": \"Run\",\r\n    \"RequestMappingsPanelOrder0\": \"0\",\r\n    \"RequestMappingsPanelOrder1\": \"1\",\r\n    \"RequestMappingsPanelWidth0\": \"75\",\r\n    \"RequestMappingsPanelWidth1\": \"75\",\r\n    \"RunOnceActivity.ShowReadmeOnStart\": \"true\",\r\n    \"Spring Boot.RuoYiApplication.executor\": \"Debug\",\r\n    \"git-widget-placeholder\": \"master\",\r\n    \"kotlin-language-version-configured\": \"true\",\r\n    \"last_opened_file_path\": \"C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/assets/icons/svg\",\r\n    \"node.js.detected.package.eslint\": \"true\",\r\n    \"node.js.detected.package.tslint\": \"true\",\r\n    \"node.js.selected.package.eslint\": \"(autodetect)\",\r\n    \"node.js.selected.package.tslint\": \"(autodetect)\",\r\n    \"nodejs_package_manager_path\": \"npm\",\r\n    \"project.structure.last.edited\": \"Project\",\r\n    \"project.structure.proportion\": \"0.0\",\r\n    \"project.structure.side.proportion\": \"0.0\",\r\n    \"settings.editor.selected.configurable\": \"MavenSettings\",\r\n    \"ts.external.directory.path\": \"D:\\\\IntelliJ IDEA 2024.2.3\\\\plugins\\\\javascript-plugin\\\\jsLanguageServicesImpl\\\\external\",\r\n    \"vue.rearranger.settings.migration\": \"true\"\r\n  }\r\n}]]></component>\r\n  <component name=\"ReactorSettings\">\r\n    <option name=\"notificationShown\" value=\"true\" />\r\n  </component>\r\n  <component name=\"RecentsManager\">\r\n    <key name=\"CopyFile.RECENT_KEYS\">\r\n      <recent name=\"C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\assets\\icons\\svg\" />\r\n    </key>\r\n  </component>\r\n  <component name=\"RunManager\">\r\n    <configuration default=\"true\" type=\"JetRunConfigurationType\">\r\n      <module name=\"RuoYi-Vue\" />\r\n      <method v=\"2\">\r\n        <option name=\"Make\" enabled=\"true\" />\r\n      </method>\r\n    </configuration>\r\n    <configuration name=\"RuoYiApplication\" type=\"SpringBootApplicationConfigurationType\" factoryName=\"Spring Boot\" nameIsGenerated=\"true\">\r\n      <option name=\"FRAME_DEACTIVATION_UPDATE_POLICY\" value=\"UpdateClassesAndResources\" />\r\n      <module name=\"ruoyi-admin\" />\r\n      <option name=\"SPRING_BOOT_MAIN_CLASS\" value=\"com.ruoyi.RuoYiApplication\" />\r\n      <method v=\"2\">\r\n        <option name=\"Make\" enabled=\"true\" />\r\n      </method>\r\n    </configuration>\r\n  </component>\r\n  <component name=\"SharedIndexes\">\r\n    <attachedChunks>\r\n      <set>\r\n        <option value=\"bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11\" />\r\n        <option value=\"bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11\" />\r\n      </set>\r\n    </attachedChunks>\r\n  </component>\r\n  <component name=\"SpellCheckerSettings\" RuntimeDictionaries=\"0\" Folders=\"0\" CustomDictionaries=\"0\" DefaultDictionary=\"application-level\" UseSingleDictionary=\"true\" transferred=\"true\" />\r\n  <component name=\"TaskManager\">\r\n    <task active=\"true\" id=\"Default\" summary=\"Default task\">\r\n      <changelist id=\"76b702cc-2850-472c-9039-935eb1ffa66c\" name=\"Changes\" comment=\"\" />\r\n      <created>1751427704689</created>\r\n      <option name=\"number\" value=\"Default\" />\r\n      <option name=\"presentableId\" value=\"Default\" />\r\n      <updated>1751427704689</updated>\r\n      <workItem from=\"1751427708659\" duration=\"47875000\" />\r\n      <workItem from=\"1751855410839\" duration=\"795000\" />\r\n      <workItem from=\"1751856240516\" duration=\"21000\" />\r\n      <workItem from=\"1751856270005\" duration=\"11045000\" />\r\n    </task>\r\n    <task id=\"LOCAL-00001\" summary=\"项目构建\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1751857168179</created>\r\n      <option name=\"number\" value=\"00001\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00001\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1751857168179</updated>\r\n    </task>\r\n    <option name=\"localTasksCounter\" value=\"2\" />\r\n    <servers />\r\n  </component>\r\n  <component name=\"TypeScriptGeneratedFilesManager\">\r\n    <option name=\"version\" value=\"3\" />\r\n  </component>\r\n  <component name=\"VcsManagerConfiguration\">\r\n    <MESSAGE value=\"项目构建\" />\r\n    <option name=\"LAST_COMMIT_MESSAGE\" value=\"项目构建\" />\r\n  </component>\r\n</project>
+Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
+<+>UTF-8
+===================================================================
+diff --git a/.idea/workspace.xml b/.idea/workspace.xml
+--- a/.idea/workspace.xml	(revision 0c3db6771e21fbfc3b63e70e0172f28c743fd34b)
++++ b/.idea/workspace.xml	(date 1752027449758)
+@@ -4,30 +4,12 @@
+     <option name="autoReloadType" value="SELECTIVE" />
+   </component>
+   <component name="ChangeListManager">
+-    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="项目构建">
+-      <change afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniDemandCategoryController.java" afterDir="false" />
+-      <change afterPath="$PROJECT_DIR$/ruoyi-ui/src/assets/icons/svg/picture.svg" afterDir="false" />
++    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="创赛路演：专家矩阵">
++      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
+       <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniNoticeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniNoticeController.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTopImageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTopImageController.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniDemand.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniDemand.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTopImage.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTopImage.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTopImageMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTopImageMapper.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTopImageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTopImageService.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTopImageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTopImageServiceImpl.java" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandCategoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandCategoryMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniDemandMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniNoticeMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniNoticeMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTopImageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTopImageMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniNoticeMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniNoticeMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTopImageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTopImageMapper.xml" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/assets/styles/sidebar.scss" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/assets/styles/sidebar.scss" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/assets/styles/variables.scss" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/assets/styles/variables.scss" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/demand/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/demand/index.vue" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/notice/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/notice/index.vue" afterDir="false" />
+-      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/content/topimage/index.vue" afterDir="false" />
++      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/config/competition/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/config/competition/index.vue" afterDir="false" />
++      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/config/video/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/config/video/index.vue" afterDir="false" />
++      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue" afterDir="false" />
+     </list>
+     <option name="SHOW_DIALOG" value="false" />
+     <option name="HIGHLIGHT_CONFLICTS" value="true" />
+@@ -40,7 +22,7 @@
+   <component name="MavenImportPreferences">
+     <option name="generalSettings">
+       <MavenGeneralSettings>
+-        <option name="customMavenHome" value="D:/maven/maven/apache-maven-3.9.6" />
++        <option name="customMavenHome" value="$PROJECT_DIR$/../../../maven/maven/apache-maven-3.9.6" />
+         <option name="localRepository" value="D:\maven\maven\mvn-repository" />
+         <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
+         <option name="userSettingsFile" value="D:\maven\maven\apache-maven-3.9.6\conf\settings.xml" />
+@@ -65,7 +47,7 @@
+     "RequestMappingsPanelWidth0": "75",
+     "RequestMappingsPanelWidth1": "75",
+     "RunOnceActivity.ShowReadmeOnStart": "true",
+-    "Spring Boot.RuoYiApplication.executor": "Debug",
++    "Spring Boot.RuoYiApplication.executor": "Run",
+     "git-widget-placeholder": "master",
+     "kotlin-language-version-configured": "true",
+     "last_opened_file_path": "C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/assets/icons/svg",
+@@ -78,7 +60,7 @@
+     "project.structure.proportion": "0.0",
+     "project.structure.side.proportion": "0.0",
+     "settings.editor.selected.configurable": "MavenSettings",
+-    "ts.external.directory.path": "D:\\IntelliJ IDEA 2024.2.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
++    "ts.external.directory.path": "D:\\develop\\IntelliJ IDEA 2024.2.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
+     "vue.rearranger.settings.migration": "true"
+   }
+ }]]></component>
+@@ -135,7 +117,55 @@
+       <option name="project" value="LOCAL" />
+       <updated>1751857168179</updated>
+     </task>
+-    <option name="localTasksCounter" value="2" />
++    <task id="LOCAL-00002" summary="海棠杯轮播图">
++      <option name="closed" value="true" />
++      <created>1751876582282</created>
++      <option name="number" value="00002" />
++      <option name="presentableId" value="LOCAL-00002" />
++      <option name="project" value="LOCAL" />
++      <updated>1751876582282</updated>
++    </task>
++    <task id="LOCAL-00003" summary="创赛路演：项目报名">
++      <option name="closed" value="true" />
++      <created>1751882196688</created>
++      <option name="number" value="00003" />
++      <option name="presentableId" value="LOCAL-00003" />
++      <option name="project" value="LOCAL" />
++      <updated>1751882196688</updated>
++    </task>
++    <task id="LOCAL-00004" summary="创赛路演：项目辅导报名&#10;（项目指导活动模块、指导活动报名模块）">
++      <option name="closed" value="true" />
++      <created>1751885422732</created>
++      <option name="number" value="00004" />
++      <option name="presentableId" value="LOCAL-00004" />
++      <option name="project" value="LOCAL" />
++      <updated>1751885422732</updated>
++    </task>
++    <task id="LOCAL-00005" summary="创赛路演：大赛介绍">
++      <option name="closed" value="true" />
++      <created>1751944485142</created>
++      <option name="number" value="00005" />
++      <option name="presentableId" value="LOCAL-00005" />
++      <option name="project" value="LOCAL" />
++      <updated>1751944485142</updated>
++    </task>
++    <task id="LOCAL-00006" summary="创赛路演：视频展播">
++      <option name="closed" value="true" />
++      <created>1751959225773</created>
++      <option name="number" value="00006" />
++      <option name="presentableId" value="LOCAL-00006" />
++      <option name="project" value="LOCAL" />
++      <updated>1751959225773</updated>
++    </task>
++    <task id="LOCAL-00007" summary="创赛路演：专家矩阵">
++      <option name="closed" value="true" />
++      <created>1751963864480</created>
++      <option name="number" value="00007" />
++      <option name="presentableId" value="LOCAL-00007" />
++      <option name="project" value="LOCAL" />
++      <updated>1751963864480</updated>
++    </task>
++    <option name="localTasksCounter" value="8" />
+     <servers />
+   </component>
+   <component name="TypeScriptGeneratedFilesManager">
+@@ -143,6 +173,12 @@
+   </component>
+   <component name="VcsManagerConfiguration">
+     <MESSAGE value="项目构建" />
+-    <option name="LAST_COMMIT_MESSAGE" value="项目构建" />
++    <MESSAGE value="海棠杯轮播图" />
++    <MESSAGE value="创赛路演：项目报名" />
++    <MESSAGE value="创赛路演：项目辅导报名&#10;（项目指导活动模块、指导活动报名模块）" />
++    <MESSAGE value="创赛路演：大赛介绍" />
++    <MESSAGE value="创赛路演：视频展播" />
++    <MESSAGE value="创赛路演：专家矩阵" />
++    <option name="LAST_COMMIT_MESSAGE" value="创赛路演：专家矩阵" />
+   </component>
+ </project>
+\ No newline at end of file
+Index: ruoyi-ui/src/views/miniapp/config/competition/index.vue
+IDEA additional info:
+Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
+<+><template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"联系人\" prop=\"contactName\">\r\n        <el-input\r\n          v-model=\"queryParams.contactName\"\r\n          placeholder=\"请输入联系人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option label=\"启用\" value=\"1\" />\r\n          <el-option label=\"禁用\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:competition:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:competition:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:competition:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:competition:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"competitionList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"介绍ID\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"顶部图片\" align=\"center\" prop=\"topImageUrl\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.topImageUrl\" :width=\"50\" :height=\"50\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactName\" />\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n      <el-table-column label=\"联系微信\" align=\"center\" prop=\"contactWechat\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:competition:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:competition:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改大赛介绍对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"顶部图片\" prop=\"topImageUrl\">\r\n              <image-upload v-model=\"form.topImageUrl\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"大赛描述\" prop=\"descriptionContent\">\r\n              <editor v-model=\"form.descriptionContent\" :min-height=\"192\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"赛程安排\" prop=\"scheduleContent\">\r\n              <editor v-model=\"form.scheduleContent\" :min-height=\"192\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"报名条件\" prop=\"registrationConditions\">\r\n              <editor v-model=\"form.registrationConditions\" :min-height=\"192\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"常见问题\" prop=\"faqContent\">\r\n              <editor v-model=\"form.faqContent\" :min-height=\"192\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"商务合作\" prop=\"businessCooperation\">\r\n              <editor v-model=\"form.businessCooperation\" :min-height=\"192\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"赞助商图片\" prop=\"sponsorImageUrl\">\r\n              <image-upload v-model=\"form.sponsorImageUrl\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人姓名\" prop=\"contactName\">\r\n              <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人姓名\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入联系电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系微信\" prop=\"contactWechat\">\r\n              <el-input v-model=\"form.contactWechat\" placeholder=\"请输入联系微信\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio :label=\"1\">启用</el-radio>\r\n                <el-radio :label=\"0\">禁用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCompetition, getCompetition, delCompetition, addCompetition, updateCompetition } from \"@/api/miniapp/competition\";\r\n\r\nexport default {\r\n  name: \"Competition\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 大赛介绍表格数据\r\n      competitionList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contactName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contactName: [\r\n          { required: true, message: \"联系人姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询大赛介绍列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCompetition(this.queryParams).then(response => {\r\n        this.competitionList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        topImageUrl: null,\r\n        descriptionContent: null,\r\n        scheduleContent: null,\r\n        registrationConditions: null,\r\n        faqContent: null,\r\n        businessCooperation: null,\r\n        sponsorImageUrl: null,\r\n        contactName: null,\r\n        contactPhone: null,\r\n        contactWechat: null,\r\n        status: 1,\r\n        createdAt: null,\r\n        updatedAt: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加大赛介绍\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getCompetition(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改大赛介绍\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateCompetition(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCompetition(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除大赛介绍编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delCompetition(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/competition/export', {\r\n        ...this.queryParams\r\n      }, `大赛介绍_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>
+Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
+<+>UTF-8
+===================================================================
+diff --git a/ruoyi-ui/src/views/miniapp/config/competition/index.vue b/ruoyi-ui/src/views/miniapp/config/competition/index.vue
+--- a/ruoyi-ui/src/views/miniapp/config/competition/index.vue	(revision 0c3db6771e21fbfc3b63e70e0172f28c743fd34b)
++++ b/ruoyi-ui/src/views/miniapp/config/competition/index.vue	(date 1752027727722)
+@@ -173,8 +173,8 @@
+           <el-col :span="12">
+             <el-form-item label="状态" prop="status">
+               <el-radio-group v-model="form.status">
+-                <el-radio :label="1">启用</el-radio>
+-                <el-radio :label="0">禁用</el-radio>
++                <el-radio :label="0">启用</el-radio>
++                <el-radio :label="1">禁用</el-radio>
+               </el-radio-group>
+             </el-form-item>
+           </el-col>
+@@ -269,7 +269,7 @@
+         contactName: null,
+         contactPhone: null,
+         contactWechat: null,
+-        status: 1,
++        status: 0,
+         createdAt: null,
+         updatedAt: null
+       };
+Index: ruoyi-ui/src/views/miniapp/config/video/index.vue
+IDEA additional info:
+Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
+<+><template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"视频标题\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入视频标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option label=\"启用\" value=\"1\" />\r\n          <el-option label=\"禁用\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:video:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:video:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:video:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:video:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"videoList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"视频ID\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"视频标题\" align=\"center\" prop=\"title\" show-overflow-tooltip />\r\n      <el-table-column label=\"视频预览\" align=\"center\" prop=\"videoUrl\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <video v-if=\"scope.row.videoUrl\" :src=\"getVideoUrl(scope.row.videoUrl)\" controls style=\"width: 150px; height: 100px;\" preload=\"metadata\">\r\n            您的浏览器不支持视频播放\r\n          </video>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:video:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:video:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改视频展播对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"视频标题\" prop=\"title\">\r\n              <el-input v-model=\"form.title\" placeholder=\"请输入视频标题\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"视频文件\" prop=\"videoUrl\">\r\n              <file-upload \r\n                v-model=\"form.videoUrl\"\r\n                :limit=\"1\"\r\n                :fileSize=\"100\"\r\n                :fileType=\"['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']\"\r\n                :isShowTip=\"true\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio :label=\"1\">启用</el-radio>\r\n                <el-radio :label=\"0\">禁用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVideo, getVideo, delVideo, addVideo, updateVideo } from \"@/api/miniapp/video\";\r\n\r\nexport default {\r\n  name: \"VideoShowcase\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 视频展播表格数据\r\n      videoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        title: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        title: [\r\n          { required: true, message: \"视频标题不能为空\", trigger: \"blur\" }\r\n        ],\r\n        videoUrl: [\r\n          { required: true, message: \"视频文件不能为空\", trigger: \"blur\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 获取视频URL\r\n    getVideoUrl(url) {\r\n      if (!url) return '';\r\n      if (url.startsWith('http')) {\r\n        return url;\r\n      }\r\n      return process.env.VUE_APP_BASE_API + url;\r\n    },\r\n    /** 查询视频展播列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVideo(this.queryParams).then(response => {\r\n        this.videoList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        title: null,\r\n        videoUrl: null,\r\n        sortOrder: 0,\r\n        status: 1,\r\n        createdAt: null,\r\n        updatedAt: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加视频展播\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getVideo(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改视频展播\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateVideo(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVideo(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除视频展播编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delVideo(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/video/export', {\r\n        ...this.queryParams\r\n      }, `视频展播_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script> 
+Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
+<+>UTF-8
+===================================================================
+diff --git a/ruoyi-ui/src/views/miniapp/config/video/index.vue b/ruoyi-ui/src/views/miniapp/config/video/index.vue
+--- a/ruoyi-ui/src/views/miniapp/config/video/index.vue	(revision 0c3db6771e21fbfc3b63e70e0172f28c743fd34b)
++++ b/ruoyi-ui/src/views/miniapp/config/video/index.vue	(date 1752027744381)
+@@ -146,8 +146,8 @@
+           <el-col :span="12">
+             <el-form-item label="状态" prop="status">
+               <el-radio-group v-model="form.status">
+-                <el-radio :label="1">启用</el-radio>
+-                <el-radio :label="0">禁用</el-radio>
++                <el-radio :label="0">启用</el-radio>
++                <el-radio :label="1">禁用</el-radio>
+               </el-radio-group>
+             </el-form-item>
+           </el-col>
+@@ -243,7 +243,7 @@
+         title: null,
+         videoUrl: null,
+         sortOrder: 0,
+-        status: 1,
++        status: 0,
+         createdAt: null,
+         updatedAt: null
+       };
+Index: .idea/misc.xml
+IDEA additional info:
+Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
+<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"ExternalStorageConfigurationManager\" enabled=\"true\" />\r\n  <component name=\"MavenProjectsManager\">\r\n    <option name=\"originalFiles\">\r\n      <list>\r\n        <option value=\"$PROJECT_DIR$/pom.xml\" />\r\n      </list>\r\n    </option>\r\n  </component>\r\n  <component name=\"ProjectRootManager\" version=\"2\" languageLevel=\"JDK_21\" default=\"true\" project-jdk-name=\"corretto-21\" project-jdk-type=\"JavaSDK\" />\r\n</project>
+Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
+<+>UTF-8
+===================================================================
+diff --git a/.idea/misc.xml b/.idea/misc.xml
+--- a/.idea/misc.xml	(revision 0c3db6771e21fbfc3b63e70e0172f28c743fd34b)
++++ b/.idea/misc.xml	(date 1751874336372)
+@@ -8,5 +8,7 @@
+       </list>
+     </option>
+   </component>
+-  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="corretto-21" project-jdk-type="JavaSDK" />
++  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" project-jdk-name="17" project-jdk-type="JavaSDK">
++    <output url="file://$PROJECT_DIR$/../../maven/maven/mvn-repository" />
++  </component>
+ </project>
+\ No newline at end of file
+Index: ruoyi-ui/src/views/miniapp/haitang/expert/index.vue
+IDEA additional info:
+Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
+<+><template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"专家姓名\" prop=\"expertName\">\r\n        <el-input\r\n          v-model=\"queryParams.expertName\"\r\n          placeholder=\"请输入专家姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"专家职位\" prop=\"expertTitle\">\r\n        <el-input\r\n          v-model=\"queryParams.expertTitle\"\r\n          placeholder=\"请输入专家职位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"专家公司\" prop=\"expertCompany\">\r\n        <el-input\r\n          v-model=\"queryParams.expertCompany\"\r\n          placeholder=\"请输入专家公司\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:expert:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:expert:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:expert:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:expert:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"expertList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"专家ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"头像\" align=\"center\" prop=\"avatarUrl\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.avatarUrl\" :width=\"50\" :height=\"50\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"专家姓名\" align=\"center\" prop=\"expertName\" />\r\n      <el-table-column label=\"专家职位\" align=\"center\" prop=\"expertTitle\" show-overflow-tooltip />\r\n      <el-table-column label=\"专家公司\" align=\"center\" prop=\"expertCompany\" show-overflow-tooltip />\r\n      <el-table-column label=\"从业年限\" align=\"center\" prop=\"yearsExperience\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.yearsExperience }}年</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:expert:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:expert:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改专家矩阵对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"专家姓名\" prop=\"expertName\">\r\n              <el-input v-model=\"form.expertName\" placeholder=\"请输入专家姓名\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"专家职位\" prop=\"expertTitle\">\r\n              <el-input v-model=\"form.expertTitle\" placeholder=\"请输入专家职位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"专家公司\" prop=\"expertCompany\">\r\n              <el-input v-model=\"form.expertCompany\" placeholder=\"请输入专家公司\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"从业年限\" prop=\"yearsExperience\">\r\n              <el-input-number v-model=\"form.yearsExperience\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"头像\" prop=\"avatarUrl\">\r\n              <image-upload v-model=\"form.avatarUrl\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"专家介绍\" prop=\"expertIntro\">\r\n              <el-input v-model=\"form.expertIntro\" type=\"textarea\" placeholder=\"请输入专家介绍\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"专业领域\" prop=\"expertiseFields\">\r\n              <el-input v-model=\"form.expertiseFields\" type=\"textarea\" placeholder=\"请输入专业领域\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"重要成就\" prop=\"notableAchievements\">\r\n              <el-input v-model=\"form.notableAchievements\" type=\"textarea\" placeholder=\"请输入重要成就\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"联系信息\" prop=\"contactInfo\">\r\n              <el-input v-model=\"form.contactInfo\" type=\"textarea\" placeholder=\"请输入联系信息\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"详细描述\" prop=\"detailedDescription\">\r\n              <editor v-model=\"form.detailedDescription\" :min-height=\"300\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio :label=\"1\">启用</el-radio>\r\n                <el-radio :label=\"0\">禁用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listExpert, getExpert, delExpert, addExpert, updateExpert } from \"@/api/miniapp/expert\";\r\n\r\nexport default {\r\n  name: \"Expert\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 专家矩阵表格数据\r\n      expertList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        expertName: null,\r\n        expertTitle: null,\r\n        expertCompany: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        expertName: [\r\n          { required: true, message: \"专家姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询专家矩阵列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listExpert(this.queryParams).then(response => {\r\n        this.expertList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        expertName: null,\r\n        expertTitle: null,\r\n        expertCompany: null,\r\n        expertIntro: null,\r\n        avatarUrl: null,\r\n        expertiseFields: null,\r\n        yearsExperience: 0,\r\n        notableAchievements: null,\r\n        contactInfo: null,\r\n        detailedDescription: null,\r\n        sortOrder: 0,\r\n        status: 1\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加专家矩阵\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getExpert(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改专家矩阵\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateExpert(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addExpert(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除专家矩阵编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delExpert(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/expert/export', {\r\n        ...this.queryParams\r\n      }, `专家矩阵_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script> 
+Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
+<+>UTF-8
+===================================================================
+diff --git a/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue b/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue
+--- a/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue	(revision 0c3db6771e21fbfc3b63e70e0172f28c743fd34b)
++++ b/ruoyi-ui/src/views/miniapp/haitang/expert/index.vue	(date 1752027710702)
+@@ -204,8 +204,8 @@
+           <el-col :span="12">
+             <el-form-item label="状态" prop="status">
+               <el-radio-group v-model="form.status">
+-                <el-radio :label="1">启用</el-radio>
+-                <el-radio :label="0">禁用</el-radio>
++                <el-radio :label="0">启用</el-radio>
++                <el-radio :label="1">禁用</el-radio>
+               </el-radio-group>
+             </el-form-item>
+           </el-col>
+@@ -300,7 +300,7 @@
+         contactInfo: null,
+         detailedDescription: null,
+         sortOrder: 0,
+-        status: 1
++        status: 0
+       };
+       this.resetForm("form");
+     },
