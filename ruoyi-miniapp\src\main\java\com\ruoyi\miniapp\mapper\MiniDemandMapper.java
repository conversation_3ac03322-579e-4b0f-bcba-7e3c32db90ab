package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniDemand;

/**
 * 需求信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniDemandMapper 
{
    /**
     * 查询需求信息
     * 
     * @param demandId 需求信息主键
     * @return 需求信息
     */
    public MiniDemand selectMiniDemandByDemandId(Long demandId);

    /**
     * 查询需求信息列表
     * 
     * @param miniDemand 需求信息
     * @return 需求信息集合
     */
    public List<MiniDemand> selectMiniDemandList(MiniDemand miniDemand);

    /**
     * 新增需求信息
     * 
     * @param miniDemand 需求信息
     * @return 结果
     */
    public int insertMiniDemand(MiniDemand miniDemand);

    /**
     * 修改需求信息
     * 
     * @param miniDemand 需求信息
     * @return 结果
     */
    public int updateMiniDemand(MiniDemand miniDemand);

    /**
     * 删除需求信息
     * 
     * @param demandId 需求信息主键
     * @return 结果
     */
    public int deleteMiniDemandByDemandId(Long demandId);

    /**
     * 批量删除需求信息
     * 
     * @param demandIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniDemandByDemandIds(Long[] demandIds);

    /**
     * 查询分类下的需求信息列表（小程序端调用）
     * 
     * @param categoryId 分类ID
     * @return 需求信息集合
     */
    public List<MiniDemand> selectMiniDemandListByCategoryId(Long categoryId);

    /**
     * 根据分类ID查询需求信息列表（复数形式）
     * 
     * @param categoryId 分类ID
     * @return 需求信息集合
     */
    public List<MiniDemand> selectMiniDemandsByCategoryId(Long categoryId);

    /**
     * 查询推荐的需求信息列表
     * 
     * @return 需求信息集合
     */
    public List<MiniDemand> selectRecommendedMiniDemandList();

    /**
     * 查询启用的需求信息列表
     * 
     * @return 需求信息集合
     */
    public List<MiniDemand> selectEnabledMiniDemandList();

    /**
     * 增加浏览次数
     * 
     * @param demandId 需求信息主键
     * @return 结果
     */
    public int increaseViewCount(Long demandId);

    /**
     * 查询用户发布的需求信息列表
     * 
     * @param userId 用户ID
     * @return 需求信息集合
     */
    public List<MiniDemand> selectMiniDemandListByUserId(Long userId);
} 