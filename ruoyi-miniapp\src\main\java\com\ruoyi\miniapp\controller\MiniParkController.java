package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniPark;
import com.ruoyi.miniapp.service.IMiniParkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 园区管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "园区管理")
@RestController
@RequestMapping("/miniapp/park")
public class MiniParkController extends BaseController
{
    @Autowired
    private IMiniParkService miniParkService;

    /**
     * 查询园区管理列表
     */
    @ApiOperation("查询园区管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:park:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") MiniPark miniPark)
    {
        startPage();
        List<MiniPark> list = miniParkService.selectMiniParkList(miniPark);
        return getDataTable(list);
    }

    /**
     * 导出园区管理列表
     */
    @ApiOperation("导出园区管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:park:export')")
    @Log(title = "园区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") MiniPark miniPark)
    {
        List<MiniPark> list = miniParkService.selectMiniParkList(miniPark);
        ExcelUtil<MiniPark> util = new ExcelUtil<MiniPark>(MiniPark.class);
        util.exportExcel(response, list, "园区管理数据");
    }

    /**
     * 获取园区管理详细信息
     */
    @ApiOperation("获取园区管理详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:park:query')")
    @GetMapping(value = "/{parkId}")
    public AjaxResult getInfo(@ApiParam("园区ID") @PathVariable("parkId") Long parkId)
    {
        return success(miniParkService.selectMiniParkByParkId(parkId));
    }

    /**
     * 新增园区管理
     */
    @ApiOperation("新增园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:add')")
    @Log(title = "园区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("园区信息") @RequestBody MiniPark miniPark)
    {
        return toAjax(miniParkService.insertMiniPark(miniPark));
    }

    /**
     * 修改园区管理
     */
    @ApiOperation("修改园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:edit')")
    @Log(title = "园区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("园区信息") @RequestBody MiniPark miniPark)
    {
        return toAjax(miniParkService.updateMiniPark(miniPark));
    }

    /**
     * 删除园区管理
     */
    @ApiOperation("删除园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:remove')")
    @Log(title = "园区管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{parkIds}")
    public AjaxResult remove(@ApiParam("园区ID数组") @PathVariable Long[] parkIds)
    {
        return toAjax(miniParkService.deleteMiniParkByParkIds(parkIds));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的园区列表（小程序端）
     */
    @ApiOperation("获取启用的园区列表")
    @GetMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniPark> list = miniParkService.selectEnabledMiniParkList();
        return success(list);
    }

    /**
     * 获取推荐的园区列表（小程序端）
     */
    @ApiOperation("获取推荐的园区列表")
    @GetMapping("/app/getRecommendedList")
    public AjaxResult getRecommendedList()
    {
        List<MiniPark> list = miniParkService.selectRecommendedMiniParkList();
        return success(list);
    }

    /**
     * 获取园区详情（小程序端）
     */
    @ApiOperation("获取园区详情")
    @GetMapping("/app/getDetail/{parkId}")
    public AjaxResult getDetail(@ApiParam("园区ID") @PathVariable("parkId") Long parkId)
    {
        MiniPark park = miniParkService.selectMiniParkByParkId(parkId);
        if (park == null || !"0".equals(park.getStatus())) {
            return error("园区不存在或已停用");
        }
        return success(park);
    }
}
