package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 活动报名对象 mini_event
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long eventId;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String title;

    /** 活动封面图 */
    @Excel(name = "活动封面图")
    private String coverImage;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String description;

    /** 活动地点 */
    @Excel(name = "活动地点")
    private String location;

    /** 活动开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationDeadline;

    /** 报名表单字段配置（JSON格式） */
    @Excel(name = "报名表单字段配置")
    private String formFields;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 活动类型（activity=活动，guidance=赛前辅导） */
    @Excel(name = "活动类型", readConverterExp = "activity=活动,guidance=赛前辅导")
    private String eventType;

    public void setEventId(Long eventId)
    {
        this.eventId = eventId;
    }

    public Long getEventId() 
    {
        return eventId;
    }
    
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    
    public void setCoverImage(String coverImage) 
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage() 
    {
        return coverImage;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    
    public void setRegistrationDeadline(Date registrationDeadline) 
    {
        this.registrationDeadline = registrationDeadline;
    }

    public Date getRegistrationDeadline() 
    {
        return registrationDeadline;
    }
    
    public void setFormFields(String formFields) 
    {
        this.formFields = formFields;
    }

    public String getFormFields() 
    {
        return formFields;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setEventType(String eventType)
    {
        this.eventType = eventType;
    }

    public String getEventType()
    {
        return eventType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("eventId", getEventId())
            .append("title", getTitle())
            .append("coverImage", getCoverImage())
            .append("description", getDescription())
            .append("location", getLocation())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("registrationDeadline", getRegistrationDeadline())
            .append("formFields", getFormFields())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("eventType", getEventType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 