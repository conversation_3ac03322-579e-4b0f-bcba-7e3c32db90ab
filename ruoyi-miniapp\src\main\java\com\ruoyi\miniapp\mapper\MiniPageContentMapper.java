package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniPageContent;

/**
 * 页面内容管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniPageContentMapper 
{
    /**
     * 查询页面内容管理
     * 
     * @param contentId 页面内容管理主键
     * @return 页面内容管理
     */
    public MiniPageContent selectMiniPageContentByContentId(Long contentId);

    /**
     * 查询页面内容管理列表
     * 
     * @param miniPageContent 页面内容管理
     * @return 页面内容管理集合
     */
    public List<MiniPageContent> selectMiniPageContentList(MiniPageContent miniPageContent);

    /**
     * 新增页面内容管理
     * 
     * @param miniPageContent 页面内容管理
     * @return 结果
     */
    public int insertMiniPageContent(MiniPageContent miniPageContent);

    /**
     * 修改页面内容管理
     * 
     * @param miniPageContent 页面内容管理
     * @return 结果
     */
    public int updateMiniPageContent(MiniPageContent miniPageContent);

    /**
     * 删除页面内容管理
     * 
     * @param contentId 页面内容管理主键
     * @return 结果
     */
    public int deleteMiniPageContentByContentId(Long contentId);

    /**
     * 批量删除页面内容管理
     * 
     * @param contentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniPageContentByContentIds(Long[] contentIds);

    /**
     * 根据页面编码查询页面内容
     * 
     * @param pageCode 页面编码
     * @return 页面内容管理
     */
    public MiniPageContent selectMiniPageContentByPageCode(String pageCode);

    /**
     * 根据页面键查询页面内容
     * 
     * @param pageKey 页面键
     * @return 页面内容管理
     */
    public MiniPageContent selectMiniPageContentByPageKey(String pageKey);

    /**
     * 查询启用的页面内容列表
     * 
     * @return 页面内容管理集合
     */
    public List<MiniPageContent> selectEnabledMiniPageContentList();
} 