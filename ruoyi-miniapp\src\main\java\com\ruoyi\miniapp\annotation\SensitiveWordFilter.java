package com.ruoyi.miniapp.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 敏感词过滤注解
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveWordFilter {
    
    /**
     * 模块名称
     */
    String moduleName() default "";
    
    /**
     * 处理策略
     */
    FilterStrategy strategy() default FilterStrategy.REPLACE;
    
    /**
     * 替换字符（当策略为REPLACE时使用）
     */
    char replacement() default '*';
    
    /**
     * 是否忽略错误（当过滤失败时是否继续执行）
     */
    boolean ignoreError() default true;
    
    /**
     * 过滤策略枚举
     */
    enum FilterStrategy {
        /**
         * 替换敏感词
         */
        REPLACE,
        
        /**
         * 拒绝包含敏感词的操作
         */
        REJECT,
        
        /**
         * 仅记录日志，不修改内容
         */
        LOG_ONLY
    }
}
