package com.ruoyi.miniapp.utils;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.support.allow.WordAllows;
import com.github.houbb.sensitive.word.support.deny.WordDenys;
import com.github.houbb.sensitive.word.api.IWordDeny;
import com.github.houbb.sensitive.word.api.IWordAllow;
import com.ruoyi.miniapp.domain.SensitiveWord;
import com.ruoyi.miniapp.mapper.SensitiveWordMapper;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 敏感词工具类
 * 集成数据库动态加载敏感词
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Component
public class SensitiveWordUtil {
    
    private static final Logger log = LoggerFactory.getLogger(SensitiveWordUtil.class);
    
    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;
    
    private SensitiveWordBs sensitiveWordBs;
    
    /**
     * 初始化敏感词引擎
     */
    @PostConstruct
    public void init() {
        try {
            // 创建自定义敏感词数据源
            DatabaseWordDeny databaseWordDeny = new DatabaseWordDeny();
            DatabaseWordAllow databaseWordAllow = new DatabaseWordAllow();
            
            // 初始化敏感词引擎
            sensitiveWordBs = SensitiveWordBs.newInstance()
                    .wordDeny(WordDenys.chains(WordDenys.defaults(), databaseWordDeny))
                    .wordAllow(WordAllows.chains(WordAllows.defaults(), databaseWordAllow))
                    .ignoreCase(true)
                    .ignoreWidth(true)
                    .ignoreNumStyle(true)
                    .ignoreChineseStyle(true)
                    .ignoreEnglishStyle(true)
                    .ignoreRepeat(true)
//                    .enableNumCheck(true)
//                    .enableEmailCheck(true)
//                    .enableUrlCheck(true)
                    .init();

            log.info("敏感词引擎初始化完成");
        } catch (Exception e) {
            log.error("敏感词引擎初始化失败", e);
        }
    }
    
    /**
     * 检测文本是否包含敏感词
     * 
     * @param text 待检测文本
     * @return 是否包含敏感词
     */
    public boolean contains(String text) {
        if (StringUtils.isEmpty(text) || sensitiveWordBs == null) {
            return false;
        }
        return sensitiveWordBs.contains(text);
    }
    
    /**
     * 查找文本中的第一个敏感词
     * 
     * @param text 待检测文本
     * @return 第一个敏感词
     */
    public String findFirst(String text) {
        if (StringUtils.isEmpty(text) || sensitiveWordBs == null) {
            return null;
        }
        return sensitiveWordBs.findFirst(text);
    }
    
    /**
     * 查找文本中的所有敏感词
     * 
     * @param text 待检测文本
     * @return 敏感词列表
     */
    public List<String> findAll(String text) {
        if (StringUtils.isEmpty(text) || sensitiveWordBs == null) {
            return null;
        }
        return sensitiveWordBs.findAll(text);
    }
    
    /**
     * 替换文本中的敏感词（使用*替换）
     * 
     * @param text 待处理文本
     * @return 处理后的文本
     */
    public String replace(String text) {
        if (StringUtils.isEmpty(text) || sensitiveWordBs == null) {
            return text;
        }
        return sensitiveWordBs.replace(text);
    }
    
    /**
     * 替换文本中的敏感词（指定替换字符）
     *
     * @param text 待处理文本
     * @param replacement 替换字符
     * @return 处理后的文本
     */
    public String replace(String text, char replacement) {
        if (StringUtils.isEmpty(text) || sensitiveWordBs == null) {
            return text;
        }
        // 新版本API可能不支持自定义替换字符，使用默认替换然后手动替换
        String replaced = sensitiveWordBs.replace(text);
        if (replacement != '*') {
            replaced = replaced.replace('*', replacement);
        }
        return replaced;
    }
    
    /**
     * 刷新敏感词缓存
     * 重新从数据库加载敏感词并重建内存索引
     */
    public void refresh() {
        try {
            log.info("开始刷新敏感词缓存...");

            // 重新初始化敏感词引擎
            init();

            log.info("敏感词缓存刷新完成");
        } catch (Exception e) {
            log.error("敏感词缓存刷新失败", e);
        }
    }

    /**
     * 动态添加敏感词到缓存
     *
     * @param word 敏感词
     */
    public void addWord(String word) {
        try {
            if (sensitiveWordBs != null && StringUtils.isNotEmpty(word)) {
                sensitiveWordBs.addWord(word);
                log.info("动态添加敏感词到缓存: {}", word);
            }
        } catch (Exception e) {
            log.error("动态添加敏感词失败: {}", word, e);
        }
    }

    /**
     * 动态从缓存中移除敏感词
     *
     * @param word 敏感词
     */
    public void removeWord(String word) {
        try {
            if (sensitiveWordBs != null && StringUtils.isNotEmpty(word)) {
                sensitiveWordBs.removeWord(word);
                log.info("动态移除敏感词: {}", word);
            }
        } catch (Exception e) {
            log.error("动态移除敏感词失败: {}", word, e);
        }
    }

    /**
     * 获取数据库中的敏感词列表
     *
     * @return 敏感词列表
     */
    public List<String> getDatabaseSensitiveWords() {
        try {
            if (sensitiveWordMapper != null) {
                List<SensitiveWord> sensitiveWords = sensitiveWordMapper.selectSensitiveWordByType("1");
                return sensitiveWords.stream()
                        .map(SensitiveWord::getWordContent)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取数据库敏感词失败", e);
        }
        return new java.util.ArrayList<>();
    }

    /**
     * 获取数据库中的白名单列表
     *
     * @return 白名单列表
     */
    public List<String> getDatabaseAllowWords() {
        try {
            if (sensitiveWordMapper != null) {
                List<SensitiveWord> allowWords = sensitiveWordMapper.selectSensitiveWordByType("2");
                return allowWords.stream()
                        .map(SensitiveWord::getWordContent)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取数据库白名单失败", e);
        }
        return new java.util.ArrayList<>();
    }
    
    /**
     * 数据库敏感词数据源
     */
    private class DatabaseWordDeny implements IWordDeny {
        @Override
        public List<String> deny() {
            try {
                List<SensitiveWord> sensitiveWords = sensitiveWordMapper.selectSensitiveWordByType("1");
                return sensitiveWords.stream()
                        .map(SensitiveWord::getWordContent)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("获取数据库敏感词失败", e);
                return null;
            }
        }
    }
    
    /**
     * 数据库白名单数据源
     */
    private class DatabaseWordAllow implements IWordAllow {
        @Override
        public List<String> allow() {
            try {
                List<SensitiveWord> allowWords = sensitiveWordMapper.selectSensitiveWordByType("2");
                return allowWords.stream()
                        .map(SensitiveWord::getWordContent)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("获取数据库白名单失败", e);
                return null;
            }
        }
    }
}
