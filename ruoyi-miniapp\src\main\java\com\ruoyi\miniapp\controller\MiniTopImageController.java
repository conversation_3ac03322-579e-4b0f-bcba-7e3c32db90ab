package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniTopImage;
import com.ruoyi.miniapp.service.IMiniTopImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * top图片Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "top图片管理")
@RestController
@RequestMapping("/miniapp/topimage")
public class MiniTopImageController extends BaseController
{
    @Autowired
    private IMiniTopImageService miniTopImageService;

    /**
     * 查询top图片列表
     */
    @ApiOperation("查询top图片列表")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniTopImage miniTopImage)
    {
        startPage();
        List<MiniTopImage> list = miniTopImageService.selectMiniTopImageList(miniTopImage);
        return getDataTable(list);
    }

    /**
     * 导出top图片列表
     */
    @ApiOperation("导出top图片列表")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:export')")
    @Log(title = "top图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniTopImage miniTopImage)
    {
        List<MiniTopImage> list = miniTopImageService.selectMiniTopImageList(miniTopImage);
        ExcelUtil<MiniTopImage> util = new ExcelUtil<MiniTopImage>(MiniTopImage.class);
        util.exportExcel(response, list, "top图片数据");
    }

    /**
     * 获取top图片详细信息
     */
    @ApiOperation("获取top图片详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("top图片ID") @RequestBody Long topImageId)
    {
        return AjaxResult.success(miniTopImageService.selectMiniTopImageByTopImageId(topImageId));
    }

    /**
     * 新增top图片
     */
    @ApiOperation("新增top图片")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:add')")
    @Log(title = "top图片", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("top图片信息") @RequestBody MiniTopImage miniTopImage)
    {
        return toAjax(miniTopImageService.insertMiniTopImage(miniTopImage));
    }

    /**
     * 修改top图片
     */
    @ApiOperation("修改top图片")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:edit')")
    @Log(title = "top图片", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("top图片信息") @RequestBody MiniTopImage miniTopImage)
    {
        return toAjax(miniTopImageService.updateMiniTopImage(miniTopImage));
    }

    /**
     * 删除top图片
     */
    @ApiOperation("删除top图片")
    @PreAuthorize("@ss.hasPermi('miniapp:topimage:remove')")
    @Log(title = "top图片", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("top图片ID数组") @RequestBody Long[] topImageIds)
    {
        return toAjax(miniTopImageService.deleteMiniTopImageByTopImageIds(topImageIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的top图片列表
     */
    @ApiOperation("获取启用的top图片列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniTopImage> list = miniTopImageService.selectEnabledMiniTopImageList();
        return AjaxResult.success(list);
    }

    /**
     * 根据页面标识获取启用的top图片列表
     */
    @ApiOperation("根据页面标识获取启用的top图片列表")
    @PostMapping("/app/getEnabledListByPage")
    public AjaxResult getEnabledListByPage(@ApiParam("页面标识") @RequestBody String pageCode)
    {
        List<MiniTopImage> list = miniTopImageService.selectEnabledMiniTopImageListByPage(pageCode);
        return AjaxResult.success(list);
    }
} 