package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniNotice;

/**
 * 滚动通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniNoticeMapper 
{
    /**
     * 查询滚动通知
     * 
     * @param noticeId 滚动通知主键
     * @return 滚动通知
     */
    public MiniNotice selectMiniNoticeByNoticeId(Long noticeId);

    /**
     * 查询滚动通知列表
     * 
     * @param miniNotice 滚动通知
     * @return 滚动通知集合
     */
    public List<MiniNotice> selectMiniNoticeList(MiniNotice miniNotice);

    /**
     * 新增滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    public int insertMiniNotice(MiniNotice miniNotice);

    /**
     * 修改滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    public int updateMiniNotice(MiniNotice miniNotice);

    /**
     * 删除滚动通知
     * 
     * @param noticeId 滚动通知主键
     * @return 结果
     */
    public int deleteMiniNoticeByNoticeId(Long noticeId);

    /**
     * 批量删除滚动通知
     * 
     * @param noticeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 查询启用的滚动通知列表（小程序端调用）
     * 
     * @return 滚动通知集合
     */
    public List<MiniNotice> selectEnabledMiniNoticeList();
} 