package com.ruoyi.miniapp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 专家矩阵对象 mini_expert_matrix
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ExpertMatrix extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 专家ID */
    private Long id;

    /** 专家姓名 */
    @Excel(name = "专家姓名")
    private String expertName;

    /** 专家职位 */
    @Excel(name = "专家职位")
    private String expertTitle;

    /** 专家公司 */
    @Excel(name = "专家公司")
    private String expertCompany;

    /** 专家介绍 */
    @Excel(name = "专家介绍")
    private String expertIntro;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatarUrl;

    /** 专业领域 */
    @Excel(name = "专业领域")
    private String expertiseFields;

    /** 从业年限 */
    @Excel(name = "从业年限")
    private Long yearsExperience;

    /** 重要成就 */
    @Excel(name = "重要成就")
    private String notableAchievements;

    /** 联系信息 */
    @Excel(name = "联系信息")
    private String contactInfo;

    /** 专家详细描述(富文本) */
    @Excel(name = "专家详细描述")
    private String detailedDescription;

    /** 排序 */
    @Excel(name = "排序")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setExpertName(String expertName) 
    {
        this.expertName = expertName;
    }

    public String getExpertName() 
    {
        return expertName;
    }
    public void setExpertTitle(String expertTitle) 
    {
        this.expertTitle = expertTitle;
    }

    public String getExpertTitle() 
    {
        return expertTitle;
    }
    public void setExpertCompany(String expertCompany) 
    {
        this.expertCompany = expertCompany;
    }

    public String getExpertCompany() 
    {
        return expertCompany;
    }
    public void setExpertIntro(String expertIntro) 
    {
        this.expertIntro = expertIntro;
    }

    public String getExpertIntro() 
    {
        return expertIntro;
    }
    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }
    public void setExpertiseFields(String expertiseFields) 
    {
        this.expertiseFields = expertiseFields;
    }

    public String getExpertiseFields() 
    {
        return expertiseFields;
    }
    public void setYearsExperience(Long yearsExperience) 
    {
        this.yearsExperience = yearsExperience;
    }

    public Long getYearsExperience() 
    {
        return yearsExperience;
    }
    public void setNotableAchievements(String notableAchievements) 
    {
        this.notableAchievements = notableAchievements;
    }

    public String getNotableAchievements() 
    {
        return notableAchievements;
    }
    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }
    public void setDetailedDescription(String detailedDescription) 
    {
        this.detailedDescription = detailedDescription;
    }

    public String getDetailedDescription() 
    {
        return detailedDescription;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("expertName", getExpertName())
            .append("expertTitle", getExpertTitle())
            .append("expertCompany", getExpertCompany())
            .append("expertIntro", getExpertIntro())
            .append("avatarUrl", getAvatarUrl())
            .append("expertiseFields", getExpertiseFields())
            .append("yearsExperience", getYearsExperience())
            .append("notableAchievements", getNotableAchievements())
            .append("contactInfo", getContactInfo())
            .append("detailedDescription", getDetailedDescription())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 