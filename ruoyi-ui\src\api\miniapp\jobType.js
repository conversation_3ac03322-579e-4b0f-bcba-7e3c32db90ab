import request from '@/utils/request'

// 查询职位类型列表
export function getJobTypeList(query) {
  return request({
    url: '/miniapp/jobType/list',
    method: 'post',
    data: query
  })
}

// 查询职位类型详细
export function getJobType(jobTypeId) {
  return request({
    url: '/miniapp/jobType/getInfo',
    method: 'post',
    data: jobTypeId
  })
}

// 新增职位类型
export function addJobType(data) {
  return request({
    url: '/miniapp/jobType/add',
    method: 'post',
    data: data
  })
}

// 修改职位类型
export function updateJobType(data) {
  return request({
    url: '/miniapp/jobType/edit',
    method: 'post',
    data: data
  })
}

// 删除职位类型
export function delJobType(jobTypeIds) {
  return request({
    url: '/miniapp/jobType/remove',
    method: 'post',
    data: jobTypeIds
  })
}

// 获取启用的职位类型列表
export function getEnabledJobTypeList() {
  return request({
    url: '/miniapp/jobType/app/getEnabledList',
    method: 'post'
  })
} 