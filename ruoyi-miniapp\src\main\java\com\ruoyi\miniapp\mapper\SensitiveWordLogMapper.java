package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.SensitiveWordLog;

/**
 * 敏感词检测日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface SensitiveWordLogMapper 
{
    /**
     * 查询敏感词检测日志
     * 
     * @param logId 敏感词检测日志主键
     * @return 敏感词检测日志
     */
    public SensitiveWordLog selectSensitiveWordLogByLogId(Long logId);

    /**
     * 查询敏感词检测日志列表
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 敏感词检测日志集合
     */
    public List<SensitiveWordLog> selectSensitiveWordLogList(SensitiveWordLog sensitiveWordLog);

    /**
     * 根据用户ID查询敏感词检测日志
     * 
     * @param userId 用户ID
     * @return 敏感词检测日志集合
     */
    public List<SensitiveWordLog> selectSensitiveWordLogByUserId(Long userId);

    /**
     * 根据模块名称查询敏感词检测日志
     * 
     * @param moduleName 模块名称
     * @return 敏感词检测日志集合
     */
    public List<SensitiveWordLog> selectSensitiveWordLogByModuleName(String moduleName);

    /**
     * 新增敏感词检测日志
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 结果
     */
    public int insertSensitiveWordLog(SensitiveWordLog sensitiveWordLog);

    /**
     * 修改敏感词检测日志
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 结果
     */
    public int updateSensitiveWordLog(SensitiveWordLog sensitiveWordLog);

    /**
     * 删除敏感词检测日志
     * 
     * @param logId 敏感词检测日志主键
     * @return 结果
     */
    public int deleteSensitiveWordLogByLogId(Long logId);

    /**
     * 批量删除敏感词检测日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSensitiveWordLogByLogIds(Long[] logIds);

    /**
     * 清理指定天数前的日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    public int cleanSensitiveWordLogByDays(int days);
}
