package com.ruoyi.miniapp.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.miniapp.domain.MiniIndustryTree;

/**
 * 产业树状结构Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface MiniIndustryTreeMapper 
{
    /**
     * 查询产业树状结构
     * 
     * @param id 产业树状结构主键
     * @return 产业树状结构
     */
    public MiniIndustryTree selectMiniIndustryTreeById(Long id);

    /**
     * 查询产业树状结构列表
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 产业树状结构集合
     */
    public List<MiniIndustryTree> selectMiniIndustryTreeList(MiniIndustryTree miniIndustryTree);

    /**
     * 查询根节点列表（产业类型）
     * 
     * @return 产业类型列表
     */
    public List<MiniIndustryTree> selectIndustryTypeList();

    /**
     * 根据父节点ID查询子节点列表
     * 
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    public List<MiniIndustryTree> selectChildrenByParentId(Long parentId);

    /**
     * 根据节点类型和父节点ID查询节点列表
     * 
     * @param nodeType 节点类型
     * @param parentId 父节点ID
     * @return 节点列表
     */
    public List<MiniIndustryTree> selectByNodeTypeAndParentId(@Param("nodeType") String nodeType, @Param("parentId") Long parentId);

    /**
     * 新增产业树状结构
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    public int insertMiniIndustryTree(MiniIndustryTree miniIndustryTree);

    /**
     * 修改产业树状结构
     *
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    public int updateMiniIndustryTree(MiniIndustryTree miniIndustryTree);

    /**
     * 级联更新子节点状态
     *
     * @param parentId 父节点ID
     * @param status 状态
     * @return 结果
     */
    public int updateChildrenStatus(@Param("parentId") Long parentId, @Param("status") String status);

    /**
     * 删除产业树状结构
     * 
     * @param id 产业树状结构主键
     * @return 结果
     */
    public int deleteMiniIndustryTreeById(Long id);

    /**
     * 批量删除产业树状结构（逻辑删除）
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniIndustryTreeByIds(Long[] ids);

    /**
     * 级联删除产业树节点及其子节点（逻辑删除）
     *
     * @param id 节点主键
     * @return 结果
     */
    public int deleteMiniIndustryTreeCascade(Long id);

    /**
     * 检查节点是否被企业使用
     *
     * @param id 节点主键
     * @return 使用数量
     */
    public int checkNodeUsedByEnterprise(Long id);

    /**
     * 检查节点的子节点是否被企业使用
     *
     * @param id 节点主键
     * @return 使用数量
     */
    public int checkChildNodesUsedByEnterprise(Long id);
} 