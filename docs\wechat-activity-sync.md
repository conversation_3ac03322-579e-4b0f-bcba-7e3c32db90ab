# 微信公众号活动同步功能

## 功能概述

本功能允许从多个微信公众号自动同步活动文章到精彩活动模块，支持配置管理、自动同步和手动同步。

## 主要特性

1. **多公众号支持**: 可以配置多个微信公众号，每个公众号独立管理
2. **配置管理**: 支持新增、修改、删除、测试公众号配置
3. **分页同步**: 自动分页获取所有已发布的文章，不遗漏任何内容
4. **去重机制**: 基于微信文章ID防止重复同步
5. **来源标识**: 每个活动都标记来源公众号
6. **进度监控**: 实时显示同步进度和结果统计
7. **配置化控制**: 支持通过配置控制同步行为

## 数据库变更

### mini_activity表新增字段

```sql
-- 微信文章ID（用于去重）
ALTER TABLE `mini_activity` ADD COLUMN `wechat_article_id` varchar(100) NULL COMMENT '微信文章ID';

-- 公众号来源（标识来源）
ALTER TABLE `mini_activity` ADD COLUMN `wechat_source` varchar(100) NULL COMMENT '公众号来源';

-- 添加索引
ALTER TABLE `mini_activity` ADD INDEX `idx_wechat_article_id`(`wechat_article_id`);
ALTER TABLE `mini_activity` ADD INDEX `idx_wechat_source`(`wechat_source`);
```

### sys_config表新增配置

```sql
-- 微信公众号配置（JSON格式）
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `remark`)
VALUES
('微信公众号活动配置', 'wechat.activity.configs', '[]', 'N', 'admin', NOW(), '微信公众号活动同步配置，JSON格式存储多个公众号配置'),
('微信同步最大页数', 'wechat.sync.max_pages', '50', 'N', 'admin', NOW(), '微信文章同步时的最大页数限制，每页20条，防止同步过多文章'),
('微信同步请求间隔', 'wechat.sync.request_delay', '100', 'N', 'admin', NOW(), '微信API请求间隔时间（毫秒），避免请求过于频繁'),
('微信同步启用状态', 'wechat.sync.enabled', 'true', 'N', 'admin', NOW(), '是否启用微信文章自动同步功能');
```

## 使用方法

### 1. 配置微信公众号

1. 进入"精彩活动"页面
2. 点击"公众号配置"按钮
3. 点击"新增配置"
4. 填写配置信息：
   - 配置名称：自定义名称，用于标识
   - AppID：微信公众号的AppID
   - AppSecret：微信公众号的AppSecret
   - 是否启用：控制是否参与同步
5. 点击"测试"按钮验证配置是否正确
6. 保存配置

### 2. 同步活动

#### 全量同步
- 点击"同步活动"按钮，从所有启用的公众号同步活动

#### 单个同步
- 在配置列表中，点击对应配置的"同步"按钮

### 3. 查看同步结果

- 同步完成后，活动列表会显示新同步的活动
- "公众号来源"列显示活动来源
- 可以通过公众号来源进行筛选

## 技术实现

### 后端接口

1. **配置管理接口**
   - `GET /miniapp/activity/wechat/getConfigs` - 获取配置列表
   - `POST /miniapp/activity/wechat/saveConfig` - 保存配置
   - `POST /miniapp/activity/wechat/deleteConfig` - 删除配置
   - `POST /miniapp/activity/wechat/testConfig` - 测试配置

2. **同步接口**
   - `POST /miniapp/activity/wechat/syncActivities` - 全量同步
   - `POST /miniapp/activity/wechat/syncFromSingle` - 单个同步

### 微信API调用

1. **获取AccessToken**
   ```
   GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
   ```

2. **获取已发布文章列表（分页）**
   ```
   POST https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token=ACCESS_TOKEN

   请求体：
   {
     "offset": 0,     // 偏移量，从0开始
     "count": 20,     // 每页数量，最大20
     "no_content": 0  // 是否返回内容，0=返回，1=不返回
   }
   ```

### 分页同步机制

系统会自动分页获取所有文章：
- 每页最多20篇文章（微信API限制）
- 自动循环获取直到所有文章都获取完毕
- 默认最多获取50页（1000篇文章），可通过配置调整
- 每次请求间隔100毫秒，避免频率限制
- 实时显示同步进度和统计信息

### 数据映射

微信文章字段 → 活动字段：
- `title` → `title` (活动标题)
- `digest` → `description` (活动描述)
- `thumb_url` → `cover_image` (封面图片)
- `url` → `article_url` (文章链接)
- `article_id` → `wechat_article_id` (微信文章ID)
- `update_time` → `create_time` (创建时间，使用文章更新时间)
- 配置名称 → `wechat_source` (公众号来源)

### 排序逻辑

系统确保最新的文章显示在前面：

1. **获取阶段**: 从微信API获取文章后，按`update_time`降序排序
2. **同步阶段**: 将微信文章的`update_time`设置为活动的`create_time`
3. **显示阶段**: 数据库查询按`sort_order asc, create_time desc`排序

**排序原理**：
- 微信API返回的文章按`update_time`降序排序
- 同步时将`update_time`转换为活动的`create_time`
- 数据库查询时主要按`create_time`降序排序
- `sort_order`字段保持原有逻辑，用于手动调整顺序

## 配置管理

系统提供以下配置项来控制同步行为：

| 配置项 | 配置键 | 默认值 | 说明 |
|--------|--------|--------|------|
| 最大页数 | `wechat.sync.max_pages` | 50 | 同步时的最大页数限制，防止同步过多文章 |
| 请求间隔 | `wechat.sync.request_delay` | 100 | API请求间隔时间（毫秒），避免频率限制 |
| 同步开关 | `wechat.sync.enabled` | true | 是否启用微信文章自动同步功能 |

可以在系统管理 → 参数设置中修改这些配置。

## 注意事项

1. **权限要求**: 微信公众号需要有"已发布内容管理"权限
2. **频率限制**: 微信API有调用频率限制，系统已自动添加请求间隔
3. **数据安全**: AppSecret等敏感信息需要妥善保管
4. **去重逻辑**: 基于`wechat_article_id`进行去重，相同文章不会重复同步
5. **状态管理**: 同步的活动默认为启用状态，可手动调整
6. **同步数量**: 默认最多同步1000篇文章，可通过配置调整
7. **网络稳定**: 大量文章同步时需要稳定的网络连接

## 图片显示优化

针对微信公众号图片的防盗链问题，系统提供了多重解决方案：

### 1. Referrer Policy
```html
<img src="微信图片URL" referrerpolicy="no-referrer" />
```

### 2. 后端代理
当直接访问失败时，自动切换到后端代理：
```
GET /miniapp/activity/imageProxy?url=微信图片URL
```

### 3. 降级处理
- 优先尝试直接加载（使用no-referrer策略）
- 失败时自动切换到后端代理
- 代理也失败时显示默认占位图

### 4. 用户体验优化
- 图片加载状态提示
- 点击预览功能
- 平滑的加载动画

## 故障排除

1. **连接测试失败**: 检查AppID和AppSecret是否正确
2. **同步失败**: 检查网络连接和微信API服务状态
3. **权限错误**: 确认公众号具有相应的API权限
4. **数据不显示**: 检查活动状态和排序设置
5. **图片不显示**:
   - 检查图片URL是否有效
   - 确认后端代理接口正常工作
   - 查看浏览器控制台是否有CORS错误

## 扩展功能

未来可以考虑添加：
- 定时自动同步
- 同步日志记录
- 更多筛选和排序选项
- 批量操作功能
