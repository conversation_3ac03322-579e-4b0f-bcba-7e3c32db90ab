package com.ruoyi.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.miniapp.service.IMiniNewsCenterService;

/**
 * 新闻中心定时任务
 *
 * <AUTHOR>
 */
@Component("newsTask")
public class NewsTask
{
    private static final Logger logger = LoggerFactory.getLogger(NewsTask.class);

    @Autowired
    private IMiniNewsCenterService miniNewsCenterService;

    /**
     * 同步微信公众号文章
     */
    public void syncWechatNews()
    {
        logger.info("开始执行定时同步微信公众号文章任务");
        try
        {
            String result = miniNewsCenterService.syncFromWechat();
            logger.info("定时同步微信公众号文章任务完成：{}", result);
        }
        catch (Exception e)
        {
            logger.error("定时同步微信公众号文章任务失败", e);
        }
    }

    /**
     * 同步微信公众号文章（带参数）
     *
     * @param params 参数格式：count,offset（如：20,0）
     */
    public void syncWechatNewsWithParams(String params)
    {
        logger.info("开始执行定时同步微信公众号文章任务，参数：{}", params);
        try
        {
            int count = 20;
            int offset = 0;
            
            if (StringUtils.isNotEmpty(params))
            {
                String[] paramArray = params.split(",");
                if (paramArray.length >= 1)
                {
                    count = Integer.parseInt(paramArray[0].trim());
                }
                if (paramArray.length >= 2)
                {
                    offset = Integer.parseInt(paramArray[1].trim());
                }
            }
            
            String result = miniNewsCenterService.syncFromWechat(count, offset);
            logger.info("定时同步微信公众号文章任务完成：{}", result);
        }
        catch (Exception e)
        {
            logger.error("定时同步微信公众号文章任务失败", e);
        }
    }
} 