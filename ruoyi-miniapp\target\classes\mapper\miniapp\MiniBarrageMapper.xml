<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniBarrageMapper">
    
    <resultMap type="MiniBarrage" id="MiniBarrageResult">
        <result property="barrageId"    column="barrage_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userNickName"    column="user_nick_name"    />
        <result property="userAvatarUrl"    column="user_avatar_url"    />
        <result property="content"    column="content"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMiniBarrageVo">
        select barrage_id, user_id, user_nick_name, user_avatar_url, content, audit_status, audit_by, audit_time, audit_remark, create_time, update_time from mini_barrage
    </sql>

    <select id="selectMiniBarrageList" parameterType="MiniBarrage" resultMap="MiniBarrageResult">
        <include refid="selectMiniBarrageVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userNickName != null  and userNickName != ''"> and user_nick_name like concat('%', #{userNickName}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and audit_by = #{auditBy}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMiniBarrageByBarrageId" parameterType="Long" resultMap="MiniBarrageResult">
        <include refid="selectMiniBarrageVo"/>
        where barrage_id = #{barrageId}
    </select>
    
    <select id="selectApprovedMiniBarrageList" resultMap="MiniBarrageResult">
        <include refid="selectMiniBarrageVo"/>
        where audit_status = '1'
        order by create_time desc
    </select>

    <insert id="insertMiniBarrage" parameterType="MiniBarrage" useGeneratedKeys="true" keyProperty="barrageId">
        insert into mini_barrage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userNickName != null">user_nick_name,</if>
            <if test="userAvatarUrl != null">user_avatar_url,</if>
            <if test="content != null">content,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userNickName != null">#{userNickName},</if>
            <if test="userAvatarUrl != null">#{userAvatarUrl},</if>
            <if test="content != null">#{content},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            sysdate(),
            sysdate()
        </trim>
    </insert>

    <update id="updateMiniBarrage" parameterType="MiniBarrage">
        update mini_barrage
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userNickName != null">user_nick_name = #{userNickName},</if>
            <if test="userAvatarUrl != null">user_avatar_url = #{userAvatarUrl},</if>
            <if test="content != null">content = #{content},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            update_time = sysdate()
        </trim>
        where barrage_id = #{barrageId}
    </update>
    
    <update id="auditBarrage">
        update mini_barrage 
        set audit_status = #{auditStatus}, 
            audit_by = #{auditBy}, 
            audit_time = sysdate(),
            audit_remark = #{auditRemark},
            update_time = sysdate()
        where barrage_id = #{barrageId}
    </update>

    <delete id="deleteMiniBarrageByBarrageId" parameterType="Long">
        delete from mini_barrage where barrage_id = #{barrageId}
    </delete>

    <delete id="deleteMiniBarrageByBarrageIds" parameterType="String">
        delete from mini_barrage where barrage_id in 
        <foreach item="barrageId" collection="array" open="(" separator="," close=")">
            #{barrageId}
        </foreach>
    </delete>

</mapper> 