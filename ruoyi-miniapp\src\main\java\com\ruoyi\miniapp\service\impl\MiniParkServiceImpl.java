package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniParkMapper;
import com.ruoyi.miniapp.domain.MiniPark;
import com.ruoyi.miniapp.service.IMiniParkService;

/**
 * 园区管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class MiniParkServiceImpl implements IMiniParkService 
{
    @Autowired
    private MiniParkMapper miniParkMapper;

    /**
     * 查询园区管理
     * 
     * @param parkId 园区管理主键
     * @return 园区管理
     */
    @Override
    public MiniPark selectMiniParkByParkId(Long parkId)
    {
        return miniParkMapper.selectMiniParkByParkId(parkId);
    }

    /**
     * 查询园区管理列表
     * 
     * @param miniPark 园区管理
     * @return 园区管理
     */
    @Override
    public List<MiniPark> selectMiniParkList(MiniPark miniPark)
    {
        return miniParkMapper.selectMiniParkList(miniPark);
    }

    /**
     * 新增园区管理
     * 
     * @param miniPark 园区管理
     * @return 结果
     */
    @Override
    public int insertMiniPark(MiniPark miniPark)
    {
        miniPark.setCreateTime(DateUtils.getNowDate());
        return miniParkMapper.insertMiniPark(miniPark);
    }

    /**
     * 修改园区管理
     * 
     * @param miniPark 园区管理
     * @return 结果
     */
    @Override
    public int updateMiniPark(MiniPark miniPark)
    {
        miniPark.setUpdateTime(DateUtils.getNowDate());
        return miniParkMapper.updateMiniPark(miniPark);
    }

    /**
     * 批量删除园区管理
     * 
     * @param parkIds 需要删除的园区管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniParkByParkIds(Long[] parkIds)
    {
        return miniParkMapper.deleteMiniParkByParkIds(parkIds);
    }

    /**
     * 删除园区管理信息
     * 
     * @param parkId 园区管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniParkByParkId(Long parkId)
    {
        return miniParkMapper.deleteMiniParkByParkId(parkId);
    }

    /**
     * 查询启用的园区管理列表（小程序端调用）
     * 
     * @return 园区管理集合
     */
    @Override
    public List<MiniPark> selectEnabledMiniParkList()
    {
        return miniParkMapper.selectEnabledMiniParkList();
    }

    /**
     * 查询推荐的园区管理列表
     * 
     * @return 园区管理集合
     */
    @Override
    public List<MiniPark> selectRecommendedMiniParkList()
    {
        return miniParkMapper.selectRecommendedMiniParkList();
    }
}
