package com.ruoyi.miniapp.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.miniapp.service.IMiniNewsCenterService;
import com.ruoyi.system.service.ISysConfigService;

/**
 * 微信新闻自动同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Component
public class WechatNewsSyncTask {
    
    private static final Logger logger = LoggerFactory.getLogger(WechatNewsSyncTask.class);
    
    @Autowired
    private IMiniNewsCenterService miniNewsCenterService;
    
    @Autowired
    private ISysConfigService configService;
    
    // 上次同步时间
    private volatile long lastSyncTime = 0;
    
    /**
     * 自动同步微信新闻
     * 每晚0点执行一次
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每晚0点执行
    public void autoSyncWechatNews() {
        try {
            // 检查是否启用自动同步
            String enableStr = configService.selectConfigByKey("wechat.sync.enable");
            if (StringUtils.isEmpty(enableStr) || !"true".equalsIgnoreCase(enableStr)) {
                logger.info("微信新闻自动同步已禁用，跳过执行");
                return;
            }

            logger.info("=== 开始每日自动同步微信新闻 ===");

            String result = miniNewsCenterService.syncFromWechat();
            lastSyncTime = System.currentTimeMillis();

            logger.info("每日自动同步微信新闻完成: {}", result);

        } catch (Exception e) {
            logger.error("每日自动同步微信新闻失败", e);
        }
    }
    
    /**
     * 手动触发同步（可通过接口调用）
     */
    public String manualSync() {
        try {
            logger.info("=== 手动触发微信新闻同步 ===");
            String result = miniNewsCenterService.syncFromWechat();
            lastSyncTime = System.currentTimeMillis();
            return result;
        } catch (Exception e) {
            logger.error("手动同步微信新闻失败", e);
            throw e;
        }
    }
    
    /**
     * 获取同步状态信息
     */
    public String getSyncStatus() {
        String enableStr = configService.selectConfigByKey("wechat.sync.enable");

        boolean enabled = "true".equalsIgnoreCase(enableStr);

        if (!enabled) {
            return "每日自动同步已禁用";
        }

        if (lastSyncTime == 0) {
            return "每日自动同步已启用，每晚0点执行，尚未执行过同步";
        }

        return "每日自动同步已启用，每晚0点执行，上次同步时间: " + new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(lastSyncTime));
    }
}
