import request from '@/utils/request'

// 查询用户报名记录列表
export function listRegistration(query) {
  // 添加活动类型过滤，只查询活动类型的报名记录
  const params = { ...query, eventType: 'activity' }
  return request({
    url: '/miniapp/registration/list',
    method: 'get',
    params: params
  })
}

// 查询用户报名记录详细
export function getRegistration(registrationId) {
  return request({
    url: '/miniapp/registration/' + registrationId,
    method: 'get'
  })
}

// 新增用户报名记录
export function addRegistration(data) {
  return request({
    url: '/miniapp/registration',
    method: 'post',
    data: data
  })
}

// 修改用户报名记录
export function updateRegistration(data) {
  return request({
    url: '/miniapp/registration',
    method: 'put',
    data: data
  })
}

// 删除用户报名记录
export function delRegistration(registrationIds) {
  return request({
    url: '/miniapp/registration/' + registrationIds,
    method: 'delete'
  })
}

// 根据活动ID查询报名记录列表
export function getRegistrationsByEventId(eventId) {
  return request({
    url: '/miniapp/registration/event/' + eventId,
    method: 'get'
  })
}

// 根据用户ID查询报名记录列表
export function getRegistrationsByUserId(userId) {
  return request({
    url: '/miniapp/registration/user/' + userId,
    method: 'get'
  })
}

// 查询用户是否已报名某活动
export function checkRegistration(eventId, userId) {
  return request({
    url: '/miniapp/registration/check/' + eventId + '/' + userId,
    method: 'get'
  })
}

// 统计活动报名人数
export function countRegistrations(eventId) {
  return request({
    url: '/miniapp/registration/count/' + eventId,
    method: 'get'
  })
} 