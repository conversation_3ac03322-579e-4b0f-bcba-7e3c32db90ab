<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.VideoShowcaseMapper">
    
    <resultMap type="VideoShowcase" id="VideoShowcaseResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectVideoShowcaseVo">
        select id, title, video_url, sort_order, status, created_at, updated_at from mini_video_showcase
    </sql>

    <select id="selectVideoShowcaseList" parameterType="VideoShowcase" resultMap="VideoShowcaseResult">
        <include refid="selectVideoShowcaseVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by sort_order asc, id desc
    </select>
    
    <select id="selectVideoShowcaseById" parameterType="Long" resultMap="VideoShowcaseResult">
        <include refid="selectVideoShowcaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVideoShowcase" parameterType="VideoShowcase" useGeneratedKeys="true" keyProperty="id">
        insert into mini_video_showcase
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="videoUrl != null and videoUrl != ''">video_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="videoUrl != null and videoUrl != ''">#{videoUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateVideoShowcase" parameterType="VideoShowcase">
        update mini_video_showcase
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="videoUrl != null and videoUrl != ''">video_url = #{videoUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVideoShowcaseById" parameterType="Long">
        delete from mini_video_showcase where id = #{id}
    </delete>

    <delete id="deleteVideoShowcaseByIds" parameterType="String">
        delete from mini_video_showcase where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 