package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniDemandCategory;

/**
 * 需求分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniDemandCategoryService 
{
    /**
     * 查询需求分类
     * 
     * @param categoryId 需求分类主键
     * @return 需求分类
     */
    public MiniDemandCategory selectMiniDemandCategoryByCategoryId(Long categoryId);

    /**
     * 查询需求分类列表
     * 
     * @param miniDemandCategory 需求分类
     * @return 需求分类集合
     */
    public List<MiniDemandCategory> selectMiniDemandCategoryList(MiniDemandCategory miniDemandCategory);

    /**
     * 新增需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    public int insertMiniDemandCategory(MiniDemandCategory miniDemandCategory);

    /**
     * 修改需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    public int updateMiniDemandCategory(MiniDemandCategory miniDemandCategory);

    /**
     * 批量删除需求分类
     * 
     * @param categoryIds 需要删除的需求分类主键集合
     * @return 结果
     */
    public int deleteMiniDemandCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除需求分类信息
     * 
     * @param categoryId 需求分类主键
     * @return 结果
     */
    public int deleteMiniDemandCategoryByCategoryId(Long categoryId);

    /**
     * 查询启用的需求分类列表（小程序端调用）
     *
     * @return 需求分类集合
     */
    public List<MiniDemandCategory> selectEnabledMiniDemandCategoryList();

    /**
     * 过滤表单字段配置中的隐藏字段（小程序端调用）
     *
     * @param formFields 原始表单字段配置JSON字符串
     * @return 过滤后的表单字段配置JSON字符串
     */
    public String getFilteredFormFields(String formFields);
}