package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniTechStar;
import com.ruoyi.miniapp.mapper.MiniTechStarMapper;
import com.ruoyi.miniapp.service.IMiniTechStarService;

/**
 * 科技之星Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniTechStarServiceImpl implements IMiniTechStarService 
{
    @Autowired
    private MiniTechStarMapper miniTechStarMapper;

    /**
     * 查询科技之星
     * 
     * @param starId 科技之星主键
     * @return 科技之星
     */
    @Override
    public MiniTechStar selectMiniTechStarByStarId(Long starId)
    {
        return miniTechStarMapper.selectMiniTechStarByStarId(starId);
    }

    /**
     * 查询科技之星列表
     * 
     * @param miniTechStar 科技之星
     * @return 科技之星
     */
    @Override
    public List<MiniTechStar> selectMiniTechStarList(MiniTechStar miniTechStar)
    {
        return miniTechStarMapper.selectMiniTechStarList(miniTechStar);
    }

    /**
     * 新增科技之星
     * 
     * @param miniTechStar 科技之星
     * @return 结果
     */
    @Override
    public int insertMiniTechStar(MiniTechStar miniTechStar)
    {
        miniTechStar.setCreateTime(DateUtils.getNowDate());
        return miniTechStarMapper.insertMiniTechStar(miniTechStar);
    }

    /**
     * 修改科技之星
     * 
     * @param miniTechStar 科技之星
     * @return 结果
     */
    @Override
    public int updateMiniTechStar(MiniTechStar miniTechStar)
    {
        miniTechStar.setUpdateTime(DateUtils.getNowDate());
        return miniTechStarMapper.updateMiniTechStar(miniTechStar);
    }

    /**
     * 批量删除科技之星
     * 
     * @param starIds 需要删除的科技之星主键
     * @return 结果
     */
    @Override
    public int deleteMiniTechStarByStarIds(Long[] starIds)
    {
        return miniTechStarMapper.deleteMiniTechStarByStarIds(starIds);
    }

    /**
     * 删除科技之星信息
     * 
     * @param starId 科技之星主键
     * @return 结果
     */
    @Override
    public int deleteMiniTechStarByStarId(Long starId)
    {
        return miniTechStarMapper.deleteMiniTechStarByStarId(starId);
    }

    /**
     * 查询推荐的科技之星列表（小程序端调用）
     * 
     * @return 科技之星集合
     */
    @Override
    public List<MiniTechStar> selectRecommendedMiniTechStarList()
    {
        return miniTechStarMapper.selectRecommendedMiniTechStarList();
    }

    /**
     * 查询启用的科技之星列表（小程序端调用）
     * 
     * @return 科技之星集合
     */
    @Override
    public List<MiniTechStar> selectEnabledMiniTechStarList()
    {
        return miniTechStarMapper.selectEnabledMiniTechStarList();
    }
} 