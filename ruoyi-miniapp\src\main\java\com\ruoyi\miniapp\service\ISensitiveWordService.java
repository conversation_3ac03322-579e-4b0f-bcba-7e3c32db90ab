package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.SensitiveWord;

/**
 * 敏感词Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface ISensitiveWordService 
{
    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词主键
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordByWordId(Long wordId);

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord);

    /**
     * 查询所有启用的敏感词
     * 
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectEnabledSensitiveWordList();

    /**
     * 根据词类型查询敏感词
     * 
     * @param wordType 词类型（1敏感词 2白名单）
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordByType(String wordType);

    /**
     * 根据分类ID查询敏感词
     * 
     * @param categoryId 分类ID
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordByCategoryId(Long categoryId);

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int insertSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int updateSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 批量删除敏感词
     * 
     * @param wordIds 需要删除的敏感词主键集合
     * @return 结果
     */
    public int deleteSensitiveWordByWordIds(Long[] wordIds);

    /**
     * 删除敏感词信息
     * 
     * @param wordId 敏感词主键
     * @return 结果
     */
    public int deleteSensitiveWordByWordId(Long wordId);

    /**
     * 检查敏感词内容是否唯一
     * 
     * @param wordContent 敏感词内容
     * @param wordId 敏感词ID（排除自己）
     * @return 结果
     */
    public boolean checkWordContentUnique(String wordContent, Long wordId);

    /**
     * 检测文本是否包含敏感词
     * 
     * @param text 待检测文本
     * @return 是否包含敏感词
     */
    public boolean containsSensitiveWord(String text);

    /**
     * 查找文本中的所有敏感词
     * 
     * @param text 待检测文本
     * @return 敏感词列表
     */
    public List<String> findSensitiveWords(String text);

    /**
     * 替换文本中的敏感词
     * 
     * @param text 待处理文本
     * @return 处理后的文本
     */
    public String replaceSensitiveWords(String text);

    /**
     * 替换文本中的敏感词（指定替换字符）
     * 
     * @param text 待处理文本
     * @param replacement 替换字符
     * @return 处理后的文本
     */
    public String replaceSensitiveWords(String text, char replacement);

    /**
     * 更新敏感词命中次数
     * 
     * @param hitWords 命中的敏感词列表
     */
    public void updateHitCount(List<String> hitWords);

    /**
     * 刷新敏感词缓存
     */
    public void refreshSensitiveWordCache();
}
