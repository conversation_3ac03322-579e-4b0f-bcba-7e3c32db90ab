package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniNewsCenter;
import org.apache.ibatis.annotations.Mapper;

/**
 * 新闻中心Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@Mapper
public interface MiniNewsCenterMapper 
{
    /**
     * 查询新闻中心
     * 
     * @param id 新闻中心主键
     * @return 新闻中心
     */
    public MiniNewsCenter selectMiniNewsCenterById(Long id);

    /**
     * 查询新闻中心列表
     * 
     * @param miniNewsCenter 新闻中心
     * @return 新闻中心集合
     */
    public List<MiniNewsCenter> selectMiniNewsCenterList(MiniNewsCenter miniNewsCenter);

    /**
     * 新增新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    public int insertMiniNewsCenter(MiniNewsCenter miniNewsCenter);

    /**
     * 修改新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    public int updateMiniNewsCenter(MiniNewsCenter miniNewsCenter);

    /**
     * 删除新闻中心
     * 
     * @param id 新闻中心主键
     * @return 结果
     */
    public int deleteMiniNewsCenterById(Long id);

    /**
     * 批量删除新闻中心
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniNewsCenterByIds(Long[] ids);

    /**
     * 根据微信文章ID查询新闻
     * 
     * @param wechatArticleId 微信文章ID
     * @return 新闻中心
     */
    public MiniNewsCenter selectMiniNewsCenterByWechatArticleId(String wechatArticleId);

    /**
     * 批量插入或更新新闻中心（用于同步）
     *
     * @param miniNewsCenterList 新闻中心列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<MiniNewsCenter> miniNewsCenterList);

    /**
     * 清空所有新闻中心数据
     *
     * @return 结果
     */
    public int deleteAllMiniNewsCenter();
} 