package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.CompetitionConfig;
import com.ruoyi.miniapp.service.ICompetitionConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 大赛介绍Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Api(tags = "创赛路演-大赛介绍")
@RestController
@RequestMapping("/miniapp/competition")
public class CompetitionConfigController extends BaseController
{
    @Autowired
    private ICompetitionConfigService competitionConfigService;

    /**
     * 查询大赛介绍列表
     */
    @ApiOperation("查询大赛介绍列表")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") CompetitionConfig competitionConfig)
    {
        startPage();
        List<CompetitionConfig> list = competitionConfigService.selectCompetitionConfigList(competitionConfig);
        return getDataTable(list);
    }

    /**
     * 导出大赛介绍列表
     */
    @ApiOperation("导出大赛介绍列表")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:export')")
    @Log(title = "大赛介绍", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompetitionConfig competitionConfig)
    {
        List<CompetitionConfig> list = competitionConfigService.selectCompetitionConfigList(competitionConfig);
        ExcelUtil<CompetitionConfig> util = new ExcelUtil<CompetitionConfig>(CompetitionConfig.class);
        util.exportExcel(response, list, "大赛介绍数据");
    }

    /**
     * 获取大赛介绍详细信息
     */
    @ApiOperation("获取大赛介绍详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("大赛介绍ID") @PathVariable("id") Long id)
    {
        return success(competitionConfigService.selectCompetitionConfigById(id));
    }

    /**
     * 新增大赛介绍
     */
    @ApiOperation("新增大赛介绍")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:add')")
    @Log(title = "大赛介绍", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("大赛介绍信息") @RequestBody CompetitionConfig competitionConfig)
    {
        return toAjax(competitionConfigService.insertCompetitionConfig(competitionConfig));
    }

    /**
     * 修改大赛介绍
     */
    @ApiOperation("修改大赛介绍")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:edit')")
    @Log(title = "大赛介绍", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("大赛介绍信息") @RequestBody CompetitionConfig competitionConfig)
    {
        return toAjax(competitionConfigService.updateCompetitionConfig(competitionConfig));
    }

    /**
     * 删除大赛介绍
     */
    @ApiOperation("删除大赛介绍")
    @PreAuthorize("@ss.hasPermi('miniapp:competition:remove')")
    @Log(title = "大赛介绍", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("大赛介绍ID数组") @PathVariable Long[] ids)
    {
        return toAjax(competitionConfigService.deleteCompetitionConfigByIds(ids));
    }

    /**
     * 获取当前启用的大赛介绍
     */
    @ApiOperation("获取当前启用的大赛介绍")
    @GetMapping("/enabled")
    public AjaxResult getEnabledConfig()
    {
        return success(competitionConfigService.selectEnabledConfig());
    }
}