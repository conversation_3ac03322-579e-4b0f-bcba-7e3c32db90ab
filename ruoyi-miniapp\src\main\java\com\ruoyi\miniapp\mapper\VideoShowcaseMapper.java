package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.VideoShowcase;

/**
 * 视频展播Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Mapper
public interface VideoShowcaseMapper 
{
    /**
     * 查询视频展播
     *
     * @param id 视频展播主键
     * @return 视频展播
     */
    public VideoShowcase selectVideoShowcaseById(Long id);

    /**
     * 查询视频展播列表
     *
     * @param videoShowcase 视频展播
     * @return 视频展播集合
     */
    public List<VideoShowcase> selectVideoShowcaseList(VideoShowcase videoShowcase);

    /**
     * 新增视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    public int insertVideoShowcase(VideoShowcase videoShowcase);

    /**
     * 修改视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    public int updateVideoShowcase(VideoShowcase videoShowcase);

    /**
     * 删除视频展播
     *
     * @param id 视频展播主键
     * @return 结果
     */
    public int deleteVideoShowcaseById(Long id);

    /**
     * 批量删除视频展播
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVideoShowcaseByIds(Long[] ids);
} 