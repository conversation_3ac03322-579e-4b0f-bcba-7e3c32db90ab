package com.ruoyi.miniapp.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.*;
import com.ruoyi.miniapp.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 行业选择Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "行业树管理")
@RestController
@RequestMapping("/miniapp/industry")
public class MiniIndustryController extends BaseController {
    @Autowired
    private IMiniIndustryTreeService miniIndustryTreeService;

    /** 获取完整产业树（仅启用的） */
    @ApiOperation("获取完整产业树（仅启用的）")
    @GetMapping("/tree")
    public AjaxResult getIndustryTree() {
        List<MiniIndustryTree> tree = miniIndustryTreeService.selectIndustryTree();
        return AjaxResult.success(tree);
    }

    /** 获取所有产业树（包括禁用的，用于后台管理） */
    @ApiOperation("获取所有产业树（包括禁用的，用于后台管理）")
    @GetMapping("/tree/all")
    public AjaxResult getAllIndustryTree() {
        List<MiniIndustryTree> tree = miniIndustryTreeService.selectAllIndustryTree();
        return AjaxResult.success(tree);
    }

    /** 新增节点 */
    @ApiOperation("新增产业树节点")
    @PostMapping("/add")
    @Log(title = "产业树", businessType = BusinessType.INSERT)
    public AjaxResult addIndustryNode(@RequestBody MiniIndustryTree node) {
        // 验证层级规则
        AjaxResult validationResult = validateNodeLevel(node);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        node.setCreateBy(getUsername());
        return toAjax(miniIndustryTreeService.insertMiniIndustryTree(node));
    }

    /** 修改节点 */
    @ApiOperation("修改产业树节点")
    @PostMapping("/edit")
    @Log(title = "产业树", businessType = BusinessType.UPDATE)
    public AjaxResult editIndustryNode(@RequestBody MiniIndustryTree node) {
        // 验证层级规则
        AjaxResult validationResult = validateNodeLevel(node);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        node.setUpdateBy(getUsername());
        return toAjax(miniIndustryTreeService.updateMiniIndustryTree(node));
    }

    /** 删除节点（带使用状态校验和级联删除） */
    @ApiOperation("删除产业树节点")
    @PostMapping("/remove")
    @Log(title = "产业树", businessType = BusinessType.DELETE)
    public AjaxResult removeIndustryNode(@RequestBody Long[] ids) {
        // 检查每个节点的删除约束
        for (Long id : ids) {
            String constraintMessage = miniIndustryTreeService.checkDeleteConstraints(id);
            if (constraintMessage != null) {
                return AjaxResult.error(constraintMessage);
            }
        }

        // 执行级联删除
        int deletedCount = 0;
        for (Long id : ids) {
            deletedCount += miniIndustryTreeService.deleteMiniIndustryTreeCascade(id);
        }

        return deletedCount > 0 ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

    /** 查询节点详情 */
    @ApiOperation("查询产业树节点详情")
    @GetMapping("/info")
    public AjaxResult getIndustryNodeInfo(@RequestParam Long id) {
        MiniIndustryTree node = miniIndustryTreeService.selectMiniIndustryTreeById(id);
        return AjaxResult.success(node);
    }

    /** 查询子节点 */
    @ApiOperation("查询子节点")
    @GetMapping("/children")
    public AjaxResult getChildrenByParentId(@RequestParam Long parentId) {
        List<MiniIndustryTree> list = miniIndustryTreeService.selectChildrenByParentId(parentId);
        return AjaxResult.success(list);
    }

    /** 按层级查询节点 */
    @ApiOperation("按层级查询节点")
    @GetMapping("/level/{level}")
    public AjaxResult getNodesByLevel(@PathVariable Integer level) {
        if (level < 1 || level > 3) {
            return AjaxResult.error("层级参数错误，只支持1-3层级");
        }
        List<MiniIndustryTree> list = miniIndustryTreeService.selectNodesByLevel(level);
        return AjaxResult.success(list);
    }

    /** 获取三层级结构树 */
    @ApiOperation("获取三层级结构树")
    @GetMapping("/threeLevel")
    public AjaxResult getThreeLevelTree() {
        List<MiniIndustryTree> tree = miniIndustryTreeService.selectThreeLevelTree();
        return AjaxResult.success(tree);
    }

    /** 获取上中下游选项 */
    @ApiOperation("获取上中下游选项")
    @GetMapping("/streamTypes")
    public AjaxResult getStreamTypes() {
        Map<String, String> streamTypes = new HashMap<>();
        streamTypes.put("upstream", "上游");
        streamTypes.put("midstream", "中游");
        streamTypes.put("downstream", "下游");
        return AjaxResult.success(streamTypes);
    }

    /** 根据上中下游类型查询第二层级节点 */
    @ApiOperation("根据上中下游类型查询第二层级节点")
    @GetMapping("/streamType")
    public AjaxResult getNodesByStreamType(@RequestParam String streamType) {
        List<MiniIndustryTree> list = miniIndustryTreeService.selectNodesByStreamType(streamType);
        return AjaxResult.success(list);
    }

    /** 批量查询行业节点信息 */
    @ApiOperation("批量查询行业节点信息")
    @PostMapping("/batchInfo")
    public AjaxResult getBatchIndustryInfo(@RequestBody List<Long> industryIds) {
        if (industryIds == null || industryIds.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }

        List<Map<String, Object>> result = miniIndustryTreeService.selectBatchIndustryWithRoot(industryIds);
        return AjaxResult.success(result);
    }

    /** 查找根节点（一级节点） */
    private MiniIndustryTree findRootNode(MiniIndustryTree node) {
        if (node == null) {
            return null;
        }

        // 如果已经是一级节点，直接返回
        if (node.getNodeLevel() == 1) {
            return node;
        }

        // 递归向上查找
        if (node.getParentId() != null && node.getParentId() > 0) {
            MiniIndustryTree parentNode = miniIndustryTreeService.selectMiniIndustryTreeById(node.getParentId());
            return findRootNode(parentNode);
        }

        return null;
    }

    /** 验证节点层级规则 */
    private AjaxResult validateNodeLevel(MiniIndustryTree node) {
        // 验证层级范围
        if (node.getNodeLevel() == null || node.getNodeLevel() < 1 || node.getNodeLevel() > 3) {
            return AjaxResult.error("节点层级必须在1-3之间");
        }

        // 验证父节点层级关系
        if (node.getParentId() != null && node.getParentId() > 0) {
            MiniIndustryTree parentNode = miniIndustryTreeService.selectMiniIndustryTreeById(node.getParentId());
            if (parentNode == null) {
                return AjaxResult.error("父节点不存在");
            }
            if (parentNode.getNodeLevel() != node.getNodeLevel() - 1) {
                return AjaxResult.error("节点层级关系错误");
            }
        }

        // 验证节点类型与层级的对应关系
        String expectedNodeType = getNodeTypeByLevel(node.getNodeLevel());
        if (!expectedNodeType.equals(node.getNodeType())) {
            return AjaxResult.error("节点类型与层级不匹配");
        }

        // 验证第二层级的上中下游标识
        if (node.getNodeLevel() == 2) {
            // 获取父节点信息，检查是否需要上中下游
            if (node.getParentId() != null && node.getParentId() > 0) {
                MiniIndustryTree parentNode = miniIndustryTreeService.selectMiniIndustryTreeById(node.getParentId());
                if (parentNode != null && "1".equals(parentNode.getHasStreamType())) {
                    // 父节点要求有上中下游，验证streamType
                    if (node.getStreamType() == null || node.getStreamType().trim().isEmpty()) {
                        return AjaxResult.error("第二层级节点必须选择上中下游位置");
                    }
                    if (!"upstream".equals(node.getStreamType()) &&
                        !"midstream".equals(node.getStreamType()) &&
                        !"downstream".equals(node.getStreamType())) {
                        return AjaxResult.error("上中下游位置参数错误");
                    }
                }
            }
        }

        return AjaxResult.success();
    }

    /** 根据层级获取节点类型 */
    private String getNodeTypeByLevel(Integer level) {
        switch (level) {
            case 1: return "type";
            case 2: return "position";
            case 3: return "segment";
            default: return "type";
        }
    }
}