package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.GuidanceRegistration;

/**
 * 指导活动报名Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IGuidanceRegistrationService 
{
    /**
     * 查询指导活动报名
     * 
     * @param registrationId 指导活动报名主键
     * @return 指导活动报名
     */
    public GuidanceRegistration selectGuidanceRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询指导活动报名列表
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 指导活动报名集合
     */
    public List<GuidanceRegistration> selectGuidanceRegistrationList(GuidanceRegistration guidanceRegistration);

    /**
     * 新增指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    public int insertGuidanceRegistration(GuidanceRegistration guidanceRegistration);

    /**
     * 修改指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    public int updateGuidanceRegistration(GuidanceRegistration guidanceRegistration);

    /**
     * 批量删除指导活动报名
     * 
     * @param registrationIds 需要删除的指导活动报名主键集合
     * @return 结果
     */
    public int deleteGuidanceRegistrationByRegistrationIds(Long[] registrationIds);

    /**
     * 删除指导活动报名信息
     * 
     * @param registrationId 指导活动报名主键
     * @return 结果
     */
    public int deleteGuidanceRegistrationByRegistrationId(Long registrationId);
} 