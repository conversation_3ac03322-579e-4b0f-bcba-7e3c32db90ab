import request from '@/utils/request'

// 获取统计数据
export function getStatistics() {
  return request({
    url: '/miniapp/statistics/getStatistics',
    method: 'post'
  })
}

// 获取用户排行
export function getUserRanking() {
  return request({
    url: '/miniapp/statistics/getUserRanking',
    method: 'post'
  })
}

// 获取最新弹幕
export function getLatestBarrages() {
  return request({
    url: '/miniapp/statistics/getLatestBarrages',
    method: 'post'
  })
}

// 获取今日数据
export function getTodayData() {
  return request({
    url: '/miniapp/statistics/getTodayData',
    method: 'post'
  })
}

// 获取用户趋势数据
export function getUserTrendData(period) {
  return request({
    url: '/miniapp/statistics/getUserTrendData',
    method: 'post',
    data: { period }
  })
}

// 获取弹幕审核数据
export function getAuditData() {
  return request({
    url: '/miniapp/statistics/getAuditData',
    method: 'post'
  })
} 