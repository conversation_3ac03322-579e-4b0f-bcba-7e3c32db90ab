import request from '@/utils/request'

// ================================ 小程序端活动接口 ================================

/**
 * 获取正在进行的活动列表
 */
export function getActiveEventList() {
  return request({
    url: '/miniapp/event/app/getActiveList',
    method: 'post'
  })
}

/**
 * 获取启用的活动列表
 */
export function getEnabledEventList() {
  return request({
    url: '/miniapp/event/app/getEnabledList',
    method: 'post'
  })
}

/**
 * 获取活动详情
 */
export function getEventDetail(eventId) {
  return request({
    url: '/miniapp/event/app/getDetail',
    method: 'post',
    data: eventId
  })
}

/**
 * 小程序端搜索活动
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @param {string} params.status 活动状态 (all, active, ended)
 * @param {string} params.eventType 活动类型 (activity, guidance)
 * @param {number} params.userId 用户ID (用于"我参与的"筛选)
 */
export function searchEvents(params) {
  return request({
    url: '/miniapp/event/app/search',
    method: 'post',
    data: params
  })
}

/**
 * 获取用户参与的活动列表
 * @param {number} userId 用户ID
 */
export function getUserEvents(userId) {
  return request({
    url: '/miniapp/event/app/getUserEvents',
    method: 'post',
    data: userId
  })
}

// ================================ 活动报名相关接口 ================================

/**
 * 用户报名活动
 * @param {Object} registrationData 报名数据
 */
export function registerEvent(registrationData) {
  return request({
    url: '/miniapp/registration/app/register',
    method: 'post',
    data: registrationData
  })
}

/**
 * 获取用户报名记录
 * @param {number} userId 用户ID
 */
export function getUserRegistrations(userId) {
  return request({
    url: '/miniapp/registration/app/getUserRegistrations',
    method: 'post',
    data: userId
  })
}

/**
 * 检查用户是否已报名某活动
 * @param {number} eventId 活动ID
 * @param {number} userId 用户ID
 */
export function checkRegistrationStatus(eventId, userId) {
  return request({
    url: '/miniapp/registration/app/checkRegistrationStatus',
    method: 'post',
    data: { eventId, userId }
  })
}
