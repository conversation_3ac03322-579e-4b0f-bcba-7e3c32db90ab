package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniBarrage;

/**
 * 弹幕Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniBarrageService 
{
    /**
     * 查询弹幕
     * 
     * @param barrageId 弹幕主键
     * @return 弹幕
     */
    public MiniBarrage selectMiniBarrageByBarrageId(Long barrageId);

    /**
     * 查询弹幕列表
     * 
     * @param miniBarrage 弹幕
     * @return 弹幕集合
     */
    public List<MiniBarrage> selectMiniBarrageList(MiniBarrage miniBarrage);

    /**
     * 新增弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    public int insertMiniBarrage(MiniBarrage miniBarrage);

    /**
     * 修改弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    public int updateMiniBarrage(MiniBarrage miniBarrage);

    /**
     * 批量删除弹幕
     * 
     * @param barrageIds 需要删除的弹幕主键集合
     * @return 结果
     */
    public int deleteMiniBarrageByBarrageIds(Long[] barrageIds);

    /**
     * 删除弹幕信息
     * 
     * @param barrageId 弹幕主键
     * @return 结果
     */
    public int deleteMiniBarrageByBarrageId(Long barrageId);

    /**
     * 查询通过审核的弹幕列表（小程序端调用）
     * 
     * @return 弹幕集合
     */
    public List<MiniBarrage> selectApprovedMiniBarrageList();

    /**
     * 审核弹幕
     * 
     * @param barrageId 弹幕ID
     * @param auditStatus 审核状态（1通过 2拒绝）
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditBarrage(Long barrageId, String auditStatus, String auditRemark);

    /**
     * 用户发布弹幕
     * 
     * @param miniBarrage 弹幕信息
     * @return 结果
     */
    public int publishBarrage(MiniBarrage miniBarrage);
} 