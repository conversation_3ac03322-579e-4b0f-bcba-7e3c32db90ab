package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniEnterprise;


/**
 * 企业管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMiniEnterpriseService 
{
    /**
     * 查询企业管理
     * 
     * @param enterpriseId 企业管理主键
     * @return 企业管理
     */
    public MiniEnterprise selectMiniEnterpriseByEnterpriseId(Long enterpriseId);

    /**
     * 查询企业管理列表
     * 
     * @param miniEnterprise 企业管理
     * @return 企业管理集合
     */
    public List<MiniEnterprise> selectMiniEnterpriseList(MiniEnterprise miniEnterprise);

    /**
     * 新增企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    public int insertMiniEnterprise(MiniEnterprise miniEnterprise);

    /**
     * 修改企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    public int updateMiniEnterprise(MiniEnterprise miniEnterprise);

    /**
     * 批量删除企业管理
     * 
     * @param enterpriseIds 需要删除的企业管理主键集合
     * @return 结果
     */
    public int deleteMiniEnterpriseByEnterpriseIds(Long[] enterpriseIds);

    /**
     * 删除企业管理信息
     * 
     * @param enterpriseId 企业管理主键
     * @return 结果
     */
    public int deleteMiniEnterpriseByEnterpriseId(Long enterpriseId);

    /**
     * 查询企业产业关联信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业产业关联列表
     */
    public List<MiniEnterprise.MiniEnterpriseIndustry> selectEnterpriseIndustryByEnterpriseId(Long enterpriseId);

    /**
     * 更新企业产业关联
     * 
     * @param miniEnterprise 企业信息（包含产业关联列表）
     * @return 结果
     */
    public int updateEnterpriseIndustry(MiniEnterprise miniEnterprise);

    /**
     * 查询企业详细信息（包含产业信息）
     * 
     * @param enterpriseId 企业ID
     * @return 企业详细信息
     */
    public MiniEnterprise selectEnterpriseDetailByEnterpriseId(Long enterpriseId);


} 