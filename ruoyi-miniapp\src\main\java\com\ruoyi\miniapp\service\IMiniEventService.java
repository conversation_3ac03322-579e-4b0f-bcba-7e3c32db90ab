package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniEvent;

/**
 * 活动报名Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniEventService 
{
    /**
     * 查询活动报名
     * 
     * @param eventId 活动报名主键
     * @return 活动报名
     */
    public MiniEvent selectMiniEventByEventId(Long eventId);

    /**
     * 查询活动报名列表
     * 
     * @param miniEvent 活动报名
     * @return 活动报名集合
     */
    public List<MiniEvent> selectMiniEventList(MiniEvent miniEvent);

    /**
     * 新增活动报名
     * 
     * @param miniEvent 活动报名
     * @return 结果
     */
    public int insertMiniEvent(MiniEvent miniEvent);

    /**
     * 修改活动报名
     * 
     * @param miniEvent 活动报名
     * @return 结果
     */
    public int updateMiniEvent(MiniEvent miniEvent);

    /**
     * 批量删除活动报名
     * 
     * @param eventIds 需要删除的活动报名主键集合
     * @return 结果
     */
    public int deleteMiniEventByEventIds(Long[] eventIds);

    /**
     * 删除活动报名信息
     * 
     * @param eventId 活动报名主键
     * @return 结果
     */
    public int deleteMiniEventByEventId(Long eventId);

    /**
     * 查询启用的活动报名列表（小程序端调用）
     * 
     * @return 活动报名集合
     */
    public List<MiniEvent> selectEnabledMiniEventList();

    /**
     * 查询活跃的活动列表
     * 
     * @return 活动报名集合
     */
    public List<MiniEvent> selectActiveMiniEventList();

    /**
     * 查询正在进行的活动列表（小程序端调用）
     *
     * @return 活动报名集合
     */
    public List<MiniEvent> selectOngoingMiniEventList();

    /**
     * 小程序端搜索活动
     *
     * @param keyword 搜索关键词
     * @param status 活动状态 (all, active, ended)
     * @param eventType 活动类型 (activity, guidance)
     * @param userId 用户ID (用于"我参与的"筛选)
     * @return 活动集合
     */
    public List<MiniEvent> searchEventsForApp(String keyword, String status, String eventType, Long userId);

    /**
     * 查询用户参与的活动列表
     *
     * @param userId 用户ID
     * @return 活动集合
     */
    public List<MiniEvent> selectEventsByUserId(Long userId);
}