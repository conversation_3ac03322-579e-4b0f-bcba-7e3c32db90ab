package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.CompetitionConfigMapper;
import com.ruoyi.miniapp.domain.CompetitionConfig;
import com.ruoyi.miniapp.service.ICompetitionConfigService;

/**
 * 大赛介绍Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class CompetitionConfigServiceImpl implements ICompetitionConfigService 
{
    @Autowired
    private CompetitionConfigMapper competitionConfigMapper;

        /**
     * 查询大赛介绍
     *
     * @param id 大赛介绍主键
     * @return 大赛介绍
     */
    @Override
    public CompetitionConfig selectCompetitionConfigById(Long id)
    {
        return competitionConfigMapper.selectCompetitionConfigById(id);
    }

        /**
     * 查询大赛介绍列表
     *
     * @param competitionConfig 大赛介绍
     * @return 大赛介绍
     */
    @Override
    public List<CompetitionConfig> selectCompetitionConfigList(CompetitionConfig competitionConfig)
    {
        return competitionConfigMapper.selectCompetitionConfigList(competitionConfig);
    }

        /**
     * 新增大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    @Override
    public int insertCompetitionConfig(CompetitionConfig competitionConfig)
    {
        if (competitionConfig.getStatus() == null) {
            competitionConfig.setStatus(1);
        }
        return competitionConfigMapper.insertCompetitionConfig(competitionConfig);
    }

        /**
     * 修改大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    @Override
    public int updateCompetitionConfig(CompetitionConfig competitionConfig)
    {
        return competitionConfigMapper.updateCompetitionConfig(competitionConfig);
    }

        /**
     * 批量删除大赛介绍
     *
     * @param ids 需要删除的大赛介绍主键
     * @return 结果
     */
    @Override
    public int deleteCompetitionConfigByIds(Long[] ids)
    {
        return competitionConfigMapper.deleteCompetitionConfigByIds(ids);
    }

        /**
     * 删除大赛介绍信息
     *
     * @param id 大赛介绍主键
     * @return 结果
     */
    @Override
    public int deleteCompetitionConfigById(Long id)
    {
        return competitionConfigMapper.deleteCompetitionConfigById(id);
    }

        /**
     * 获取当前启用的大赛介绍
     *
     * @return 大赛介绍
     */
    @Override
    public CompetitionConfig selectEnabledConfig()
    {
        return competitionConfigMapper.selectEnabledConfig();
    }
}