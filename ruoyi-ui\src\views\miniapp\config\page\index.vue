<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="页面名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入页面名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="pageList" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="页面ID" align="center" prop="pageId" width="80" />
      <el-table-column label="页面名称" align="center" prop="name" />
      <el-table-column label="页面类型" align="center" prop="type" width="100" />
      <el-table-column label="页面描述" align="center" prop="description" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.status === '0'" class="text-success">正常</span>
          <span v-else class="text-danger">禁用</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改页面对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="页面名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入页面名称" />
        </el-form-item>
        <el-form-item label="页面类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择页面类型">
            <el-option label="首页" value="1" />
            <el-option label="关于我们" value="2" />
            <el-option label="帮助中心" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="页面描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入页面描述" />
        </el-form-item>
        <el-form-item label="页面内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入页面内容" rows="10" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "MiniPage",
  data() {
    return {
      loading: true,
      ids: [],
      multiple: true,
      showSearch: true,
      total: 0,
      pageList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      form: {},
      rules: {
        name: [
          { required: true, message: "页面名称不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "页面类型不能为空", trigger: "change" }
        ],
        content: [
          { required: true, message: "页面内容不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      setTimeout(() => {
        this.pageList = [
          {
            pageId: 1,
            name: '关于我们',
            type: '2',
            description: '公司介绍页面',
            content: '这是公司的介绍页面内容...',
            status: '0'
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 1000);
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        pageId: null,
        name: null,
        type: null,
        description: null,
        content: null,
        status: "0"
      };
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pageId);
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加页面";
    },
    handleUpdate(row) {
      this.reset();
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = "修改页面";
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.pageId != null) {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          } else {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }
        }
      });
    },
    handleDelete(row) {
      const pageIds = row.pageId || this.ids;
      this.$modal.confirm('是否确认删除页面编号为"' + pageIds + '"的数据项？').then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script> 