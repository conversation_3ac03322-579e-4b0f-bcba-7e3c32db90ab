package com.ruoyi.miniapp.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.SensitiveWordLogMapper;
import com.ruoyi.miniapp.domain.SensitiveWordLog;
import com.ruoyi.miniapp.service.ISensitiveWordLogService;

/**
 * 敏感词检测日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SensitiveWordLogServiceImpl implements ISensitiveWordLogService 
{
    @Autowired
    private SensitiveWordLogMapper sensitiveWordLogMapper;

    /**
     * 查询敏感词检测日志
     * 
     * @param logId 敏感词检测日志主键
     * @return 敏感词检测日志
     */
    @Override
    public SensitiveWordLog selectSensitiveWordLogByLogId(Long logId)
    {
        return sensitiveWordLogMapper.selectSensitiveWordLogByLogId(logId);
    }

    /**
     * 查询敏感词检测日志列表
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 敏感词检测日志
     */
    @Override
    public List<SensitiveWordLog> selectSensitiveWordLogList(SensitiveWordLog sensitiveWordLog)
    {
        return sensitiveWordLogMapper.selectSensitiveWordLogList(sensitiveWordLog);
    }

    /**
     * 根据用户ID查询敏感词检测日志
     * 
     * @param userId 用户ID
     * @return 敏感词检测日志集合
     */
    @Override
    public List<SensitiveWordLog> selectSensitiveWordLogByUserId(Long userId)
    {
        return sensitiveWordLogMapper.selectSensitiveWordLogByUserId(userId);
    }

    /**
     * 根据模块名称查询敏感词检测日志
     * 
     * @param moduleName 模块名称
     * @return 敏感词检测日志集合
     */
    @Override
    public List<SensitiveWordLog> selectSensitiveWordLogByModuleName(String moduleName)
    {
        return sensitiveWordLogMapper.selectSensitiveWordLogByModuleName(moduleName);
    }

    /**
     * 新增敏感词检测日志
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 结果
     */
    @Override
    public int insertSensitiveWordLog(SensitiveWordLog sensitiveWordLog)
    {
        sensitiveWordLog.setCreateTime(DateUtils.getNowDate());
        return sensitiveWordLogMapper.insertSensitiveWordLog(sensitiveWordLog);
    }

    /**
     * 修改敏感词检测日志
     * 
     * @param sensitiveWordLog 敏感词检测日志
     * @return 结果
     */
    @Override
    public int updateSensitiveWordLog(SensitiveWordLog sensitiveWordLog)
    {
        return sensitiveWordLogMapper.updateSensitiveWordLog(sensitiveWordLog);
    }

    /**
     * 批量删除敏感词检测日志
     * 
     * @param logIds 需要删除的敏感词检测日志主键
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordLogByLogIds(Long[] logIds)
    {
        return sensitiveWordLogMapper.deleteSensitiveWordLogByLogIds(logIds);
    }

    /**
     * 删除敏感词检测日志信息
     * 
     * @param logId 敏感词检测日志主键
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordLogByLogId(Long logId)
    {
        return sensitiveWordLogMapper.deleteSensitiveWordLogByLogId(logId);
    }

    /**
     * 清理指定天数前的日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    @Override
    public int cleanSensitiveWordLogByDays(int days)
    {
        return sensitiveWordLogMapper.cleanSensitiveWordLogByDays(days);
    }

    /**
     * 记录敏感词检测日志
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param moduleName 模块名称
     * @param operationType 操作类型
     * @param originalContent 原始内容
     * @param filteredContent 过滤后内容
     * @param hitWords 命中的敏感词
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     */
    @Override
    public void recordSensitiveWordLog(Long userId, String userName, String moduleName, 
                                     String operationType, String originalContent, 
                                     String filteredContent, List<String> hitWords, 
                                     String clientIp, String userAgent)
    {
        SensitiveWordLog log = new SensitiveWordLog();
        log.setUserId(userId);
        log.setUserName(userName);
        log.setModuleName(moduleName);
        log.setOperationType(operationType);
        log.setOriginalContent(originalContent);
        log.setFilteredContent(filteredContent);
        log.setHitCount(hitWords != null ? hitWords.size() : 0);
        log.setHitWords(hitWords != null ? JSON.toJSONString(hitWords) : null);
        log.setClientIp(clientIp);
        log.setUserAgent(userAgent);
        log.setCreateTime(new Date());
        
        insertSensitiveWordLog(log);
    }
}
