# 小程序管理后台模块说明

## 模块概述

`ruoyi-miniapp` 模块是基于若依框架开发的小程序管理后台系统，用于管理小程序用户、内容、积分等功能。

## 数据库表关系图

### 核心表关系

```
mini_tag_category (标签分类)
└── mini_tag (标签) [1:N]

mini_demand_category (需求分类)
└── mini_demand (需求信息) [1:N]

mini_event (活动)
└── mini_event_registration (活动报名) [1:N]
```

### 独立内容管理表

- `mini_banner` - 轮播图管理
- `mini_notice` - 通知管理
- `mini_activity` - 精彩活动管理
- `mini_tech_star` - 科技之星管理
- `mini_job` - 招聘信息管理
- `mini_page_content` - 页面内容管理

## 表结构详细说明

### 1. 标签体系

#### mini_tag_category (标签分类表)
- **分类类型**: 职位标签、行业标签、资质标签、身份标签

#### mini_tag (标签表)
- **关联**: 属于某个标签分类
- **特点**: 包含标签颜色用于前端展示

### 2. 内容管理

#### mini_barrage (弹幕表)
- **特点**: 包含审核机制
- **状态**: 0-待审核、1-审核通过、2-审核拒绝
- **冗余字段**: 存储发布用户的昵称和头像，提高查询效率

#### mini_banner (轮播图表)
- **用途**: 首页轮播图展示
- **功能**: 支持跳转链接

#### mini_notice (通知表)
- **用途**: 首页滚动通知栏
- **特点**: 支持排序

#### mini_activity (精彩活动表)
- **用途**: 关联公众号文章
- **功能**: 点击跳转到公众号文章详情

#### mini_tech_star (科技之星表)
- **用途**: 展示优秀企业家和企业
- **展示**: 轮动展示照片、姓名、企业、简介

#### mini_job (招聘职位表)
- **用途**: "加入我们"板块的招聘信息
- **内容**: 职位、公司、薪资、要求等信息

### 3. 需求管理

#### mini_demand_category (需求分类表)
- **用途**: 需求对接平台的8个分类

#### mini_demand (需求信息表)
- **状态**: 已发布、已对接、已下架
- **功能**: 支持置顶、浏览计数

### 4. 活动报名

#### mini_event (活动报名表)
- **特点**: 使用JSON字段存储动态表单配置
- **灵活性**: 每个活动可配置不同的报名表单字段

#### mini_event_registration (用户报名记录表)
- **数据**: JSON格式存储用户填写的报名信息
- **约束**: 同一用户同一活动只能报名一次

### 5. 系统配置

#### mini_page_content (页面内容管理表)
- **用途**: 管理"关于我们"、"用户协议"、"隐私协议"等页面内容
- **字段**: `page_code` 用于区分不同页面

## 代码架构

### 目录结构

```
ruoyi-miniapp/
├── src/main/java/com/ruoyi/miniapp/
│   ├── domain/          # 实体类
│   │   ├── MiniTag.java
│   │   ├── MiniBanner.java
│   │   ├── MiniNotice.java
│   │   └── ...
│   ├── mapper/          # Mapper接口
│   │   ├── MiniTagMapper.java
│   │   ├── MiniBannerMapper.java
│   │   └── ...
│   └── service/         # 服务层
│       ├── IMiniTagService.java
│       └── impl/
└── src/main/resources/
    └── mapper/miniapp/  # Mapper XML文件
        ├── MiniTagMapper.xml
        ├── MiniBannerMapper.xml
        └── ...
```

### 已生成的组件

1. **实体类 (Domain)**
   - ✅ MiniTag - 标签
   - ✅ MiniTagCategory - 标签分类
   - ✅ MiniBanner - 轮播图
   - ✅ MiniNotice - 通知
   - ✅ MiniBarrage - 弹幕
   - ✅ MiniActivity - 精彩活动
   - ✅ MiniEvent - 活动报名
   - ✅ MiniDemand - 需求对接

2. **Mapper接口**
   - ✅ 对应所有实体类的Mapper接口
   - ✅ 包含基础的CRUD方法
   - ✅ 包含业务特定的查询方法

3. **Mapper XML**
   - ✅ MiniBarrageMapper.xml - 弹幕相关SQL
   - ✅ MiniBannerMapper.xml - 轮播图相关SQL
   - ✅ MiniNoticeMapper.xml - 通知相关SQL

## 业务逻辑说明

### 弹幕审核流程

1. 发布弹幕 → 状态：待审核
2. 管理员审核 → 状态：审核通过/审核拒绝
3. 只有审核通过的弹幕才会在首页展示

## 后续开发建议

1. **Service层**: 需要创建对应的Service接口和实现类
2. **Controller层**: 需要创建对应的Controller用于后台管理
3. **前端页面**: 需要创建对应的Vue页面用于数据管理
4. **API接口**: 需要创建小程序端调用的API接口
5. **权限控制**: 根据业务需要配置相应的权限

## 注意事项

1. **数据库索引**: 建议对经常查询的字段添加索引
2. **数据安全**: 敏感信息需要加密存储
3. **性能优化**: 大表查询需要注意分页和缓存
4. **数据一致性**: 关键业务操作需要保证事务一致性