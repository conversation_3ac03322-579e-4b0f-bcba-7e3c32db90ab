package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniJob;
import com.ruoyi.miniapp.mapper.MiniJobMapper;
import com.ruoyi.miniapp.service.IMiniJobService;

/**
 * 招聘职位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniJobServiceImpl implements IMiniJobService 
{
    @Autowired
    private MiniJobMapper miniJobMapper;

    /**
     * 查询招聘职位
     * 
     * @param jobId 招聘职位主键
     * @return 招聘职位
     */
    @Override
    public MiniJob selectMiniJobByJobId(Long jobId)
    {
        return miniJobMapper.selectMiniJobByJobId(jobId);
    }

    /**
     * 查询招聘职位列表
     * 
     * @param miniJob 招聘职位
     * @return 招聘职位
     */
    @Override
    public List<MiniJob> selectMiniJobList(MiniJob miniJob)
    {
        return miniJobMapper.selectMiniJobList(miniJob);
    }

    /**
     * 新增招聘职位
     * 
     * @param miniJob 招聘职位
     * @return 结果
     */
    @Override
    public int insertMiniJob(MiniJob miniJob)
    {
        miniJob.setCreateTime(DateUtils.getNowDate());
        return miniJobMapper.insertMiniJob(miniJob);
    }

    /**
     * 修改招聘职位
     * 
     * @param miniJob 招聘职位
     * @return 结果
     */
    @Override
    public int updateMiniJob(MiniJob miniJob)
    {
        miniJob.setUpdateTime(DateUtils.getNowDate());
        return miniJobMapper.updateMiniJob(miniJob);
    }

    /**
     * 批量删除招聘职位
     * 
     * @param jobIds 需要删除的招聘职位主键
     * @return 结果
     */
    @Override
    public int deleteMiniJobByJobIds(Long[] jobIds)
    {
        return miniJobMapper.deleteMiniJobByJobIds(jobIds);
    }

    /**
     * 删除招聘职位信息
     * 
     * @param jobId 招聘职位主键
     * @return 结果
     */
    @Override
    public int deleteMiniJobByJobId(Long jobId)
    {
        return miniJobMapper.deleteMiniJobByJobId(jobId);
    }

    /**
     * 查询推荐的招聘职位列表（小程序端调用）
     * 
     * @return 招聘职位集合
     */
    @Override
    public List<MiniJob> selectRecommendedMiniJobList()
    {
        return miniJobMapper.selectRecommendedMiniJobList();
    }

    /**
     * 查询启用的招聘职位列表（小程序端调用）
     * 
     * @return 招聘职位集合
     */
    @Override
    public List<MiniJob> selectEnabledMiniJobList()
    {
        return miniJobMapper.selectEnabledMiniJobList();
    }
} 