<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniCollegeMapper">
    
    <resultMap type="MiniCollege" id="MiniCollegeResult">
        <result property="collegeId"    column="college_id"    />
        <result property="collegeName"    column="college_name"    />
        <result property="collegeCode"    column="college_code"    />
        <result property="collegeDesc"    column="college_desc"    />
        <result property="deanName"    column="dean_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="address"    column="address"    />
        <result property="websiteUrl"    column="website_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniCollegeVo">
        select college_id, college_name, college_code, college_desc, dean_name, contact_phone, contact_email, address, website_url, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_college
    </sql>

    <select id="selectMiniCollegeList" parameterType="MiniCollege" resultMap="MiniCollegeResult">
        <include refid="selectMiniCollegeVo"/>
        <where>  
            <if test="collegeName != null  and collegeName != ''"> and college_name like concat('%', #{collegeName}, '%')</if>
            <if test="collegeCode != null  and collegeCode != ''"> and college_code = #{collegeCode}</if>
            <if test="deanName != null  and deanName != ''"> and dean_name like concat('%', #{deanName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniCollegeByCollegeId" parameterType="Long" resultMap="MiniCollegeResult">
        <include refid="selectMiniCollegeVo"/>
        where college_id = #{collegeId}
    </select>

    <select id="selectMiniCollegeByCollegeCode" parameterType="String" resultMap="MiniCollegeResult">
        <include refid="selectMiniCollegeVo"/>
        where college_code = #{collegeCode}
    </select>

    <select id="selectEnabledMiniCollegeList" resultMap="MiniCollegeResult">
        <include refid="selectMiniCollegeVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniCollege" parameterType="MiniCollege" useGeneratedKeys="true" keyProperty="collegeId">
        insert into mini_college
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="collegeName != null and collegeName != ''">college_name,</if>
            <if test="collegeCode != null and collegeCode != ''">college_code,</if>
            <if test="collegeDesc != null">college_desc,</if>
            <if test="deanName != null">dean_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="address != null">address,</if>
            <if test="websiteUrl != null">website_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="collegeName != null and collegeName != ''">#{collegeName},</if>
            <if test="collegeCode != null and collegeCode != ''">#{collegeCode},</if>
            <if test="collegeDesc != null">#{collegeDesc},</if>
            <if test="deanName != null">#{deanName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="address != null">#{address},</if>
            <if test="websiteUrl != null">#{websiteUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniCollege" parameterType="MiniCollege">
        update mini_college
        <trim prefix="SET" suffixOverrides=",">
            <if test="collegeName != null and collegeName != ''">college_name = #{collegeName},</if>
            <if test="collegeCode != null and collegeCode != ''">college_code = #{collegeCode},</if>
            <if test="collegeDesc != null">college_desc = #{collegeDesc},</if>
            <if test="deanName != null">dean_name = #{deanName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="address != null">address = #{address},</if>
            <if test="websiteUrl != null">website_url = #{websiteUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where college_id = #{collegeId}
    </update>

    <delete id="deleteMiniCollegeByCollegeId" parameterType="Long">
        delete from mini_college where college_id = #{collegeId}
    </delete>

    <delete id="deleteMiniCollegeByCollegeIds" parameterType="String">
        delete from mini_college where college_id in 
        <foreach item="collegeId" collection="array" open="(" separator="," close=")">
            #{collegeId}
        </foreach>
    </delete>

</mapper>
