package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.HaitangTopImageMapper;
import com.ruoyi.miniapp.domain.HaitangTopImage;
import com.ruoyi.miniapp.service.IHaitangTopImageService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 海棠杯顶图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class HaitangTopImageServiceImpl implements IHaitangTopImageService 
{
    @Autowired
    private HaitangTopImageMapper haitangTopImageMapper;

    /**
     * 查询海棠杯顶图
     * 
     * @param topImageId 海棠杯顶图主键
     * @return 海棠杯顶图
     */
    @Override
    public HaitangTopImage selectHaitangTopImageByTopImageId(Long topImageId)
    {
        return haitangTopImageMapper.selectHaitangTopImageByTopImageId(topImageId);
    }

    /**
     * 查询海棠杯顶图列表
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 海棠杯顶图
     */
    @Override
    public List<HaitangTopImage> selectHaitangTopImageList(HaitangTopImage haitangTopImage)
    {
        return haitangTopImageMapper.selectHaitangTopImageList(haitangTopImage);
    }

    /**
     * 查询启用的海棠杯顶图
     * 
     * @return 海棠杯顶图
     */
    @Override
    public HaitangTopImage selectEnabledHaitangTopImage()
    {
        return haitangTopImageMapper.selectEnabledHaitangTopImage();
    }

    /**
     * 新增海棠杯顶图
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 结果
     */
    @Override
    public int insertHaitangTopImage(HaitangTopImage haitangTopImage)
    {
        haitangTopImage.setCreateTime(DateUtils.getNowDate());
        return haitangTopImageMapper.insertHaitangTopImage(haitangTopImage);
    }

    /**
     * 修改海棠杯顶图
     * 
     * @param haitangTopImage 海棠杯顶图
     * @return 结果
     */
    @Override
    public int updateHaitangTopImage(HaitangTopImage haitangTopImage)
    {
        haitangTopImage.setUpdateTime(DateUtils.getNowDate());
        return haitangTopImageMapper.updateHaitangTopImage(haitangTopImage);
    }

    /**
     * 批量删除海棠杯顶图
     * 
     * @param topImageIds 需要删除的海棠杯顶图主键
     * @return 结果
     */
    @Override
    public int deleteHaitangTopImageByTopImageIds(Long[] topImageIds)
    {
        return haitangTopImageMapper.deleteHaitangTopImageByTopImageIds(topImageIds);
    }

    /**
     * 删除海棠杯顶图信息
     * 
     * @param topImageId 海棠杯顶图主键
     * @return 结果
     */
    @Override
    public int deleteHaitangTopImageByTopImageId(Long topImageId)
    {
        return haitangTopImageMapper.deleteHaitangTopImageByTopImageId(topImageId);
    }
}
