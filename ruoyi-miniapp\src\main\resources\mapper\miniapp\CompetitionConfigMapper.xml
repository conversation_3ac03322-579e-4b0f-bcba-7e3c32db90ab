<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.CompetitionConfigMapper">
    
    <resultMap type="CompetitionConfig" id="CompetitionConfigResult">
        <result property="id"    column="id"    />
        <result property="topImageUrl"    column="top_image_url"    />
        <result property="descriptionContent"    column="description_content"    />
        <result property="scheduleContent"    column="schedule_content"    />
        <result property="registrationConditions"    column="registration_conditions"    />
        <result property="faqContent"    column="faq_content"    />
        <result property="businessCooperation"    column="business_cooperation"    />
        <result property="sponsorImageUrl"    column="sponsor_image_url"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactWechat"    column="contact_wechat"    />
        <result property="status"    column="status"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCompetitionConfigVo">
        select id, top_image_url, description_content, schedule_content, registration_conditions, faq_content, business_cooperation, sponsor_image_url, contact_name, contact_phone, contact_wechat, status, created_at, updated_at from mini_competition_config
    </sql>

    <select id="selectCompetitionConfigList" parameterType="CompetitionConfig" resultMap="CompetitionConfigResult">
        <include refid="selectCompetitionConfigVo"/>
        <where>  
            <if test="topImageUrl != null  and topImageUrl != ''"> and top_image_url like concat('%', #{topImageUrl}, '%')</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="contactWechat != null  and contactWechat != ''"> and contact_wechat like concat('%', #{contactWechat}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectCompetitionConfigById" parameterType="Long" resultMap="CompetitionConfigResult">
        <include refid="selectCompetitionConfigVo"/>
        where id = #{id}
    </select>
    
    <select id="selectEnabledConfig" resultMap="CompetitionConfigResult">
        <include refid="selectCompetitionConfigVo"/>
        where status = 0
        order by id desc
        limit 1
    </select>
        
    <insert id="insertCompetitionConfig" parameterType="CompetitionConfig" useGeneratedKeys="true" keyProperty="id">
        insert into mini_competition_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="topImageUrl != null">top_image_url,</if>
            <if test="descriptionContent != null">description_content,</if>
            <if test="scheduleContent != null">schedule_content,</if>
            <if test="registrationConditions != null">registration_conditions,</if>
            <if test="faqContent != null">faq_content,</if>
            <if test="businessCooperation != null">business_cooperation,</if>
            <if test="sponsorImageUrl != null">sponsor_image_url,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactWechat != null">contact_wechat,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="topImageUrl != null">#{topImageUrl},</if>
            <if test="descriptionContent != null">#{descriptionContent},</if>
            <if test="scheduleContent != null">#{scheduleContent},</if>
            <if test="registrationConditions != null">#{registrationConditions},</if>
            <if test="faqContent != null">#{faqContent},</if>
            <if test="businessCooperation != null">#{businessCooperation},</if>
            <if test="sponsorImageUrl != null">#{sponsorImageUrl},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactWechat != null">#{contactWechat},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateCompetitionConfig" parameterType="CompetitionConfig">
        update mini_competition_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="topImageUrl != null">top_image_url = #{topImageUrl},</if>
            <if test="descriptionContent != null">description_content = #{descriptionContent},</if>
            <if test="scheduleContent != null">schedule_content = #{scheduleContent},</if>
            <if test="registrationConditions != null">registration_conditions = #{registrationConditions},</if>
            <if test="faqContent != null">faq_content = #{faqContent},</if>
            <if test="businessCooperation != null">business_cooperation = #{businessCooperation},</if>
            <if test="sponsorImageUrl != null">sponsor_image_url = #{sponsorImageUrl},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactWechat != null">contact_wechat = #{contactWechat},</if>
            <if test="status != null">status = #{status},</if>
            updated_at = CURRENT_TIMESTAMP,
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompetitionConfigById" parameterType="Long">
        delete from mini_competition_config where id = #{id}
    </delete>

    <delete id="deleteCompetitionConfigByIds" parameterType="String">
        delete from mini_competition_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>