package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniCollege;
import com.ruoyi.miniapp.service.IMiniCollegeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 学院信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Api(tags = "学院信息管理")
@RestController
@RequestMapping("/miniapp/college")
public class MiniCollegeController extends BaseController
{
    @Autowired
    private IMiniCollegeService miniCollegeService;

    /**
     * 查询学院信息列表
     */
    @ApiOperation("查询学院信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:college:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniCollege miniCollege)
    {
        startPage();
        List<MiniCollege> list = miniCollegeService.selectMiniCollegeList(miniCollege);
        return getDataTable(list);
    }

    /**
     * 导出学院信息列表
     */
    @ApiOperation("导出学院信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:college:export')")
    @Log(title = "学院信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniCollege miniCollege)
    {
        List<MiniCollege> list = miniCollegeService.selectMiniCollegeList(miniCollege);
        ExcelUtil<MiniCollege> util = new ExcelUtil<MiniCollege>(MiniCollege.class);
        util.exportExcel(response, list, "学院信息数据");
    }

    /**
     * 获取学院信息详细信息
     */
    @ApiOperation("获取学院信息详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:college:query')")
    @GetMapping(value = "/{collegeId}")
    public AjaxResult getInfo(@ApiParam("学院ID") @PathVariable("collegeId") Long collegeId)
    {
        return AjaxResult.success(miniCollegeService.selectMiniCollegeByCollegeId(collegeId));
    }

    /**
     * 新增学院信息
     */
    @ApiOperation("新增学院信息")
    @PreAuthorize("@ss.hasPermi('miniapp:college:add')")
    @Log(title = "学院信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("学院信息") @RequestBody MiniCollege miniCollege)
    {
        if (!miniCollegeService.checkCollegeCodeUnique(miniCollege))
        {
            return AjaxResult.error("新增学院'" + miniCollege.getCollegeName() + "'失败，学院代码已存在");
        }
        miniCollege.setCreateBy(getUsername());
        return toAjax(miniCollegeService.insertMiniCollege(miniCollege));
    }

    /**
     * 修改学院信息
     */
    @ApiOperation("修改学院信息")
    @PreAuthorize("@ss.hasPermi('miniapp:college:edit')")
    @Log(title = "学院信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("学院信息") @RequestBody MiniCollege miniCollege)
    {
        if (!miniCollegeService.checkCollegeCodeUnique(miniCollege))
        {
            return AjaxResult.error("修改学院'" + miniCollege.getCollegeName() + "'失败，学院代码已存在");
        }
        miniCollege.setUpdateBy(getUsername());
        return toAjax(miniCollegeService.updateMiniCollege(miniCollege));
    }

    /**
     * 删除学院信息
     */
    @ApiOperation("删除学院信息")
    @PreAuthorize("@ss.hasPermi('miniapp:college:remove')")
    @Log(title = "学院信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{collegeIds}")
    public AjaxResult remove(@ApiParam("学院ID数组") @PathVariable Long[] collegeIds)
    {
        return toAjax(miniCollegeService.deleteMiniCollegeByCollegeIds(collegeIds));
    }

    /**
     * 获取启用的学院信息列表
     */
    @ApiOperation("获取启用的学院信息列表")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<MiniCollege> list = miniCollegeService.selectEnabledMiniCollegeList();
        return AjaxResult.success(list);
    }

    /**
     * 校验学院代码
     */
    @ApiOperation("校验学院代码")
    @PostMapping("/checkCollegeCodeUnique")
    public AjaxResult checkCollegeCodeUnique(@ApiParam("学院信息") @RequestBody MiniCollege miniCollege)
    {
        return AjaxResult.success(miniCollegeService.checkCollegeCodeUnique(miniCollege));
    }
}
