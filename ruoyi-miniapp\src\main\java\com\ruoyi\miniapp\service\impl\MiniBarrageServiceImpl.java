package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniBarrage;
import com.ruoyi.miniapp.mapper.MiniBarrageMapper;
import com.ruoyi.miniapp.service.IMiniBarrageService;

/**
 * 弹幕Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniBarrageServiceImpl implements IMiniBarrageService 
{
    @Autowired
    private MiniBarrageMapper miniBarrageMapper;

    /**
     * 查询弹幕
     * 
     * @param barrageId 弹幕主键
     * @return 弹幕
     */
    @Override
    public MiniBarrage selectMiniBarrageByBarrageId(Long barrageId)
    {
        return miniBarrageMapper.selectMiniBarrageByBarrageId(barrageId);
    }

    /**
     * 查询弹幕列表
     * 
     * @param miniBarrage 弹幕
     * @return 弹幕
     */
    @Override
    public List<MiniBarrage> selectMiniBarrageList(MiniBarrage miniBarrage)
    {
        return miniBarrageMapper.selectMiniBarrageList(miniBarrage);
    }

    /**
     * 新增弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    @Override
    public int insertMiniBarrage(MiniBarrage miniBarrage)
    {
        miniBarrage.setCreateTime(DateUtils.getNowDate());
        return miniBarrageMapper.insertMiniBarrage(miniBarrage);
    }

    /**
     * 修改弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    @Override
    public int updateMiniBarrage(MiniBarrage miniBarrage)
    {
        miniBarrage.setUpdateTime(DateUtils.getNowDate());
        return miniBarrageMapper.updateMiniBarrage(miniBarrage);
    }

    /**
     * 批量删除弹幕
     * 
     * @param barrageIds 需要删除的弹幕主键
     * @return 结果
     */
    @Override
    public int deleteMiniBarrageByBarrageIds(Long[] barrageIds)
    {
        return miniBarrageMapper.deleteMiniBarrageByBarrageIds(barrageIds);
    }

    /**
     * 删除弹幕信息
     * 
     * @param barrageId 弹幕主键
     * @return 结果
     */
    @Override
    public int deleteMiniBarrageByBarrageId(Long barrageId)
    {
        return miniBarrageMapper.deleteMiniBarrageByBarrageId(barrageId);
    }

    /**
     * 查询通过审核的弹幕列表（小程序端调用）
     * 
     * @return 弹幕集合
     */
    @Override
    public List<MiniBarrage> selectApprovedMiniBarrageList()
    {
        return miniBarrageMapper.selectApprovedMiniBarrageList();
    }

    /**
     * 审核弹幕
     * 
     * @param barrageId 弹幕ID
     * @param auditStatus 审核状态（1通过 2拒绝）
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditBarrage(Long barrageId, String auditStatus, String auditRemark)
    {
        MiniBarrage barrage = new MiniBarrage();
        barrage.setBarrageId(barrageId);
        barrage.setAuditStatus(auditStatus);
        barrage.setAuditRemark(auditRemark);
        barrage.setUpdateTime(DateUtils.getNowDate());
        return miniBarrageMapper.updateMiniBarrage(barrage);
    }

    /**
     * 用户发布弹幕
     * 
     * @param miniBarrage 弹幕信息
     * @return 结果
     */
    @Override
    public int publishBarrage(MiniBarrage miniBarrage)
    {
        // 设置弹幕为待审核状态
        miniBarrage.setAuditStatus("0"); // 待审核
        miniBarrage.setCreateTime(DateUtils.getNowDate());
        
        return insertMiniBarrage(miniBarrage);
    }
} 