package com.ruoyi.framework.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 图片资源配置
 * 配置静态资源映射，让前端能够访问下载的图片
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Configuration
public class ImageResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置图片访问路径映射
        // 访问路径：/images/**
        // 实际路径：D:/develop/images/
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:D:/develop/images/");
    }
}
