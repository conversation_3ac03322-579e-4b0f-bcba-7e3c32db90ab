package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.VideoShowcase;
import com.ruoyi.miniapp.service.IVideoShowcaseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 视频展播Controller
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Api(tags = "创赛路演-视频展播管理")
@RestController
@RequestMapping("/miniapp/video")
public class VideoShowcaseController extends BaseController
{
    @Autowired
    private IVideoShowcaseService videoShowcaseService;

    /**
     * 查询视频展播列表
     */
    @ApiOperation("查询视频展播列表")
    @PreAuthorize("@ss.hasPermi('miniapp:video:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") VideoShowcase videoShowcase)
    {
        startPage();
        List<VideoShowcase> list = videoShowcaseService.selectVideoShowcaseList(videoShowcase);
        return getDataTable(list);
    }

    /**
     * 导出视频展播列表
     */
    @ApiOperation("导出视频展播列表")
    @PreAuthorize("@ss.hasPermi('miniapp:video:export')")
    @Log(title = "视频展播", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") VideoShowcase videoShowcase)
    {
        List<VideoShowcase> list = videoShowcaseService.selectVideoShowcaseList(videoShowcase);
        ExcelUtil<VideoShowcase> util = new ExcelUtil<VideoShowcase>(VideoShowcase.class);
        util.exportExcel(response, list, "视频展播数据");
    }

    /**
     * 获取视频展播详细信息
     */
    @ApiOperation("获取视频展播详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:video:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("视频ID") @PathVariable("id") Long id)
    {
        return success(videoShowcaseService.selectVideoShowcaseById(id));
    }

    /**
     * 新增视频展播
     */
    @ApiOperation("新增视频展播")
    @PreAuthorize("@ss.hasPermi('miniapp:video:add')")
    @Log(title = "视频展播", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("视频展播信息") @RequestBody VideoShowcase videoShowcase)
    {
        return toAjax(videoShowcaseService.insertVideoShowcase(videoShowcase));
    }

    /**
     * 修改视频展播
     */
    @ApiOperation("修改视频展播")
    @PreAuthorize("@ss.hasPermi('miniapp:video:edit')")
    @Log(title = "视频展播", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("视频展播信息") @RequestBody VideoShowcase videoShowcase)
    {
        return toAjax(videoShowcaseService.updateVideoShowcase(videoShowcase));
    }

    /**
     * 删除视频展播
     */
    @PreAuthorize("@ss.hasPermi('miniapp:video:remove')")
    @Log(title = "视频展播", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(videoShowcaseService.deleteVideoShowcaseByIds(ids));
    }
} 