package com.ruoyi.web.controller.mq;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.mq.config.RabbitMQConfig;
import com.ruoyi.mq.domain.MessageDTO;
import com.ruoyi.mq.service.RabbitMQProducer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ测试接口
 * 
 * <AUTHOR>
 */
@Api(tags = "RabbitMQ消息接口")
@RestController
@RequestMapping("/test/mq")
public class RabbitMQController extends BaseController {

    @Autowired
    private RabbitMQProducer rabbitMQProducer;

    /**
     * 发送普通消息
     */
    @ApiOperation("发送普通消息")
    @PostMapping("/send")
    public AjaxResult sendMessage(@RequestBody MessageDTO message) {
        String messageId = rabbitMQProducer.sendMessage(
                RabbitMQConfig.DIRECT_EXCHANGE, 
                RabbitMQConfig.DEFAULT_ROUTING_KEY, 
                message);
        
        Map<String, Object> data = new HashMap<>();
        data.put("messageId", messageId);
        return AjaxResult.success("消息发送成功", data);
    }

    /**
     * 发送延迟消息
     */
    @ApiOperation("发送延迟消息")
    @PostMapping("/send/delay")
    public AjaxResult sendDelayMessage(@RequestBody MessageDTO message, @RequestParam long delayTime) {
        String messageId = rabbitMQProducer.sendDelayMessage(message, delayTime);
        
        Map<String, Object> data = new HashMap<>();
        data.put("messageId", messageId);
        data.put("delayTime", delayTime);
        return AjaxResult.success("延迟消息发送成功", data);
    }

    /**
     * 发送消息到示例队列
     */
    @ApiOperation("发送消息到示例队列")
    @PostMapping("/send/demo")
    public AjaxResult sendDemoMessage(@RequestBody MessageDTO message) {
        String messageId = rabbitMQProducer.sendMessage(
                RabbitMQConfig.DIRECT_EXCHANGE, 
                RabbitMQConfig.DEMO_ROUTING_KEY, 
                message);
        
        Map<String, Object> data = new HashMap<>();
        data.put("messageId", messageId);
        return AjaxResult.success("示例消息发送成功", data);
    }

    /**
     * 简单消息发送（GET请求测试）
     */
    @ApiOperation("简单消息发送（GET请求测试）")
    @GetMapping("/simple")
    public AjaxResult sendSimpleMessage(@RequestParam String content) {
        MessageDTO message = new MessageDTO(content);
        String messageId = rabbitMQProducer.sendMessage(
                RabbitMQConfig.DIRECT_EXCHANGE, 
                RabbitMQConfig.DEFAULT_ROUTING_KEY, 
                message);
        
        Map<String, Object> data = new HashMap<>();
        data.put("messageId", messageId);
        data.put("content", content);
        return AjaxResult.success("简单消息发送成功", data);
    }
} 