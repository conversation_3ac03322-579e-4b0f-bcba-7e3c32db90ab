-- mini_notice表结构增强完成记录
-- 执行时间：2025-01-20
-- 目的：为mini_notice表添加title字段，并将content字段改为支持富文本

-- ========================================
-- 已完成的数据库修改
-- ========================================

-- 1. 添加title字段
-- ALTER TABLE `mini_notice` ADD COLUMN `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '通知标题' AFTER `notice_id`;

-- 2. 修改content字段以支持富文本
-- ALTER TABLE `mini_notice` MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '通知内容（富文本）';

-- 3. 添加title字段索引
-- ALTER TABLE `mini_notice` ADD INDEX `idx_title`(`title`) USING BTREE;

-- 4. 更新现有数据的标题
-- UPDATE `mini_notice` SET `title` = CASE 
--   WHEN notice_id = 1 THEN '欢迎通知'
--   WHEN notice_id = 2 THEN '活动提醒'
--   WHEN notice_id = 3 THEN '维护通知'
--   ELSE CONCAT('通知-', notice_id)
-- END WHERE `title` = '' OR `title` IS NULL;

-- ========================================
-- 当前表结构
-- ========================================
-- 字段列表：
-- - notice_id: bigint (主键，自增)
-- - title: varchar(200) (通知标题，必填)
-- - content: text (通知内容，富文本格式，必填)
-- - sort_order: int (排序，默认0)
-- - status: char(1) (状态，0正常 1停用，默认0)
-- - create_by: varchar(64) (创建者)
-- - create_time: datetime (创建时间)
-- - update_by: varchar(64) (更新者)
-- - update_time: datetime (更新时间)
-- - remark: varchar(500) (备注)

-- 索引：
-- - PRIMARY KEY (notice_id)
-- - idx_sort_order (sort_order)
-- - idx_status (status)
-- - idx_title (title)

-- ========================================
-- 测试数据示例
-- ========================================
-- 查询所有通知
-- SELECT notice_id, title, content, sort_order, status FROM mini_notice ORDER BY sort_order ASC;

-- 按标题搜索
-- SELECT * FROM mini_notice WHERE title LIKE '%关键词%';

-- 按内容搜索
-- SELECT * FROM mini_notice WHERE content LIKE '%关键词%';

-- 插入富文本通知示例
-- INSERT INTO mini_notice (title, content, sort_order, status, create_by, create_time) 
-- VALUES ('富文本通知标题', '<p><strong>粗体文字</strong></p><p><em>斜体文字</em></p><ul><li>列表项</li></ul>', 1, '0', 'admin', NOW());
