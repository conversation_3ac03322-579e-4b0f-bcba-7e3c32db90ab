import request from '@/utils/request'

// 查询指导活动报名列表
export function listRegistration(query) {
  // 添加活动类型过滤，只查询项目指导类型的报名记录
  const params = { ...query, eventType: 'guidance' }
  return request({
    url: '/miniapp/registration/list',
    method: 'get',
    params: params
  })
}

// 查询指导活动报名详细
export function getRegistration(registrationId) {
  return request({
    url: '/miniapp/registration/' + registrationId,
    method: 'get'
  })
}

// 删除指导活动报名
export function delRegistration(registrationId) {
  return request({
    url: '/miniapp/registration/' + registrationId,
    method: 'delete'
  })
}

// 导出指导活动报名
export function exportRegistration(query) {
  // 添加活动类型过滤
  const params = { ...query, eventType: 'guidance' }
  return request({
    url: '/miniapp/registration/export',
    method: 'get',
    params: params
  })
}