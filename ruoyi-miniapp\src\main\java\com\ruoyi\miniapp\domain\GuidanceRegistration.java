package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指导活动报名对象 mini_guidance_registration
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class GuidanceRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID */
    private Long registrationId;

    /** 指导活动ID */
    @Excel(name = "指导活动ID")
    private Long guidanceId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 表单数据 */
    @Excel(name = "表单数据")
    private String formData;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String guidanceTitle;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 用户手机号 */
    @Excel(name = "用户手机号")
    private String userPhone;

    public void setRegistrationId(Long registrationId) 
    {
        this.registrationId = registrationId;
    }

    public Long getRegistrationId() 
    {
        return registrationId;
    }
    public void setGuidanceId(Long guidanceId) 
    {
        this.guidanceId = guidanceId;
    }

    public Long getGuidanceId() 
    {
        return guidanceId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setFormData(String formData) 
    {
        this.formData = formData;
    }

    public String getFormData() 
    {
        return formData;
    }
    public void setRegistrationTime(Date registrationTime) 
    {
        this.registrationTime = registrationTime;
    }

    public Date getRegistrationTime() 
    {
        return registrationTime;
    }
    public void setGuidanceTitle(String guidanceTitle) 
    {
        this.guidanceTitle = guidanceTitle;
    }

    public String getGuidanceTitle() 
    {
        return guidanceTitle;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setUserPhone(String userPhone) 
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone() 
    {
        return userPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("registrationId", getRegistrationId())
            .append("guidanceId", getGuidanceId())
            .append("userId", getUserId())
            .append("formData", getFormData())
            .append("registrationTime", getRegistrationTime())
            .append("guidanceTitle", getGuidanceTitle())
            .append("userName", getUserName())
            .append("userPhone", getUserPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 