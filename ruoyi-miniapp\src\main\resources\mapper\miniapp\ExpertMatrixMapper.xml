<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.ExpertMatrixMapper">
    
    <resultMap type="ExpertMatrix" id="ExpertMatrixResult">
        <result property="id"    column="id"    />
        <result property="expertName"    column="expert_name"    />
        <result property="expertTitle"    column="expert_title"    />
        <result property="expertCompany"    column="expert_company"    />
        <result property="expertIntro"    column="expert_intro"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="expertiseFields"    column="expertise_fields"    />
        <result property="yearsExperience"    column="years_experience"    />
        <result property="notableAchievements"    column="notable_achievements"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="detailedDescription"    column="detailed_description"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="updated_at"    />
    </resultMap>

    <sql id="selectExpertMatrixVo">
        select id, expert_name, expert_title, expert_company, expert_intro, avatar_url, expertise_fields, years_experience, notable_achievements, contact_info, detailed_description, sort_order, status, created_at, updated_at from mini_expert_matrix
    </sql>

    <select id="selectExpertMatrixList" parameterType="ExpertMatrix" resultMap="ExpertMatrixResult">
        <include refid="selectExpertMatrixVo"/>
        <where>  
            <if test="expertName != null  and expertName != ''"> and expert_name like concat('%', #{expertName}, '%')</if>
            <if test="expertTitle != null  and expertTitle != ''"> and expert_title like concat('%', #{expertTitle}, '%')</if>
            <if test="expertCompany != null  and expertCompany != ''"> and expert_company like concat('%', #{expertCompany}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by sort_order asc, created_at desc
    </select>
    
    <select id="selectExpertMatrixById" parameterType="Long" resultMap="ExpertMatrixResult">
        <include refid="selectExpertMatrixVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExpertMatrix" parameterType="ExpertMatrix" useGeneratedKeys="true" keyProperty="id">
        insert into mini_expert_matrix
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expertName != null">expert_name,</if>
            <if test="expertTitle != null">expert_title,</if>
            <if test="expertCompany != null">expert_company,</if>
            <if test="expertIntro != null">expert_intro,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="expertiseFields != null">expertise_fields,</if>
            <if test="yearsExperience != null">years_experience,</if>
            <if test="notableAchievements != null">notable_achievements,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="detailedDescription != null">detailed_description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expertName != null">#{expertName},</if>
            <if test="expertTitle != null">#{expertTitle},</if>
            <if test="expertCompany != null">#{expertCompany},</if>
            <if test="expertIntro != null">#{expertIntro},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="expertiseFields != null">#{expertiseFields},</if>
            <if test="yearsExperience != null">#{yearsExperience},</if>
            <if test="notableAchievements != null">#{notableAchievements},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="detailedDescription != null">#{detailedDescription},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateExpertMatrix" parameterType="ExpertMatrix">
        update mini_expert_matrix
        <trim prefix="SET" suffixOverrides=",">
            <if test="expertName != null">expert_name = #{expertName},</if>
            <if test="expertTitle != null">expert_title = #{expertTitle},</if>
            <if test="expertCompany != null">expert_company = #{expertCompany},</if>
            <if test="expertIntro != null">expert_intro = #{expertIntro},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="expertiseFields != null">expertise_fields = #{expertiseFields},</if>
            <if test="yearsExperience != null">years_experience = #{yearsExperience},</if>
            <if test="notableAchievements != null">notable_achievements = #{notableAchievements},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="detailedDescription != null">detailed_description = #{detailedDescription},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpertMatrixById" parameterType="Long">
        delete from mini_expert_matrix where id = #{id}
    </delete>

    <delete id="deleteExpertMatrixByIds" parameterType="String">
        delete from mini_expert_matrix where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 