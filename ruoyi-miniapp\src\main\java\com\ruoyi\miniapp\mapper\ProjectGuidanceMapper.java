package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.ProjectGuidance;

/**
 * 项目指导活动Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProjectGuidanceMapper 
{
    /**
     * 查询项目指导活动
     * 
     * @param guidanceId 项目指导活动主键
     * @return 项目指导活动
     */
    public ProjectGuidance selectProjectGuidanceByGuidanceId(Long guidanceId);

    /**
     * 查询项目指导活动列表
     * 
     * @param projectGuidance 项目指导活动
     * @return 项目指导活动集合
     */
    public List<ProjectGuidance> selectProjectGuidanceList(ProjectGuidance projectGuidance);

    /**
     * 新增项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    public int insertProjectGuidance(ProjectGuidance projectGuidance);

    /**
     * 修改项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    public int updateProjectGuidance(ProjectGuidance projectGuidance);

    /**
     * 删除项目指导活动
     * 
     * @param guidanceId 项目指导活动主键
     * @return 结果
     */
    public int deleteProjectGuidanceByGuidanceId(Long guidanceId);

    /**
     * 批量删除项目指导活动
     * 
     * @param guidanceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectGuidanceByGuidanceIds(Long[] guidanceIds);
} 