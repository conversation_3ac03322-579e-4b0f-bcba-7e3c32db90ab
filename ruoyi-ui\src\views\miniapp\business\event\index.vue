<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="活动标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入活动地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="活动状态" clearable>
          <el-option label="正常" value="0"/>
          <el-option label="停用" value="1"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:event:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:event:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:event:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="eventList" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="eventId" width="80" />
      <el-table-column label="活动标题" align="center" prop="title" show-overflow-tooltip />
      <el-table-column label="活动描述" align="center" prop="description" show-overflow-tooltip />
      <el-table-column label="封面图片" align="center" prop="coverImage" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.coverImage" :width="50" :height="50" v-if="scope.row.coverImage"/>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" prop="location" show-overflow-tooltip />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:event:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:event:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入活动标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入活动地点" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="活动描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入活动描述" :rows="3" />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImage">
          <image-upload v-model="form.coverImage"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报名截止时间" prop="registrationDeadline">
          <el-date-picker
            v-model="form.registrationDeadline"
            type="datetime"
            placeholder="选择报名截止时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" :max="9999" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 报名表单字段配置 -->
        <el-form-item label="报名表单配置" prop="formFields">
          <div class="form-fields-config">
            <!-- 工具栏 -->
            <div class="form-fields-toolbar">
              <div class="toolbar-left">
                <el-button type="primary" size="small" @click="addFormField" icon="el-icon-plus">
                  添加字段
                </el-button>
                <el-dropdown @command="handleTemplateCommand" size="small">
                  <el-button size="small">
                    预设模板<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="basic">基础信息模板</el-dropdown-item>
                    <el-dropdown-item command="tech">技术分享模板</el-dropdown-item>
                    <el-dropdown-item command="sport">体育活动模板</el-dropdown-item>
                    <el-dropdown-item command="clear">清空所有字段</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="toolbar-right">
                <el-button size="small" @click="previewForm" icon="el-icon-view">
                  预览表单
                </el-button>
              </div>
            </div>

            <!-- 字段配置区域 -->
            <div class="form-fields-list" v-if="formFieldsList.length > 0">
              <div 
                v-for="(field, index) in formFieldsList" 
                :key="index"
                class="form-field-item"
              >
                <div class="field-header">
                  <div class="field-info">
                    <i :class="getFieldIcon(field.type)" class="field-icon"></i>
                    <span class="field-label">{{ field.label || '未命名字段' }}</span>
                    <el-tag v-if="field.required" size="mini" type="danger">必填</el-tag>
                    <el-tag v-else size="mini" type="info">选填</el-tag>
                  </div>
                  <div class="field-actions">
                    <el-button 
                      type="text" 
                      size="mini" 
                      @click="moveField(index, -1)"
                      :disabled="index === 0"
                      icon="el-icon-arrow-up"
                    ></el-button>
                    <el-button 
                      type="text" 
                      size="mini" 
                      @click="moveField(index, 1)"
                      :disabled="index === formFieldsList.length - 1"
                      icon="el-icon-arrow-down"
                    ></el-button>
                    <el-button 
                      type="text" 
                      size="mini" 
                      @click="removeFormField(index)"
                      icon="el-icon-delete"
                      class="danger-btn"
                    ></el-button>
                  </div>
                </div>
                
                <div class="field-content">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <div class="field-item">
                        <label>字段标签</label>
                        <el-input 
                          v-model="field.label" 
                          placeholder="显示给用户的标签，如：姓名、电话等"
                          size="small"
                          @input="updateFieldName(field, $event)"
                        />
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="field-item">
                        <label>字段类型</label>
                        <el-select v-model="field.type" placeholder="选择类型" size="small" style="width: 100%">
                          <el-option label="📝 文本输入" value="input" />
                          <el-option label="📄 多行文本" value="textarea" />
                          <el-option label="🔢 数字输入" value="number" />
                          <el-option label="📧 邮箱" value="email" />
                          <el-option label="📞 电话" value="tel" />
                          <el-option label="🔘 单选" value="radio" />
                          <el-option label="☑️ 多选" value="checkbox" />
                          <el-option label="📋 下拉选择" value="select" />
                          <el-option label="📅 日期" value="date" />
                        </el-select>
                      </div>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="10" style="margin-top: 10px;">
                    <el-col :span="4">
                      <div class="field-item">
                        <label>是否必填</label>
                        <el-switch v-model="field.required" />
                      </div>
                    </el-col>
                    <el-col :span="20" v-if="['radio', 'checkbox', 'select'].includes(field.type)">
                      <div class="field-item">
                        <label>选项配置</label>
                        <el-input 
                          v-model="field.options" 
                          placeholder="用逗号分隔选项，如：选项1,选项2,选项3"
                          size="small"
                        />
                        <div class="options-preview" v-if="field.options">
                          <el-tag 
                            v-for="(option, optIndex) in field.options.split(',')" 
                            :key="optIndex"
                            size="mini"
                            style="margin-right: 5px; margin-top: 5px;"
                          >
                            {{ option.trim() }}
                          </el-tag>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" v-else>
              <i class="el-icon-document-add"></i>
              <p>暂无表单字段，点击"添加字段"开始配置</p>
            </div>
          </div>
        </el-form-item>

        <!-- 表单预览对话框 -->
        <el-dialog title="表单预览" :visible.sync="previewDialogVisible" width="600px" append-to-body>
          <div class="form-preview">
            <div class="preview-header">
              <h3>{{ form.title || '活动报名表' }}</h3>
              <p>{{ form.description || '请填写以下信息完成报名' }}</p>
            </div>
            <div class="preview-form">
              <div v-for="(field, index) in formFieldsList" :key="index" class="preview-field">
                <label class="preview-label">
                  {{ field.label }}
                  <span v-if="field.required" class="required">*</span>
                </label>
                <div class="preview-input">
                  <el-input 
                    v-if="field.type === 'input' || field.type === 'email' || field.type === 'tel'"
                    :placeholder="'请输入' + field.label"
                    size="small"
                    disabled
                  />
                  <el-input 
                    v-else-if="field.type === 'textarea'"
                    type="textarea"
                    :placeholder="'请输入' + field.label"
                    size="small"
                    disabled
                  />
                  <el-input-number 
                    v-else-if="field.type === 'number'"
                    :placeholder="'请输入' + field.label"
                    size="small"
                    disabled
                  />
                  <el-radio-group v-else-if="field.type === 'radio'" disabled>
                    <el-radio 
                      v-for="(option, optIndex) in field.options.split(',')" 
                      :key="optIndex"
                      :label="option.trim()"
                    >
                      {{ option.trim() }}
                    </el-radio>
                  </el-radio-group>
                  <el-checkbox-group v-else-if="field.type === 'checkbox'" disabled>
                    <el-checkbox 
                      v-for="(option, optIndex) in field.options.split(',')" 
                      :key="optIndex"
                      :label="option.trim()"
                    >
                      {{ option.trim() }}
                    </el-checkbox>
                  </el-checkbox-group>
                  <el-select v-else-if="field.type === 'select'" :placeholder="'请选择' + field.label" size="small" disabled>
                    <el-option 
                      v-for="(option, optIndex) in field.options.split(',')" 
                      :key="optIndex"
                      :label="option.trim()"
                      :value="option.trim()"
                    />
                  </el-select>
                  <el-date-picker 
                    v-else-if="field.type === 'date'"
                    type="date"
                    :placeholder="'请选择' + field.label"
                    size="small"
                    disabled
                  />
                </div>
              </div>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="previewDialogVisible = false">关闭</el-button>
          </div>
        </el-dialog>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEvent, getEvent, delEvent, addEvent, updateEvent } from "@/api/miniapp/event";

export default {
  name: "MiniEvent",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动报名表格数据
      eventList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        location: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单字段配置列表
      formFieldsList: [],
      // 表单预览对话框
      previewDialogVisible: false,
      // 表单校验
      rules: {
        title: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        coverImage: [
          { required: true, message: "封面图片不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询活动报名列表 */
    getList() {
      this.loading = true;
      listEvent(this.queryParams).then(response => {
        this.eventList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取活动列表失败:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        eventId: null,
        title: null,
        coverImage: null,
        description: null,
        location: null,
        startTime: null,
        endTime: null,
        registrationDeadline: null,
        formFields: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.formFieldsList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.eventId);
      this.single = selection.length!==1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加活动报名";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const eventId = row.eventId || this.ids;
      getEvent(eventId).then(response => {
        this.form = response.data;
        // 解析表单字段配置
        if (this.form.formFields) {
          try {
            this.formFieldsList = JSON.parse(this.form.formFields);
          } catch (e) {
            this.formFieldsList = [];
          }
        }
        this.open = true;
        this.title = "修改活动报名";
      }).catch(error => {
        console.error('获取活动详情失败:', error);
        this.$modal.msgError('获取活动详情失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 将表单字段配置转换为JSON字符串
          this.form.formFields = JSON.stringify(this.formFieldsList);
          
          if (this.form.eventId != null) {
            updateEvent(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改活动失败:', error);
              this.$modal.msgError('修改活动失败');
            });
          } else {
            addEvent(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增活动失败:', error);
              this.$modal.msgError('新增活动失败');
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const eventIds = row.eventId || this.ids;
      this.$modal.confirm('是否确认删除活动报名编号为"' + eventIds + '"的数据项？').then(function() {
        return delEvent(eventIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/event/export', {
        ...this.queryParams
      }, `event_${new Date().getTime()}.xlsx`)
    },
    // 添加表单字段
    addFormField() {
      this.formFieldsList.push({
        label: '',
        name: '',
        type: 'input',
        required: false,
        options: ''
      });
    },
    // 删除表单字段
    removeFormField(index) {
      this.formFieldsList.splice(index, 1);
    },
    // 获取字段图标
    getFieldIcon(type) {
      const iconMap = {
        input: 'el-icon-edit',
        textarea: 'el-icon-document',
        number: 'el-icon-s-data',
        email: 'el-icon-message',
        tel: 'el-icon-phone',
        radio: 'el-icon-circle-check',
        checkbox: 'el-icon-check',
        select: 'el-icon-arrow-down',
        date: 'el-icon-date'
      };
      return iconMap[type] || 'el-icon-edit';
    },
    // 自动生成字段名称
    updateFieldName(field, label) {
      // 常用字段的映射表
      const nameMap = {
        '姓名': 'name',
        '真实姓名': 'real_name',
        '联系电话': 'phone',
        '手机号': 'mobile',
        '电话': 'phone',
        '邮箱': 'email',
        '邮箱地址': 'email',
        '年龄': 'age',
        '性别': 'gender',
        '地址': 'address',
        '详细地址': 'address',
        '公司': 'company',
        '所在公司': 'company',
        '职位': 'position',
        '工作岗位': 'position',
        '备注': 'remark',
        '说明': 'remark',
        '身份证': 'id_card',
        '身份证号': 'id_card',
        '学校': 'school',
        '专业': 'major',
        '班级': 'class',
        '学号': 'student_id',
        '工作经验': 'experience',
        '工作年限': 'work_years',
        '紧急联系人': 'emergency_contact',
        '紧急联系人电话': 'emergency_phone',
        '个人简介': 'introduction',
        '自我介绍': 'introduction',
        '期望收获': 'expectations',
        '参与原因': 'reason',
        '特长技能': 'skills',
        '兴趣爱好': 'hobbies',
        '技术方向': 'tech_direction',
        '出生日期': 'birthday',
        '参赛项目': 'events',
        '是否有运动损伤史': 'injury_history',
        '特殊说明': 'special_notes'
      };
      
      // 优先使用映射表，否则生成简单的英文名
      if (nameMap[label]) {
        field.name = nameMap[label];
      } else {
        // 去除特殊字符，转为小写，用下划线连接
        field.name = label
          .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '_') // 替换特殊字符为下划线
          .replace(/_{2,}/g, '_') // 多个下划线合并为一个
          .replace(/^_|_$/g, '') // 去除首尾下划线
          .toLowerCase() || 'field_' + Date.now();
      }
    },
    // 移动字段位置
    moveField(index, direction) {
      const newIndex = index + direction;
      if (newIndex >= 0 && newIndex < this.formFieldsList.length) {
        const item = this.formFieldsList.splice(index, 1)[0];
        this.formFieldsList.splice(newIndex, 0, item);
      }
    },
    // 处理模板命令
    handleTemplateCommand(command) {
      if (command === 'clear') {
        this.$confirm('确定要清空所有字段吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.formFieldsList = [];
          this.$message.success('已清空所有字段');
        });
        return;
      }
      
      const templates = {
        basic: [
          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },
          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }
        ],
        tech: [
          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },
          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },
          { label: '所在公司', name: 'company', type: 'input', required: false, options: '' },
          { label: '技术方向', name: 'tech_direction', type: 'select', required: true, options: '前端开发,后端开发,移动开发,人工智能,数据分析' },
          { label: '工作年限', name: 'experience', type: 'radio', required: true, options: '1年以内,1-3年,3-5年,5年以上' },
          { label: '期望收获', name: 'expectations', type: 'textarea', required: false, options: '' }
        ],
        sport: [
          { label: '参赛者姓名', name: 'player_name', type: 'input', required: true, options: '' },
          { label: '身份证号', name: 'id_card', type: 'input', required: true, options: '' },
          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },
          { label: '紧急联系人', name: 'emergency_contact', type: 'input', required: true, options: '' },
          { label: '紧急联系人电话', name: 'emergency_phone', type: 'tel', required: true, options: '' },
          { label: '参赛项目', name: 'events', type: 'checkbox', required: true, options: '100米,200米,800米,跳远,跳高' },
          { label: '是否有运动损伤史', name: 'injury_history', type: 'radio', required: true, options: '是,否' },
          { label: '特殊说明', name: 'special_notes', type: 'textarea', required: false, options: '' }
        ]
      };
      
      if (templates[command]) {
        this.formFieldsList = templates[command];
        this.$message.success('模板应用成功');
      }
    },
    // 预览表单
    previewForm() {
      if (this.formFieldsList.length === 0) {
        this.$message.warning('请先添加表单字段');
        return;
      }
      this.previewDialogVisible = true;
    }
  }
};
</script>

<style scoped>
.form-fields-config {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafbfc;
  overflow: hidden;
}

.form-fields-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.form-fields-list {
  padding: 15px;
}

.form-field-item {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.form-field-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-icon {
  font-size: 16px;
  color: #409eff;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-actions {
  display: flex;
  gap: 4px;
}

.danger-btn {
  color: #f56c6c;
}

.danger-btn:hover {
  color: #f56c6c;
}

.field-content {
  padding: 16px;
}

.field-item {
  margin-bottom: 12px;
}

.field-item:last-child {
  margin-bottom: 0;
}

.field-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.options-preview {
  margin-top: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 表单预览样式 */
.form-preview {
  background-color: #fafbfc;
  border-radius: 8px;
  padding: 20px;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-form {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
}

.preview-field {
  margin-bottom: 20px;
}

.preview-field:last-child {
  margin-bottom: 0;
}

.preview-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.preview-label .required {
  color: #f56c6c;
  margin-left: 4px;
}

.preview-input {
  width: 100%;
}

.preview-input .el-input,
.preview-input .el-select,
.preview-input .el-date-editor {
  width: 100%;
}

.preview-input .el-radio-group,
.preview-input .el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-input .el-radio,
.preview-input .el-checkbox {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-fields-toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .field-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .field-info {
    justify-content: center;
  }
}
</style> 