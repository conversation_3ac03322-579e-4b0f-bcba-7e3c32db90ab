package com.ruoyi.miniapp.service;

import com.ruoyi.miniapp.domain.dto.WechatConfigDTO;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 微信公众号活动同步Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWechatActivityService
{
    /**
     * 获取微信公众号配置列表
     * 
     * @return 配置列表
     */
    List<WechatConfigDTO> getWechatConfigList();

    /**
     * 保存微信公众号配置
     * 
     * @param config 配置信息
     * @return 结果
     */
    AjaxResult saveWechatConfig(WechatConfigDTO config);

    /**
     * 删除微信公众号配置
     * 
     * @param configId 配置ID
     * @return 结果
     */
    AjaxResult deleteWechatConfig(String configId);

    /**
     * 测试微信公众号配置
     * 
     * @param config 配置信息
     * @return 结果
     */
    AjaxResult testWechatConfig(WechatConfigDTO config);

    /**
     * 从微信公众号同步活动
     * 
     * @return 结果
     */
    AjaxResult syncActivitiesFromWechat();

    /**
     * 从指定微信公众号同步活动
     * 
     * @param configId 配置ID
     * @return 结果
     */
    AjaxResult syncActivitiesFromSingleWechat(String configId);

    /**
     * 获取微信AccessToken
     * 
     * @param appId AppID
     * @param appSecret AppSecret
     * @return AccessToken
     */
    String getAccessToken(String appId, String appSecret);
}
