package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniEnterprise;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 企业管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface MiniEnterpriseMapper 
{
    /**
     * 查询企业管理
     * 
     * @param enterpriseId 企业管理主键
     * @return 企业管理
     */
    public MiniEnterprise selectMiniEnterpriseByEnterpriseId(Long enterpriseId);

    /**
     * 查询企业管理列表
     * 
     * @param miniEnterprise 企业管理
     * @return 企业管理集合
     */
    public List<MiniEnterprise> selectMiniEnterpriseList(MiniEnterprise miniEnterprise);

    /**
     * 新增企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    public int insertMiniEnterprise(MiniEnterprise miniEnterprise);

    /**
     * 修改企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    public int updateMiniEnterprise(MiniEnterprise miniEnterprise);

    /**
     * 删除企业管理
     * 
     * @param enterpriseId 企业管理主键
     * @return 结果
     */
    public int deleteMiniEnterpriseByEnterpriseId(Long enterpriseId);

    /**
     * 批量删除企业管理
     * 
     * @param enterpriseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniEnterpriseByEnterpriseIds(Long[] enterpriseIds);

    /**
     * 查询企业产业关联信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业产业关联列表
     */
    public List<MiniEnterprise.MiniEnterpriseIndustry> selectEnterpriseIndustryByEnterpriseId(Long enterpriseId);

    /**
     * 删除企业产业关联
     * 
     * @param enterpriseId 企业ID
     * @return 结果
     */
    public int deleteMiniEnterpriseIndustryByEnterpriseId(Long enterpriseId);

    /**
     * 批量删除企业产业关联
     * 
     * @param enterpriseIds 企业ID集合
     * @return 结果
     */
    public int deleteMiniEnterpriseIndustryByEnterpriseIds(Long[] enterpriseIds);

    /**
     * 批量插入企业产业关联
     * 
     * @param industryList 企业产业关联列表
     * @return 结果
     */
    public int batchInsertEnterpriseIndustry(List<MiniEnterprise.MiniEnterpriseIndustry> industryList);

    /**
     * 查询指定行业类型是否被企业绑定
     * @param industryTypeIds 行业类型ID数组
     * @return 绑定数量
     */
    public int countEnterpriseBindIndustryTypes(@Param("ids") Long[] industryTypeIds);


} 