<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniParkMapper">
    
    <resultMap type="MiniPark" id="MiniParkResult">
        <result property="parkId"    column="park_id"    />
        <result property="parkName"    column="park_name"    />
        <result property="parkCode"    column="park_code"    />
        <result property="description"    column="description"    />
        <result property="content"    column="content"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="address"    column="address"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="websiteUrl"    column="website_url"    />
        <result property="areaSize"    column="area_size"    />
        <result property="establishedDate"    column="established_date"    />
        <result property="parkType"    column="park_type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniParkVo">
        select park_id, park_name, park_code, description, content, cover_image, address, contact_phone, contact_email, website_url, area_size, established_date, park_type, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_park
    </sql>

    <select id="selectMiniParkList" parameterType="MiniPark" resultMap="MiniParkResult">
        <include refid="selectMiniParkVo"/>
        <where>
            <if test="parkName != null  and parkName != ''"> and park_name like concat('%', #{parkName}, '%')</if>
            <if test="parkCode != null  and parkCode != ''"> and park_code like concat('%', #{parkCode}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="parkType != null  and parkType != ''"> and park_type = #{parkType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniParkByParkId" parameterType="Long" resultMap="MiniParkResult">
        <include refid="selectMiniParkVo"/>
        where park_id = #{parkId}
    </select>

    <select id="selectEnabledMiniParkList" resultMap="MiniParkResult">
        <include refid="selectMiniParkVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectRecommendedMiniParkList" resultMap="MiniParkResult">
        <include refid="selectMiniParkVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
        limit 10
    </select>
        
    <insert id="insertMiniPark" parameterType="MiniPark" useGeneratedKeys="true" keyProperty="parkId">
        insert into mini_park
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parkName != null and parkName != ''">park_name,</if>
            <if test="parkCode != null and parkCode != ''">park_code,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email,</if>
            <if test="websiteUrl != null and websiteUrl != ''">website_url,</if>
            <if test="areaSize != null and areaSize != ''">area_size,</if>
            <if test="establishedDate != null">established_date,</if>
            <if test="parkType != null and parkType != ''">park_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parkName != null and parkName != ''">#{parkName},</if>
            <if test="parkCode != null and parkCode != ''">#{parkCode},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">#{contactEmail},</if>
            <if test="websiteUrl != null and websiteUrl != ''">#{websiteUrl},</if>
            <if test="areaSize != null and areaSize != ''">#{areaSize},</if>
            <if test="establishedDate != null">#{establishedDate},</if>
            <if test="parkType != null and parkType != ''">#{parkType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateMiniPark" parameterType="MiniPark">
        update mini_park
        <trim prefix="SET" suffixOverrides=",">
            <if test="parkName != null and parkName != ''">park_name = #{parkName},</if>
            <if test="parkCode != null and parkCode != ''">park_code = #{parkCode},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="coverImage != null and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="websiteUrl != null and websiteUrl != ''">website_url = #{websiteUrl},</if>
            <if test="areaSize != null and areaSize != ''">area_size = #{areaSize},</if>
            <if test="establishedDate != null">established_date = #{establishedDate},</if>
            <if test="parkType != null and parkType != ''">park_type = #{parkType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where park_id = #{parkId}
    </update>

    <delete id="deleteMiniParkByParkId" parameterType="Long">
        delete from mini_park where park_id = #{parkId}
    </delete>

    <delete id="deleteMiniParkByParkIds" parameterType="String">
        delete from mini_park where park_id in 
        <foreach item="parkId" collection="array" open="(" separator="," close=")">
            #{parkId}
        </foreach>
    </delete>
</mapper>
