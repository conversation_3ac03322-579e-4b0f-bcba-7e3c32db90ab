package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.miniapp.domain.MiniBarrage;

/**
 * 弹幕Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Mapper
public interface MiniBarrageMapper 
{
    /**
     * 查询弹幕
     * 
     * @param barrageId 弹幕主键
     * @return 弹幕
     */
    public MiniBarrage selectMiniBarrageByBarrageId(Long barrageId);

    /**
     * 查询弹幕列表
     * 
     * @param miniBarrage 弹幕
     * @return 弹幕集合
     */
    public List<MiniBarrage> selectMiniBarrageList(MiniBarrage miniBarrage);

    /**
     * 新增弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    public int insertMiniBarrage(MiniBarrage miniBarrage);

    /**
     * 修改弹幕
     * 
     * @param miniBarrage 弹幕
     * @return 结果
     */
    public int updateMiniBarrage(MiniBarrage miniBarrage);

    /**
     * 删除弹幕
     * 
     * @param barrageId 弹幕主键
     * @return 结果
     */
    public int deleteMiniBarrageByBarrageId(Long barrageId);

    /**
     * 批量删除弹幕
     * 
     * @param barrageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniBarrageByBarrageIds(Long[] barrageIds);

    /**
     * 查询审核通过的弹幕列表
     * 
     * @return 弹幕集合
     */
    public List<MiniBarrage> selectApprovedBarrageList();

    /**
     * 审核弹幕
     * 
     * @param barrageId 弹幕ID
     * @param auditStatus 审核状态
     * @param auditBy 审核人
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditBarrage(@Param("barrageId") Long barrageId, 
                           @Param("auditStatus") String auditStatus, 
                           @Param("auditBy") String auditBy, 
                           @Param("auditRemark") String auditRemark);

    /**
     * 查询已审核的弹幕列表
     * 
     * @return 弹幕集合
     */
    public List<MiniBarrage> selectApprovedMiniBarrageList();
} 