package com.ruoyi.miniapp.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 微信文章响应DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel("微信文章响应")
public class WechatArticleResponseDTO
{
    /** 总数 */
    @ApiModelProperty("总数")
    @JsonProperty("total_count")
    private Integer totalCount;

    /** 本次返回数量 */
    @ApiModelProperty("本次返回数量")
    @JsonProperty("item_count")
    private Integer itemCount;

    /** 文章列表 */
    @ApiModelProperty("文章列表")
    private List<WechatArticleDTO> item;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public List<WechatArticleDTO> getItem() {
        return item;
    }

    public void setItem(List<WechatArticleDTO> item) {
        this.item = item;
    }
}
