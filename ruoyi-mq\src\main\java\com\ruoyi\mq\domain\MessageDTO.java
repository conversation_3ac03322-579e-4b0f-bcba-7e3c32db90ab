package com.ruoyi.mq.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消息数据传输对象
 * 
 * <AUTHOR>
 */
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private String messageId;

    /** 消息内容 */
    private String content;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 附加数据 */
    private Object data;

    public MessageDTO() {
        this.createTime = LocalDateTime.now();
    }

    public MessageDTO(String content) {
        this.content = content;
        this.createTime = LocalDateTime.now();
    }

    public MessageDTO(String content, Object data) {
        this.content = content;
        this.data = data;
        this.createTime = LocalDateTime.now();
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "MessageDTO{" +
                "messageId='" + messageId + '\'' +
                ", content='" + content + '\'' +
                ", createTime=" + createTime +
                ", data=" + data +
                '}';
    }
} 