package com.ruoyi.miniapp.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 小程序登录响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@ApiModel("小程序登录响应")
public class MiniappLoginResponse
{
    @ApiModelProperty("JWT令牌")
    private String token;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("微信昵称")
    private String weixinNickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("微信头像")
    private String weixinAvatar;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("手机号码")
    private String phonenumber;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("总积分")
    private Integer totalPoints;

    @ApiModelProperty("是否新用户")
    private Boolean isNewUser;

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getWeixinNickname()
    {
        return weixinNickname;
    }

    public void setWeixinNickname(String weixinNickname)
    {
        this.weixinNickname = weixinNickname;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getWeixinAvatar()
    {
        return weixinAvatar;
    }

    public void setWeixinAvatar(String weixinAvatar)
    {
        this.weixinAvatar = weixinAvatar;
    }

    public String getRealName()
    {
        return realName;
    }

    public void setRealName(String realName)
    {
        this.realName = realName;
    }

    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public Integer getTotalPoints()
    {
        return totalPoints;
    }

    public void setTotalPoints(Integer totalPoints)
    {
        this.totalPoints = totalPoints;
    }

    public Boolean getIsNewUser()
    {
        return isNewUser;
    }

    public void setIsNewUser(Boolean isNewUser)
    {
        this.isNewUser = isNewUser;
    }

    @Override
    public String toString()
    {
        return "MiniappLoginResponse{" +
                "token='" + token + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", weixinNickname='" + weixinNickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", weixinAvatar='" + weixinAvatar + '\'' +
                ", realName='" + realName + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                ", sex='" + sex + '\'' +
                ", totalPoints=" + totalPoints +
                ", isNewUser=" + isNewUser +
                '}';
    }
}
