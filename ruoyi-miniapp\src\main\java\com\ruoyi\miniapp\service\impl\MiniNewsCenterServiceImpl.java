package com.ruoyi.miniapp.service.impl;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.ruoyi.miniapp.domain.MiniNewsCenter;
import com.ruoyi.miniapp.mapper.MiniNewsCenterMapper;
import com.ruoyi.miniapp.service.IMiniNewsCenterService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.common.utils.StringUtils;

/**
 * 新闻中心Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@Service
public class MiniNewsCenterServiceImpl implements IMiniNewsCenterService 
{
    private static final Logger logger = LoggerFactory.getLogger(MiniNewsCenterServiceImpl.class);

    @Autowired
    private MiniNewsCenterMapper miniNewsCenterMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ISysConfigService configService;

    // 微信公众号API配置
    private static final String WECHAT_API_BASE_URL = "https://api.weixin.qq.com/cgi-bin/freepublish/batchget";

    /**
     * 获取微信公众号AccessToken
     * 每次调用都重新获取最新的token，不依赖缓存
     *
     * @return AccessToken
     */
    private String getAccessToken() {
        logger.info("=== 开始获取最新的微信AccessToken ===");

        try {
            // 使用全局配置获取最新Token
            logger.info("使用全局微信公众号配置获取最新Token");
            String globalAppid = configService.selectConfigByKey("news.wechat.appid");
            String globalSecret = configService.selectConfigByKey("news.wechat.secret");

            if (StringUtils.isNotEmpty(globalAppid) && StringUtils.isNotEmpty(globalSecret)) {
                return getFreshAccessToken(globalAppid, globalSecret);
            } else {
                throw new RuntimeException("微信公众号配置不完整，请检查appid和secret配置");
            }

        } catch (Exception e) {
            logger.error("获取微信AccessToken失败", e);
            throw new RuntimeException("无法获取有效的微信AccessToken: " + e.getMessage(), e);
        }
    }

    /**
     * 构建微信API URL
     * 
     * @return API URL
     */
    private String buildWechatApiUrl() {
        return WECHAT_API_BASE_URL + "?access_token=" + getAccessToken();
    }

    /**
     * 查询新闻中心
     *
     * @param id 新闻中心主键
     * @return 新闻中心
     */
    @Override
    public MiniNewsCenter selectMiniNewsCenterById(Long id)
    {
        return miniNewsCenterMapper.selectMiniNewsCenterById(id);
    }

    /**
     * 查询新闻中心列表
     *
     * @param miniNewsCenter 新闻中心
     * @return 新闻中心
     */
    @Override
    public List<MiniNewsCenter> selectMiniNewsCenterList(MiniNewsCenter miniNewsCenter)
    {
        return miniNewsCenterMapper.selectMiniNewsCenterList(miniNewsCenter);
    }

    /**
     * 新增新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    @Override
    public int insertMiniNewsCenter(MiniNewsCenter miniNewsCenter)
    {
        return miniNewsCenterMapper.insertMiniNewsCenter(miniNewsCenter);
    }

    /**
     * 修改新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    @Override
    public int updateMiniNewsCenter(MiniNewsCenter miniNewsCenter)
    {
        return miniNewsCenterMapper.updateMiniNewsCenter(miniNewsCenter);
    }

    /**
     * 批量删除新闻中心
     * 
     * @param ids 需要删除的新闻中心主键
     * @return 结果
     */
    @Override
    public int deleteMiniNewsCenterByIds(Long[] ids)
    {
        return miniNewsCenterMapper.deleteMiniNewsCenterByIds(ids);
    }

    /**
     * 删除新闻中心信息
     * 
     * @param id 新闻中心主键
     * @return 结果
     */
    @Override
    public int deleteMiniNewsCenterById(Long id)
    {
        return miniNewsCenterMapper.deleteMiniNewsCenterById(id);
    }

    /**
     * 从微信公众号同步文章
     * 
     * @return 同步结果
     */
    @Override
    public String syncFromWechat()
    {
        return syncFromWechat(20, 0);
    }

    /**
     * 手动同步指定数量的文章
     * 
     * @param count 同步数量
     * @param offset 偏移量
     * @return 同步结果
     */
    @Override
    public String syncFromWechat(int count, int offset)
    {
        logger.info("开始同步微信公众号文章，数量：{}，偏移：{}", count, offset);
        
        try {
            // 构造请求体
            String requestBody = String.format("{\"offset\":%d,\"count\":%d}", offset, count);
            
            // 设置请求头，明确指定UTF-8编码
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(new MediaType("application", "json", java.nio.charset.StandardCharsets.UTF_8));
            headers.set("Accept", "application/json;charset=UTF-8");
            headers.set("Accept-Charset", "UTF-8");
            
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            
            // 发起请求
            ResponseEntity<String> response = restTemplate.exchange(
                buildWechatApiUrl(), 
                HttpMethod.POST, 
                request, 
                String.class
            );
            
            // 解析响应，确保UTF-8编码正确处理
            String responseBody = response.getBody();
            if (responseBody == null) {
                logger.error("微信API响应为空");
                return "同步失败：API响应为空";
            }

            JsonNode rootNode = objectMapper.readTree(responseBody);
            logger.info("微信API响应：{}", responseBody);
            
            // 检查是否有错误（失败的响应才有errcode）
            JsonNode errcodeNode = rootNode.get("errcode");
            if (errcodeNode != null) {
                int errcode = errcodeNode.asInt();
                if (errcode != 0) {
                    String errmsg = rootNode.get("errmsg").asText();
                    logger.error("微信API返回错误：{} - {}", errcode, errmsg);
                    return "同步失败：" + errmsg;
                }
            }
            
            // 获取总数和文章列表
            JsonNode totalCountNode = rootNode.get("total_count");
            JsonNode itemNode = rootNode.get("item");
            
            if (totalCountNode == null || itemNode == null) {
                logger.warn("微信API返回的数据格式不正确：缺少total_count或item字段");
                return "同步失败：返回数据格式不正确";
            }
            
            int totalCount = totalCountNode.asInt();
            logger.info("微信公众号共有{}篇文章", totalCount);
            
            if (!itemNode.isArray()) {
                logger.warn("微信API返回的item不是数组格式");
                return "同步失败：返回数据格式不正确";
            }
            
            List<MiniNewsCenter> newsList = new ArrayList<>();
            int successCount = 0;
            
            for (JsonNode article : itemNode) {
                try {
                    // 获取文章ID
                    String articleId = article.get("article_id").asText();

                    // 获取文章内容
                    JsonNode contentNode = article.get("content");
                    if (contentNode == null) {
                        logger.warn("文章{}缺少content字段", articleId);
                        continue;
                    }
                    
                    // 获取创建时间
                    long createTime = contentNode.get("create_time").asLong();
                    
                    // 获取文章详情
                    JsonNode newsItem = contentNode.get("news_item");
                    if (newsItem == null || !newsItem.isArray() || newsItem.size() == 0) {
                        logger.warn("文章{}缺少news_item字段或为空", articleId);
                        continue;
                    }
                    
                    JsonNode firstItem = newsItem.get(0); // 取第一篇文章

                    MiniNewsCenter newsCenter = new MiniNewsCenter();

                    // 安全获取字段值，处理可能的null值和编码问题
                    JsonNode titleNode = firstItem.get("title");
                    JsonNode authorNode = firstItem.get("author");
                    JsonNode digestNode = firstItem.get("digest");
                    JsonNode thumbUrlNode = firstItem.get("thumb_url");
                    JsonNode urlNode = firstItem.get("url");

                    // 调试日志：输出完整的文章项信息
                    logger.debug("文章项完整信息：{}", firstItem.toString());

                    // 设置字段值，确保UTF-8编码正确处理
                    String title = titleNode != null && !titleNode.isNull() ? titleNode.asText("") : "";
                    String author = authorNode != null && !authorNode.isNull() ? authorNode.asText("") : "";
                    String digest = digestNode != null && !digestNode.isNull() ? digestNode.asText("") : "";
                    String thumbUrl = thumbUrlNode != null && !thumbUrlNode.isNull() ? thumbUrlNode.asText("") : "";
                    String articleUrl = urlNode != null && !urlNode.isNull() ? urlNode.asText("") : "";

                    // 调试日志：输出封面图URL
                    logger.info("文章《{}》的封面图URL：{}", title, thumbUrl);

                    // 直接使用API获取的封面图URL，不再下载到本地
                    String localThumbUrl = thumbUrl;
                    logger.info("=== 处理封面图URL ===");
                    logger.info("封面图URL：{}", thumbUrl);

                    // 直接使用原始URL，前端通过 referrerpolicy="no-referrer" 处理跨域问题
                    if (StringUtils.isNotEmpty(thumbUrl)) {
                        logger.info("使用原始封面图URL：{}", thumbUrl);
                    } else {
                        logger.info("文章无封面图");
                    }

                    logger.info("最终存储的封面图URL：{}", localThumbUrl);
                    logger.info("=== 封面图处理完成 ===");

                    newsCenter.setTitle(title);
                    newsCenter.setAuthor(author);
                    newsCenter.setDigest(digest);
                    localThumbUrl = localThumbUrl.replaceFirst("^http://", "https://");
                    newsCenter.setThumbUrl(localThumbUrl); // 存储本地路径或原始URL
                    newsCenter.setWechatArticleUrl(articleUrl);
                    
                    // 使用article_id作为唯一标识
                    newsCenter.setWechatArticleId(articleId);
                    
                    newsCenter.setStatus("0"); // 默认正常状态
                    
                    // 设置创建时间和同步时间
                    newsCenter.setWechatCreateTime(new Date(createTime * 1000));
                    newsCenter.setSyncTime(new Date());
                    
                    newsList.add(newsCenter);
                    successCount++;
                    
                    logger.debug("解析文章成功：{} - {}", articleId, newsCenter.getTitle());
                    
                } catch (Exception e) {
                    logger.error("解析文章数据失败", e);
                }
            }
            
            // 批量插入或更新
            if (!newsList.isEmpty()) {
                miniNewsCenterMapper.batchInsertOrUpdate(newsList);
                logger.info("成功同步{}篇文章", successCount);
                return String.format("同步成功，共同步 %d 篇文章", successCount);
            } else {
                logger.info("没有可同步的文章");
                return "同步完成，但没有可同步的文章";
            }
            
        } catch (Exception e) {
            logger.error("同步微信公众号文章失败", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 清空所有新闻中心数据
     *
     * @return 结果
     */
    @Override
    public int deleteAllMiniNewsCenter()
    {
        return miniNewsCenterMapper.deleteAllMiniNewsCenter();
    }



    /**
     * 获取最新的AccessToken
     * 优先使用稳定Token接口，如果达到限制则回退到传统接口
     *
     * @param appid 微信公众号AppID
     * @param secret 微信公众号Secret
     * @return 最新的AccessToken
     */
    private String getFreshAccessToken(String appid, String secret) {
        logger.info("正在获取最新的微信AccessToken，AppID: {}...", appid.substring(0, Math.min(8, appid.length())));

        // 首先尝试稳定Token接口
        String token = tryStableTokenApi(appid, secret);
        if (token != null) {
            return token;
        }

        // 如果稳定接口失败，回退到传统接口
        logger.warn("稳定Token接口失败，回退到传统Token接口");
        return tryTraditionalTokenApi(appid, secret);
    }

    /**
     * 尝试使用稳定Token接口获取AccessToken
     */
    private String tryStableTokenApi(String appid, String secret) {
        try {
            String urlStr = "https://api.weixin.qq.com/cgi-bin/stable_token";
            logger.info("尝试稳定Token接口");

            // 构建POST请求体
            StringBuilder requestBody = new StringBuilder();
            requestBody.append("{");
            requestBody.append("\"grant_type\":\"client_credential\",");
            requestBody.append("\"appid\":\"").append(appid).append("\",");
            requestBody.append("\"secret\":\"").append(secret).append("\",");
            requestBody.append("\"force_refresh\":false");  // 不强制刷新，避免频繁调用
            requestBody.append("}");

            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);

            // 发送请求体
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toString().getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String responseStr = response.toString();
                logger.info("稳定Token接口响应: {}", responseStr);

                // 检查是否有错误信息
                if (responseStr.contains("\"errcode\"")) {
                    logger.warn("稳定Token接口返回错误: {}", responseStr);
                    return null; // 返回null表示需要回退到传统接口
                }

                // 解析access_token
                if (responseStr.contains("\"access_token\"")) {
                    int startIndex = responseStr.indexOf("\"access_token\":\"") + 16;
                    int endIndex = responseStr.indexOf("\"", startIndex);
                    if (startIndex > 15 && endIndex > startIndex) {
                        String token = responseStr.substring(startIndex, endIndex);
                        logger.info("✅ 稳定Token接口获取成功，长度: {}", token.length());
                        return token;
                    }
                }

                logger.warn("稳定Token接口响应格式异常: {}", responseStr);
                return null;
            } else {
                logger.warn("稳定Token接口请求失败，响应码: {}", responseCode);
                return null;
            }

        } catch (Exception e) {
            logger.warn("稳定Token接口异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 尝试使用传统Token接口获取AccessToken
     */
    private String tryTraditionalTokenApi(String appid, String secret) {
        try {
            String urlStr = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appid, secret
            );

            logger.info("尝试传统Token接口");

            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String responseStr = response.toString();
                logger.info("传统Token接口响应: {}", responseStr);

                // 检查是否有错误信息
                if (responseStr.contains("\"errcode\"")) {
                    logger.error("❌ 传统Token接口返回错误: {}", responseStr);
                    return null;
                }

                // 解析access_token
                if (responseStr.contains("\"access_token\"")) {
                    int startIndex = responseStr.indexOf("\"access_token\":\"") + 16;
                    int endIndex = responseStr.indexOf("\"", startIndex);
                    if (startIndex > 15 && endIndex > startIndex) {
                        String token = responseStr.substring(startIndex, endIndex);
                        logger.info("✅ 传统Token接口获取成功，长度: {}", token.length());
                        return token;
                    }
                }

                logger.error("❌ 传统Token接口响应格式错误: {}", responseStr);
                return null;
            } else {
                logger.error("传统Token接口请求失败，响应码: {}", responseCode);
                return null;
            }

        } catch (Exception e) {
            logger.error("传统Token接口异常", e);
            return null;
        }
    }
}