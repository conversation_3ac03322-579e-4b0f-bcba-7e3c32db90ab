package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.CompetitionConfig;

/**
 * 大赛介绍Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Mapper
public interface CompetitionConfigMapper 
{
        /**
     * 查询大赛介绍
     *
     * @param id 大赛介绍主键
     * @return 大赛介绍
     */
    public CompetitionConfig selectCompetitionConfigById(Long id);

        /**
     * 查询大赛介绍列表
     *
     * @param competitionConfig 大赛介绍
     * @return 大赛介绍集合
     */
    public List<CompetitionConfig> selectCompetitionConfigList(CompetitionConfig competitionConfig);

        /**
     * 新增大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    public int insertCompetitionConfig(CompetitionConfig competitionConfig);

        /**
     * 修改大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    public int updateCompetitionConfig(CompetitionConfig competitionConfig);

        /**
     * 删除大赛介绍
     *
     * @param id 大赛介绍主键
     * @return 结果
     */
    public int deleteCompetitionConfigById(Long id);

    /**
     * 批量删除大赛介绍
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompetitionConfigByIds(Long[] ids);

        /**
     * 获取当前启用的大赛介绍
     *
     * @return 大赛介绍
     */
    public CompetitionConfig selectEnabledConfig();
}