<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="需求标题" prop="demandTitle">
        <el-input
          v-model="queryParams.demandTitle"
          placeholder="请输入需求标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="需求类型" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择需求类型" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.categoryId"
            :label="category.categoryName"
            :value="category.categoryId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="需求状态" prop="demandStatus">
        <el-select v-model="queryParams.demandStatus" placeholder="请选择需求状态" clearable>
          <el-option label="已发布" value="0" />
          <el-option label="已对接" value="1" />
          <el-option label="已下架" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间筛选" prop="timeFilter">
        <el-select v-model="queryParams.timeFilter" placeholder="请选择时间范围" clearable>
          <el-option label="一周内发布" value="week_within" />
          <el-option label="发布满一周" value="week_over" />
          <el-option label="发布满一月" value="month_over" />
          <el-option label="发布满一年" value="year_over" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:demand:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:demand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:demand:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="demandList"
      @selection-change="handleSelectionChange"
      row-key="demandId"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="需求ID" align="center" prop="demandId" width="80" />
      <el-table-column label="需求标题" align="center" prop="demandTitle" show-overflow-tooltip />
      <el-table-column label="需求类型" align="center" prop="categoryName" width="120" />
      <el-table-column label="需求描述" align="center" prop="demandDesc" show-overflow-tooltip />
      <el-table-column label="联系人" align="center" prop="contactName" width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
      <el-table-column label="需求状态" align="center" prop="demandStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.demandStatus === '0'" type="success">已发布</el-tag>
          <el-tag v-else-if="scope.row.demandStatus === '1'" type="warning">已对接</el-tag>
          <el-tag v-else type="danger">已下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否置顶" align="center" prop="isTop" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isTop === '1'" type="warning">置顶</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:demand:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:demand:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-top"
            @click="handleToggleTop(scope.row)"
            v-hasPermi="['miniapp:demand:edit']"
          >{{ scope.row.isTop === '1' ? '取消置顶' : '置顶' }}</el-button>
          <el-button
            v-if="scope.row.demandStatus !== '2'"
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleOffShelf(scope.row)"
            v-hasPermi="['miniapp:demand:edit']"
            style="color: #E6A23C;"
          >下架</el-button>
          <el-button
            v-if="scope.row.demandStatus === '2'"
            size="mini"
            type="text"
            icon="el-icon-upload2"
            @click="handleOnShelf(scope.row)"
            v-hasPermi="['miniapp:demand:edit']"
            style="color: #67C23A;"
          >上架</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="需求类型" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择需求类型" style="width: 100%" @change="onCategoryChange">
                <el-option
                  v-for="category in categoryList"
                  :key="category.categoryId"
                  :label="category.categoryName"
                  :value="category.categoryId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求状态" prop="demandStatus">
              <el-select v-model="form.demandStatus" placeholder="请选择需求状态" style="width: 100%">
                <el-option label="已发布" value="0" />
                <el-option label="已对接" value="1" />
                <el-option label="已下架" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="需求标题" prop="demandTitle">
          <el-input v-model="form.demandTitle" placeholder="请输入需求标题" />
        </el-form-item>
        <el-form-item label="需求描述" prop="demandDesc">
          <el-input v-model="form.demandDesc" type="textarea" placeholder="请输入需求描述" :rows="4" />
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人姓名" prop="contactName">
              <el-input v-model="form.contactName" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否置顶" prop="isTop">
              <el-radio-group v-model="form.isTop">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 动态表单字段 -->
        <div v-if="categoryFieldsData && categoryFieldsData.length > 0" class="dynamic-fields-section">
          <el-divider content-position="left">
            <span style="color: #409EFF; font-weight: bold;">{{ getCategoryName() }}专属字段</span>
          </el-divider>

          <!-- 渲染分类字段 -->
          <div v-for="(categoryData, categoryIndex) in categoryFieldsData" :key="`category-${categoryIndex}`" class="category-group">
            <div v-if="categoryData.name" class="category-title">
              <i class="el-icon-folder-opened"></i>
              <span>{{ categoryData.name }}</span>
            </div>
            <div v-if="categoryData.description" class="category-description">
              {{ categoryData.description }}
            </div>

            <div v-for="(field, fieldIndex) in categoryData.fields" :key="`field-${field.name}-${fieldIndex}`" class="dynamic-field-item">
              <div class="field-label">
                <span v-if="field.required" class="required-mark">*</span>
                {{ field.label }}
              </div>
              <div class="field-content">
                <!-- 静态内容 -->
                <div v-if="field.type === 'static'" class="static-content">
                  {{ field.staticContent }}
                </div>
                <!-- 文本输入 -->
                <el-input
                  v-else-if="field.type === 'input'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 多行文本 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  type="textarea"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  :rows="3"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 数字输入 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  :value="form.dynamicData[field.name] || field.value || 0"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 电话号码 -->
                <el-input
                  v-else-if="field.type === 'tel'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 邮箱地址 -->
                <el-input
                  v-else-if="field.type === 'email'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 单选框 -->
                <el-radio-group
                  v-else-if="field.type === 'radio'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  @input="value => handleFieldInput(field, value)"
                >
                  <el-radio
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-radio-${optionIndex}-${option}`"
                    :label="option"
                  >{{ option }}</el-radio>
                </el-radio-group>
                <!-- 多选框 -->
                <el-checkbox-group
                  v-else-if="field.type === 'checkbox'"
                  :value="form.dynamicData[field.name] || field.value || []"
                  @input="value => handleFieldInput(field, value)"
                >
                  <el-checkbox
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-checkbox-${optionIndex}-${option}`"
                    :label="option"
                  >{{ option }}</el-checkbox>
                </el-checkbox-group>
                <!-- 下拉选择 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                >
                  <el-option
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-option-${optionIndex}-${option}`"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <!-- 日期选择 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  :value="form.dynamicData[field.name] || field.value || null"
                  type="date"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 时间选择 -->
                <el-time-picker
                  v-else-if="field.type === 'time'"
                  :value="form.dynamicData[field.name] || field.value || null"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 文件上传 -->
                <div v-else-if="field.type === 'file'">
                  <!-- 如果已有文件URL，显示文件信息 -->
                  <div v-if="field.value && typeof field.value === 'string' && field.value.startsWith('http')" class="existing-file">
                    <div class="file-display">
                      <i class="el-icon-document"></i>
                      <a :href="field.value" target="_blank" class="file-link">
                        {{ getFileNameFromUrl(field.value) }}
                      </a>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="removeFileUrl(field)"
                        class="remove-file-btn"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>

                  <!-- 文件上传组件 -->
                  <el-upload
                    v-else
                    action="/dev-api/common/upload"
                    :headers="uploadHeaders"
                    :on-success="(response, file, fileList) => handleFileSuccess(response, file, fileList, field)"
                    :on-remove="(file, fileList) => handleFileRemove(file, fileList, field)"
                    :file-list="getFileList(field)"
                    :on-preview="handleFilePreview"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                  </el-upload>

                  <!-- 已上传文件列表显示（数组格式） -->
                  <div v-if="Array.isArray(field.value) && field.value.length > 0" class="uploaded-files-list">
                    <div class="uploaded-files-title">已上传文件：</div>
                    <div
                      v-for="(file, index) in field.value"
                      :key="`uploaded-${field.name}-${index}`"
                      class="uploaded-file-item"
                    >
                      <i class="el-icon-document"></i>
                      <a
                        :href="file.url || file"
                        target="_blank"
                        class="file-link"
                        @click="downloadFile(file.url || file, file.name || getFileNameFromUrl(file))"
                      >
                        {{ file.name || getFileNameFromUrl(file.url || file) }}
                      </a>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="removeUploadedFile(field, index)"
                        class="remove-file-btn"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDemand, getDemand, delDemand, addDemand, updateDemand, offShelfDemand, onShelfDemand } from "@/api/miniapp/demand";
import { getEnabledDemandCategoryList } from "@/api/miniapp/demandcategory";

export default {
  name: "MiniDemand",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求表格数据
      demandList: [],
      // 需求类型列表
      categoryList: [],
      // 动态表单字段
      dynamicFields: [],
      // 选中的类型名称
      selectedCategoryName: '',
      // 分类字段数据（新格式）
      categoryFieldsData: [],
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + this.$store.getters.token
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        demandTitle: null,
        categoryId: null,
        demandStatus: null,
        timeFilter: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryId: [
          { required: true, message: "需求类型不能为空", trigger: "change" }
        ],
        demandTitle: [
          { required: true, message: "需求标题不能为空", trigger: "blur" }
        ],
        demandDesc: [
          { required: true, message: "需求描述不能为空", trigger: "blur" }
        ],
        contactName: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" }
        ],
        contactPhone: [
          { required: true, message: "联系人电话不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        demandStatus: [
          { required: true, message: "需求状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    /** 按模块分组的动态字段 */
    groupedDynamicFields() {
      const grouped = {};
      this.dynamicFields.forEach(field => {
        const moduleTitle = field.moduleTitle || '其他字段';
        if (!grouped[moduleTitle]) {
          grouped[moduleTitle] = [];
        }
        grouped[moduleTitle].push(field);
      });
      return grouped;
    },

    /** 安全的动态数据访问器 */
    safeDynamicData() {
      const safeData = { ...this.form.dynamicData };
      this.dynamicFields.forEach(field => {
        if (field.name) {
          if (field.type === 'checkbox' && !Array.isArray(safeData[field.name])) {
            safeData[field.name] = [];
          } else if (field.type === 'file' && !Array.isArray(safeData[field.name])) {
            safeData[field.name] = [];
          }
        }
      });
      return safeData;
    }
  },
  created() {
    this.getList();
    this.getCategoryList();
    // 测试新的数据格式
    this.testNewDataFormat();
  },
  methods: {
    /** 查询需求列表 */
    getList() {
      this.loading = true;
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取需求列表失败:', error);
        this.loading = false;
        this.$modal.msgError("获取需求列表失败");
      });
    },
    /** 获取需求类型列表 */
    getCategoryList() {
      getEnabledDemandCategoryList().then(response => {
        this.categoryList = response.data;
      }).catch(error => {
        console.error('获取需求类型列表失败:', error);
        this.$modal.msgError("获取需求类型列表失败");
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        demandId: null,
        categoryId: null,
        demandTitle: "",
        demandDesc: "",
        contactName: "",
        contactPhone: "",
        demandStatus: "0",
        isTop: "0",
        remark: "",
        dynamicData: {}
      };

      // 清除动态字段的验证规则
      Object.keys(this.rules).forEach(key => {
        if (key.startsWith('dynamicData.')) {
          this.$delete(this.rules, key);
        }
      });

      // 重置动态字段
      this.dynamicFields = [];
      this.selectedCategoryName = '';

      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.demandId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 先清理状态，但不重置表单
      this.dynamicFields = [];
      this.selectedCategoryName = '';

      const demandId = row.demandId || this.ids;
      getDemand(demandId).then(response => {
        // 使用$set来保持响应式
        const data = response.data;
        this.$set(this.form, 'demandId', data.demandId);
        this.$set(this.form, 'categoryId', data.categoryId);
        this.$set(this.form, 'demandTitle', data.demandTitle || "");
        this.$set(this.form, 'demandDesc', data.demandDesc || "");
        this.$set(this.form, 'contactName', data.contactName || "");
        this.$set(this.form, 'contactPhone', data.contactPhone || "");
        this.$set(this.form, 'demandStatus', data.demandStatus || "0");
        this.$set(this.form, 'isTop', data.isTop || "0");
        this.$set(this.form, 'remark', data.remark || "");

        // 解析动态表单数据
        if (data.formData) {
          try {
            const formData = JSON.parse(data.formData);

            // 检查是否是新格式的数据（包含fields数组的对象）
            if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {
              // 新格式：先设置表单数据，再处理分类字段数据
              this.$set(this.form, 'dynamicData', {});

              // 从fields中提取数据到dynamicData
              formData.forEach(categoryData => {
                if (categoryData.fields) {
                  categoryData.fields.forEach(field => {
                    if (field.value !== undefined && field.value !== null && field.value !== '') {
                      this.$set(this.form.dynamicData, field.name, field.value);
                    }
                  });
                }
              });

              // 处理分类字段数据
              this.processCategoryFieldsData(formData);
            } else {
              // 旧格式：直接使用formData作为dynamicData
              this.$set(this.form, 'dynamicData', formData);
              this.loadDynamicFields(this.form.categoryId);
            }
          } catch (e) {
            console.error('解析动态表单数据失败:', e);
            this.$set(this.form, 'dynamicData', {});
            this.loadDynamicFields(this.form.categoryId);
          }
        } else {
          this.$set(this.form, 'dynamicData', {});
          this.loadDynamicFields(this.form.categoryId);
        }

        // 在下一个tick中清除表单验证状态
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate();
          }
        });

        this.open = true;
        this.title = "修改需求";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 先验证动态字段
      let dynamicFieldsValid = true;
      let firstErrorField = null;

      // 验证新格式的分类字段数据
      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
        this.categoryFieldsData.forEach(categoryData => {
          if (categoryData.fields) {
            categoryData.fields.forEach(field => {
              if (field.required && field.name && field.type !== 'static') {
                const value = this.form.dynamicData[field.name];
                let isEmpty = false;

                if (field.type === 'checkbox' || field.type === 'file') {
                  isEmpty = !Array.isArray(value) || value.length === 0;
                } else {
                  isEmpty = value === null || value === undefined || value === '';
                }

                if (isEmpty) {
                  dynamicFieldsValid = false;
                  if (!firstErrorField) {
                    firstErrorField = field.label;
                  }
                }
              }
            });
          }
        });
      } else {
        // 验证旧格式的动态字段
        this.dynamicFields.forEach(field => {
          if (field.required && field.name) {
            const value = this.form.dynamicData[field.name];
            let isEmpty = false;

            if (field.type === 'checkbox' || field.type === 'file') {
              isEmpty = !Array.isArray(value) || value.length === 0;
            } else {
              isEmpty = value === null || value === undefined || value === '';
            }

            if (isEmpty) {
              dynamicFieldsValid = false;
              if (!firstErrorField) {
                firstErrorField = field.label;
              }
            }
          }
        });
      }

      if (!dynamicFieldsValid) {
        this.$modal.msgError(`${firstErrorField}不能为空`);
        return;
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          const formData = { ...this.form };

          // 构建包含value的完整字段数据格式
          if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
            // 新格式：使用分类字段数据，并更新每个字段的value
            const categoryDataWithValues = this.categoryFieldsData.map(categoryData => ({
              ...categoryData,
              fields: categoryData.fields.map(field => ({
                ...field,
                value: this.form.dynamicData[field.name] || field.value || (field.type === 'checkbox' || field.type === 'file' ? [] : '')
              }))
            }));
            formData.formData = JSON.stringify(categoryDataWithValues);
          } else if (formData.dynamicData && Object.keys(formData.dynamicData).length > 0) {
            // 旧格式：直接使用dynamicData
            formData.formData = JSON.stringify(formData.dynamicData);
          }

          delete formData.dynamicData; // 删除临时字段

          console.log('submitForm - formData.formData:', formData.formData);

          if (this.form.demandId != null) {
            updateDemand(formData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDemand(formData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const demandIds = row ? [row.demandId] : this.ids;
      const confirmText = row
        ? `是否确认删除需求编号为"${row.demandId}"的数据项？`
        : `是否确认删除选中的${this.ids.length}条数据项？`;

      this.$modal.confirm(confirmText).then(function() {
        return delDemand(demandIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/demand/export', {
        ...this.queryParams
      }, `需求数据_${new Date().getTime()}.xlsx`)
    },
    /** 置顶/取消置顶 */
    handleToggleTop(row) {
      const text = row.isTop === "1" ? "取消置顶" : "置顶";
      const isTop = row.isTop === "1" ? "0" : "1";
      this.$modal.confirm('确认要"' + text + '"需求"' + row.demandTitle + '"吗？').then(function() {
        const updateData = {
          demandId: row.demandId,
          isTop: isTop
        };
        return updateDemand(updateData);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    },

    /** 下架需求 */
    handleOffShelf(row) {
      this.$modal.confirm('确认要下架需求"' + row.demandTitle + '"吗？').then(function() {
        return offShelfDemand(row.demandId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("下架成功");
      }).catch(() => {});
    },

    /** 上架需求 */
    handleOnShelf(row) {
      this.$modal.confirm('确认要上架需求"' + row.demandTitle + '"吗？').then(function() {
        return onShelfDemand(row.demandId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("上架成功");
      }).catch(() => {});
    },

    /** 需求类型变化事件 */
    onCategoryChange(categoryId) {
      console.log('onCategoryChange - categoryId:', categoryId);

      // 清空动态表单数据
      this.form.dynamicData = {};
      // 清空分类字段数据
      this.categoryFieldsData = [];

      if (!categoryId) {
        this.dynamicFields = [];
        this.selectedCategoryName = '';
        return;
      }

      const category = this.categoryList.find(cat => cat.categoryId === categoryId);
      console.log('onCategoryChange - found category:', category);

      if (category && category.formFields) {
        try {
          const formConfig = JSON.parse(category.formFields);
          console.log('onCategoryChange - formConfig:', formConfig);

          // 检查是否是新格式的数据（包含fields数组的对象）
          if (Array.isArray(formConfig) && formConfig.length > 0 && formConfig[0].fields) {
            // 新格式：使用分类字段数据
            this.processCategoryFieldsData(formConfig);
            console.log('onCategoryChange - using new format, categoryFieldsData:', this.categoryFieldsData);
          } else {
            // 旧格式：使用传统的动态字段加载
            this.loadDynamicFields(categoryId);
            console.log('onCategoryChange - using old format, dynamicFields:', this.dynamicFields);
          }
        } catch (e) {
          console.error('解析表单配置失败:', e);
          this.loadDynamicFields(categoryId);
        }
      } else {
        console.log('onCategoryChange - no category or formFields found');
        this.dynamicFields = [];
        this.selectedCategoryName = '';
      }
    },

    /** 加载动态表单字段 */
    loadDynamicFields(categoryId) {
      if (!categoryId) {
        this.dynamicFields = [];
        this.selectedCategoryName = '';
        return;
      }

      const category = this.categoryList.find(cat => cat.categoryId === categoryId);
      if (category) {
        this.selectedCategoryName = category.categoryName;

        if (category.formFields) {
          try {
            const formConfig = JSON.parse(category.formFields);
            this.dynamicFields = [];

            // 检查是否是新的模块化结构
            if (Array.isArray(formConfig) && formConfig.length > 0) {
              if (formConfig[0].fields) {
                // 新的模块化结构：提取所有模块中的字段
                formConfig.forEach(module => {
                  if (module.fields && Array.isArray(module.fields)) {
                    module.fields.forEach(field => {
                      // 跳过静态展示字段
                      if (field.type !== 'static' && field.name) {
                        this.dynamicFields.push({
                          ...field,
                          moduleTitle: module.name // 添加模块标题用于分组显示
                        });
                      }
                    });
                  }
                });
              } else {
                // 旧的扁平结构：直接使用
                this.dynamicFields = formConfig;
              }
            }

            // 初始化动态数据对象和验证规则
            this.dynamicFields.forEach(field => {
              if (field.name) {
                // 确保字段总是有正确的初始值
                if (field.type === 'checkbox') {
                  this.$set(this.form.dynamicData, field.name,
                    Array.isArray(this.form.dynamicData[field.name]) ? this.form.dynamicData[field.name] : []);
                } else if (field.type === 'file') {
                  // 处理文件字段的数据转换
                  const fileData = this.form.dynamicData[field.name];
                  if (typeof fileData === 'string' && fileData.trim() !== '') {
                    // 如果是字符串URL，转换为对象数组格式
                    const fileName = fileData.split('/').pop() || '下载文件';
                    this.$set(this.form.dynamicData, field.name, [{
                      name: fileName,
                      url: fileData
                    }]);
                  } else if (Array.isArray(fileData)) {
                    // 如果已经是数组，保持不变
                    this.$set(this.form.dynamicData, field.name, fileData);
                  } else {
                    // 其他情况设为空数组
                    this.$set(this.form.dynamicData, field.name, []);
                  }
                } else if (field.type === 'number') {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);
                } else if (field.type === 'date' || field.type === 'time') {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);
                } else {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : '');
                }

                // 添加动态字段的验证规则
                if (field.required) {
                  const ruleName = `dynamicData.${field.name}`;
                  this.$set(this.rules, ruleName, [
                    {
                      required: true,
                      message: `${field.label}不能为空`,
                      trigger: field.type === 'checkbox' ? 'change' : 'blur'
                    }
                  ]);
                }
              }
            });
          } catch (e) {
            console.error('解析表单字段配置失败:', e);
            this.dynamicFields = [];
          }
        } else {
          this.dynamicFields = [];
        }
      }
    },

    /** 获取字段选项 */
    getFieldOptions(field) {
      if (!field.options) return [];
      return field.options.split(',').map(option => option.trim()).filter(option => option);
    },

    /** 文件上传成功回调 */
    handleFileSuccess(response, file, fileList, field) {
      console.log('handleFileSuccess - response:', response, 'file:', file, 'field:', field.name);

      if (response.code === 200) {
        const fileUrl = response.url || response.fileName || response.data;

        // 如果当前值是空的或者是字符串，直接设置为文件URL
        if (!field.value || typeof field.value === 'string') {
          this.handleFieldInput(field, fileUrl);
        } else if (Array.isArray(field.value)) {
          // 如果是数组，添加文件对象
          const fileData = {
            name: file.name,
            url: fileUrl
          };
          const newValue = [...field.value, fileData];
          this.handleFieldInput(field, newValue);
        } else {
          // 其他情况，设置为文件URL
          this.handleFieldInput(field, fileUrl);
        }

        console.log('handleFileSuccess - field.value after update:', field.value);
      } else {
        this.$modal.msgError(response.msg || '文件上传失败');
      }
    },

    /** 文件删除回调 */
    handleFileRemove(file, fileList, field) {
      if (field.value && Array.isArray(field.value)) {
        const index = field.value.findIndex(item => item.name === file.name);
        if (index > -1) {
          field.value.splice(index, 1);
          this.updateFieldValue(field);
        }
      }
    },

    /** 获取多选框的安全值 */
    getCheckboxValue(fieldName) {
      const value = this.form.dynamicData[fieldName];
      return Array.isArray(value) ? value : [];
    },

    /** 更新多选框的值 */
    updateCheckboxValue(fieldName, value) {
      this.$set(this.form.dynamicData, fieldName, Array.isArray(value) ? value : []);
    },

    /** 获取文件列表（用于el-upload组件） */
    getFileList(field) {
      const files = field.value;
      console.log('getFileList - field:', field.name, 'value:', files);

      // 如果是字符串URL，不显示在upload组件中（单独显示）
      if (typeof files === 'string') {
        return [];
      }

      if (!Array.isArray(files)) {
        console.log('getFileList - files is not array, returning empty array');
        return [];
      }

      // 转换为el-upload需要的格式
      const fileList = files.map((file, index) => ({
        name: file.name || this.getFileNameFromUrl(file.url || file),
        url: file.url || file,
        uid: `${field.name}-${index}`,
        status: 'success'
      }));

      console.log('getFileList - converted fileList:', fileList);
      return fileList;
    },

    /** 获取已上传的文件列表（用于显示） */
    getUploadedFiles(field) {
      const files = field.value;
      return Array.isArray(files) ? files : [];
    },

    /** 文件预览 */
    handleFilePreview(file) {
      if (file.url) {
        window.open(file.url, '_blank');
      }
    },

    /** 下载文件 */
    downloadFile(url, fileName) {
      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '下载文件';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /** 删除已上传的文件 */
    removeUploadedFile(field, index) {
      if (field.value && Array.isArray(field.value)) {
        const newValue = [...field.value];
        newValue.splice(index, 1);
        this.handleFieldInput(field, newValue);
      }
    },

    /** 删除文件URL */
    removeFileUrl(field) {
      this.handleFieldInput(field, '');
    },

    /** 从URL中提取文件名 */
    getFileNameFromUrl(url) {
      if (!url) return '未知文件';
      const parts = url.split('/');
      const fileName = parts[parts.length - 1];
      // 如果文件名包含时间戳等，尝试提取原始文件名
      const match = fileName.match(/.*_\d+A\d+\.(.*)/);
      if (match) {
        return `文件.${match[1]}`;
      }
      return fileName || '未知文件';
    },

    /** 处理字段输入 */
    handleFieldInput(field, value) {
      // 更新字段的value
      field.value = value;
      // 同步到表单数据
      this.$set(this.form.dynamicData, field.name, value);
      console.log('handleFieldInput - field:', field.name, 'value:', value);
    },

    /** 更新字段值到表单数据 */
    updateFieldValue(field) {
      this.$set(this.form.dynamicData, field.name, field.value);
    },

    /** 获取分类名称 */
    getCategoryName() {
      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
        return this.categoryFieldsData[0].name || '专属字段';
      }
      return this.selectedCategoryName || '专属字段';
    },

    /** 处理分类字段数据 */
    processCategoryFieldsData(data) {
      if (typeof data === 'string') {
        try {
          this.categoryFieldsData = JSON.parse(data);
        } catch (e) {
          console.error('解析分类字段数据失败:', e);
          this.categoryFieldsData = [];
        }
      } else if (Array.isArray(data)) {
        this.categoryFieldsData = data;
      } else {
        this.categoryFieldsData = [];
      }

      // 初始化字段值到表单数据
      this.categoryFieldsData.forEach(categoryData => {
        if (categoryData.fields) {
          categoryData.fields.forEach(field => {
            // 确保字段有初始值
            if (field.value === undefined || field.value === null) {
              if (field.type === 'file') {
                field.value = [];
              } else if (field.type === 'checkbox') {
                field.value = [];
              } else {
                field.value = '';
              }
            }

            // 从表单数据中恢复字段值（如果存在）
            if (this.form.dynamicData && this.form.dynamicData[field.name] !== undefined) {
              field.value = this.form.dynamicData[field.name];
            } else {
              // 设置到表单数据
              this.$set(this.form.dynamicData, field.name, field.value);
            }
          });
        }
      });
    },

    /** 测试新的数据格式 */
    testNewDataFormat() {
      // 使用您提供的实际JSON数据格式进行测试
      const testData = [
        {
          "name": "基础信息",
          "description": "",
          "fields": [
            {
              "label": "企业全称",
              "name": "field_652408",
              "type": "input",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "测试企业有限公司"
            },
            {
              "label": "行业标签",
              "name": "field_720944",
              "type": "select",
              "required": true,
              "options": "新能源,硬科技",
              "placeholder": "请选择",
              "staticContent": "",
              "value": "新能源"
            },
            {
              "label": "联系人",
              "name": "contact_name",
              "type": "input",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "张三"
            },
            {
              "label": "电话",
              "name": "phone",
              "type": "tel",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "13800138000"
            }
          ],
          "icon": "http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png"
        },
        {
          "name": "其他材料补充",
          "description": "",
          "fields": [
            {
              "label": "上传附件",
              "name": "field_989222",
              "type": "file",
              "required": false,
              "options": "",
              "placeholder": "未选择任何文件",
              "staticContent": "",
              "value": "http://************:8080/profile/upload/2025/07/23/xhuFwa0qulPS03911c35329f695848fb659a24f6f159_20250723183220A001.png"
            },
            {
              "label": "邮件提交至",
              "name": "field_227969",
              "type": "static",
              "required": false,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "<EMAIL>(文件名：【企业曝光申请】-企业/项目名）",
              "value": ""
            }
          ],
          "icon": "http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png"
        }
      ];

      // 当点击修改按钮时，可以调用这个方法来设置测试数据
      // this.processCategoryFieldsData(testData);
    },




  }
};
</script>

<style scoped>
.dynamic-fields-section {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}

.module-group {
  margin-bottom: 20px;
}

.module-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  font-weight: bold;
  color: #303133;
}

.module-title i {
  margin-right: 8px;
  color: #409eff;
}

/* 分类组样式 */
.category-group {
  margin-bottom: 20px;
}

.category-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  font-weight: bold;
  color: #303133;
}

.category-title i {
  margin-right: 8px;
  color: #409eff;
}

.category-description {
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 动态字段垂直布局样式 */
.dynamic-field-item {
  margin-bottom: 20px;
  width: 100%;
}

.dynamic-field-item:last-child {
  margin-bottom: 20px; /* 保持底部间距，避免与下方元素重合 */
}

/* 字段标签样式 */
.dynamic-field-item .field-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 必填字段标识 */
.dynamic-field-item .required-mark {
  color: #f56c6c;
  margin-right: 4px;
}

/* 字段内容区域 */
.dynamic-field-item .field-content {
  width: 100%;
}

/* 表单控件样式 */
.dynamic-field-item .field-content .el-input,
.dynamic-field-item .field-content .el-textarea,
.dynamic-field-item .field-content .el-select,
.dynamic-field-item .field-content .el-input-number,
.dynamic-field-item .field-content .el-date-editor,
.dynamic-field-item .field-content .el-time-picker {
  width: 100%;
}

/* 单选框和多选框布局 */
.dynamic-field-item .field-content .el-radio-group,
.dynamic-field-item .field-content .el-checkbox-group {
  width: 100%;
  line-height: 1.8;
}

.dynamic-field-item .field-content .el-radio,
.dynamic-field-item .field-content .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
  display: inline-block;
}

/* 文件上传组件 */
.dynamic-field-item .field-content .el-upload {
  width: 100%;
}

.el-divider {
  margin: 10px 0 20px 0;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .dynamic-field-item .el-form-item__label {
    width: 100px !important;
    text-align: left;
  }

  .dynamic-field-item .el-radio,
  .dynamic-field-item .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }
}

/* 上传组件样式优化 */
.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.dynamic-field-item .el-upload {
  width: 100%;
}

.dynamic-field-item .el-upload-list {
  margin-top: 10px;
}

/* 文件上传相关样式 */
.uploaded-files-list {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.uploaded-files-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 8px;
}

.uploaded-file-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
}

.uploaded-file-item:last-child {
  border-bottom: none;
}

.uploaded-file-item i {
  margin-right: 8px;
  color: #409eff;
  font-size: 16px;
}

.file-link {
  flex: 1;
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
}

.file-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.remove-file-btn {
  margin-left: 10px;
  color: #f56c6c;
}

.remove-file-btn:hover {
  color: #f78989;
}

/* 静态内容样式 */
.static-content {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #e4e7ed;
}

/* 已存在文件显示样式 */
.existing-file {
  margin-bottom: 10px;
}

.file-display {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  gap: 8px;
}

.file-display .el-icon-document {
  color: #409eff;
  font-size: 16px;
}

.file-display .file-link {
  flex: 1;
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}

.file-display .file-link:hover {
  text-decoration: underline;
}

.file-display .remove-file-btn {
  color: #f56c6c;
  padding: 0;
}

/* 动态字段整体布局优化 */
.dynamic-fields-section .el-row {
  margin-left: -10px;
  margin-right: -10px;
}

.dynamic-fields-section .el-col {
  padding-left: 10px;
  padding-right: 10px;
}

/* 优化表单验证错误提示的显示 */
.dynamic-field-item .el-form-item__error {
  position: static;
  margin-top: 2px;
  padding-top: 2px;
}
</style>
