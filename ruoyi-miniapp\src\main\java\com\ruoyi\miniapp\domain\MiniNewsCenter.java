package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 新闻中心对象 mini_news_center
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
public class MiniNewsCenter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 文章标题 */
    @Excel(name = "文章标题")
    private String title;

    /** 作者 */
    @Excel(name = "作者")
    private String author;

    /** 缩略图URL */
    @Excel(name = "缩略图URL")
    private String thumbUrl;

    /** 文章摘要 */
    @Excel(name = "文章摘要")
    private String digest;

    /** 微信文章ID */
    @Excel(name = "微信文章ID")
    private String wechatArticleId;

    /** 微信文章URL */
    @Excel(name = "微信文章URL")
    private String wechatArticleUrl;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 微信创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "微信创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date wechatCreateTime;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    
    public void setAuthor(String author) 
    {
        this.author = author;
    }

    public String getAuthor() 
    {
        return author;
    }
    
    public void setThumbUrl(String thumbUrl) 
    {
        this.thumbUrl = thumbUrl;
    }

    public String getThumbUrl() 
    {
        return thumbUrl;
    }
    
    public void setDigest(String digest) 
    {
        this.digest = digest;
    }

    public String getDigest() 
    {
        return digest;
    }
    
    public void setWechatArticleId(String wechatArticleId) 
    {
        this.wechatArticleId = wechatArticleId;
    }

    public String getWechatArticleId() 
    {
        return wechatArticleId;
    }
    
    public void setWechatArticleUrl(String wechatArticleUrl) 
    {
        this.wechatArticleUrl = wechatArticleUrl;
    }

    public String getWechatArticleUrl() 
    {
        return wechatArticleUrl;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setWechatCreateTime(Date wechatCreateTime) 
    {
        this.wechatCreateTime = wechatCreateTime;
    }

    public Date getWechatCreateTime() 
    {
        return wechatCreateTime;
    }
    
    public void setSyncTime(Date syncTime) 
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime() 
    {
        return syncTime;
    }
    
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("author", getAuthor())
            .append("thumbUrl", getThumbUrl())
            .append("digest", getDigest())
            .append("wechatArticleId", getWechatArticleId())
            .append("wechatArticleUrl", getWechatArticleUrl())
            .append("status", getStatus())
            .append("wechatCreateTime", getWechatCreateTime())
            .append("syncTime", getSyncTime())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
} 