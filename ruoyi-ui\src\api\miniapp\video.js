import request from '@/utils/request'

// 查询视频展播列表
export function listVideo(query) {
  return request({
    url: '/miniapp/video/list',
    method: 'get',
    params: query
  })
}

// 查询视频展播详细
export function getVideo(id) {
  return request({
    url: '/miniapp/video/' + id,
    method: 'get'
  })
}

// 新增视频展播
export function addVideo(data) {
  return request({
    url: '/miniapp/video',
    method: 'post',
    data: data
  })
}

// 修改视频展播
export function updateVideo(data) {
  return request({
    url: '/miniapp/video',
    method: 'put',
    data: data
  })
}

// 删除视频展播
export function delVideo(id) {
  return request({
    url: '/miniapp/video/' + id,
    method: 'delete'
  })
} 