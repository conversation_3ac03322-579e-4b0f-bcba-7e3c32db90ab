<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.ProjectGuidanceMapper">
    
    <resultMap type="ProjectGuidance" id="ProjectGuidanceResult">
        <result property="guidanceId"    column="guidance_id"    />
        <result property="title"    column="title"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="description"    column="description"    />
        <result property="location"    column="location"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="registrationDeadline"    column="registration_deadline"    />
        <result property="formFields"    column="form_fields"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProjectGuidanceVo">
        select guidance_id, title, cover_image, description, location, start_time, end_time, registration_deadline, form_fields, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_project_guidance
    </sql>

    <select id="selectProjectGuidanceList" parameterType="ProjectGuidance" resultMap="ProjectGuidanceResult">
        <include refid="selectProjectGuidanceVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="registrationDeadline != null "> and registration_deadline &gt;= #{registrationDeadline}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectProjectGuidanceByGuidanceId" parameterType="Long" resultMap="ProjectGuidanceResult">
        <include refid="selectProjectGuidanceVo"/>
        where guidance_id = #{guidanceId}
    </select>
        
    <insert id="insertProjectGuidance" parameterType="ProjectGuidance" useGeneratedKeys="true" keyProperty="guidanceId">
        insert into mini_project_guidance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="description != null">description,</if>
            <if test="location != null">location,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="registrationDeadline != null">registration_deadline,</if>
            <if test="formFields != null">form_fields,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="description != null">#{description},</if>
            <if test="location != null">#{location},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="registrationDeadline != null">#{registrationDeadline},</if>
            <if test="formFields != null">#{formFields},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProjectGuidance" parameterType="ProjectGuidance">
        update mini_project_guidance
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="coverImage != null and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="description != null">description = #{description},</if>
            <if test="location != null">location = #{location},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="registrationDeadline != null">registration_deadline = #{registrationDeadline},</if>
            <if test="formFields != null">form_fields = #{formFields},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where guidance_id = #{guidanceId}
    </update>

    <delete id="deleteProjectGuidanceByGuidanceId" parameterType="Long">
        delete from mini_project_guidance where guidance_id = #{guidanceId}
    </delete>

    <delete id="deleteProjectGuidanceByGuidanceIds" parameterType="String">
        delete from mini_project_guidance where guidance_id in 
        <foreach item="guidanceId" collection="array" open="(" separator="," close=")">
            #{guidanceId}
        </foreach>
    </delete>
</mapper> 