package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniNotice;
import com.ruoyi.miniapp.mapper.MiniNoticeMapper;
import com.ruoyi.miniapp.service.IMiniNoticeService;

/**
 * 滚动通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniNoticeServiceImpl implements IMiniNoticeService 
{
    @Autowired
    private MiniNoticeMapper miniNoticeMapper;

    /**
     * 查询滚动通知
     * 
     * @param noticeId 滚动通知主键
     * @return 滚动通知
     */
    @Override
    public MiniNotice selectMiniNoticeByNoticeId(Long noticeId)
    {
        return miniNoticeMapper.selectMiniNoticeByNoticeId(noticeId);
    }

    /**
     * 查询滚动通知列表
     * 
     * @param miniNotice 滚动通知
     * @return 滚动通知
     */
    @Override
    public List<MiniNotice> selectMiniNoticeList(MiniNotice miniNotice)
    {
        return miniNoticeMapper.selectMiniNoticeList(miniNotice);
    }

    /**
     * 新增滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    @Override
    public int insertMiniNotice(MiniNotice miniNotice)
    {
        miniNotice.setCreateTime(DateUtils.getNowDate());
        return miniNoticeMapper.insertMiniNotice(miniNotice);
    }

    /**
     * 修改滚动通知
     * 
     * @param miniNotice 滚动通知
     * @return 结果
     */
    @Override
    public int updateMiniNotice(MiniNotice miniNotice)
    {
        miniNotice.setUpdateTime(DateUtils.getNowDate());
        return miniNoticeMapper.updateMiniNotice(miniNotice);
    }

    /**
     * 批量删除滚动通知
     * 
     * @param noticeIds 需要删除的滚动通知主键
     * @return 结果
     */
    @Override
    public int deleteMiniNoticeByNoticeIds(Long[] noticeIds)
    {
        return miniNoticeMapper.deleteMiniNoticeByNoticeIds(noticeIds);
    }

    /**
     * 删除滚动通知信息
     * 
     * @param noticeId 滚动通知主键
     * @return 结果
     */
    @Override
    public int deleteMiniNoticeByNoticeId(Long noticeId)
    {
        return miniNoticeMapper.deleteMiniNoticeByNoticeId(noticeId);
    }

    /**
     * 查询启用的滚动通知列表（小程序端调用）
     * 
     * @return 滚动通知集合
     */
    @Override
    public List<MiniNotice> selectEnabledMiniNoticeList()
    {
        return miniNoticeMapper.selectEnabledMiniNoticeList();
    }
} 