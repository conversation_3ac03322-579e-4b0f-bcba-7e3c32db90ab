import request from '@/utils/request'

// 查询新闻中心列表
export function listNews(query) {
  return request({
    url: '/miniapp/haitang/news/list',
    method: 'get',
    params: query
  })
}

// 查询新闻中心详细
export function getNews(id) {
  return request({
    url: '/miniapp/haitang/news/' + id,
    method: 'get'
  })
}

// 新增新闻中心
export function addNews(data) {
  return request({
    url: '/miniapp/haitang/news',
    method: 'post',
    data: data
  })
}

// 修改新闻中心
export function updateNews(data) {
  return request({
    url: '/miniapp/haitang/news',
    method: 'put',
    data: data
  })
}

// 删除新闻中心
export function delNews(id) {
  return request({
    url: '/miniapp/haitang/news/' + id,
    method: 'delete'
  })
}

// 导出新闻中心
export function exportNews(query) {
  return request({
    url: '/miniapp/haitang/news/export',
    method: 'post',
    params: query
  })
}

// 同步微信公众号文章
export function syncWechatNews() {
  return request({
    url: '/miniapp/haitang/news/sync',
    method: 'post'
  })
}

// 手动同步指定数量的文章
export function syncWechatNewsWithParams(count, offset) {
  return request({
    url: '/miniapp/haitang/news/sync/' + count + '/' + offset,
    method: 'post'
  })
}

// 清理并重新同步文章
export function resyncWechatNews(count) {
  return request({
    url: '/miniapp/haitang/news/resync/' + count,
    method: 'post'
  })
}