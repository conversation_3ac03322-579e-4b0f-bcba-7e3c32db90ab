package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 招聘职位对象 mini_job
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniJob extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 职位ID */
    private Long jobId;

    /** 职位名称 */
    @Excel(name = "职位名称")
    private String jobTitle;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司规模 */
    @Excel(name = "公司规模")
    private String companyScale;

    /** 薪资范围 */
    @Excel(name = "薪资范围")
    private String salaryRange;

    /** 职位标签（多个用逗号分隔） */
    @Excel(name = "职位标签")
    private String jobTags;

    /** 工作地点 */
    @Excel(name = "工作地点")
    private String workLocation;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String jobDescription;

    /** 任职要求 */
    @Excel(name = "任职要求")
    private String requirements;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setJobId(Long jobId) 
    {
        this.jobId = jobId;
    }

    public Long getJobId() 
    {
        return jobId;
    }
    
    public void setJobTitle(String jobTitle) 
    {
        this.jobTitle = jobTitle;
    }

    public String getJobTitle() 
    {
        return jobTitle;
    }
    
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    
    public void setCompanyScale(String companyScale) 
    {
        this.companyScale = companyScale;
    }

    public String getCompanyScale() 
    {
        return companyScale;
    }
    
    public void setSalaryRange(String salaryRange) 
    {
        this.salaryRange = salaryRange;
    }

    public String getSalaryRange() 
    {
        return salaryRange;
    }
    
    public void setJobTags(String jobTags) 
    {
        this.jobTags = jobTags;
    }

    public String getJobTags() 
    {
        return jobTags;
    }
    
    public void setWorkLocation(String workLocation) 
    {
        this.workLocation = workLocation;
    }

    public String getWorkLocation() 
    {
        return workLocation;
    }
    
    public void setJobDescription(String jobDescription) 
    {
        this.jobDescription = jobDescription;
    }

    public String getJobDescription() 
    {
        return jobDescription;
    }
    
    public void setRequirements(String requirements) 
    {
        this.requirements = requirements;
    }

    public String getRequirements() 
    {
        return requirements;
    }
    
    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("jobId", getJobId())
            .append("jobTitle", getJobTitle())
            .append("companyName", getCompanyName())
            .append("companyScale", getCompanyScale())
            .append("salaryRange", getSalaryRange())
            .append("jobTags", getJobTags())
            .append("workLocation", getWorkLocation())
            .append("jobDescription", getJobDescription())
            .append("requirements", getRequirements())
            .append("contactInfo", getContactInfo())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 