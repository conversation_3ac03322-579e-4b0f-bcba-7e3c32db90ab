<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniIndustryTreeMapper">
    
    <resultMap type="MiniIndustryTree" id="MiniIndustryTreeResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="nodeCode"    column="node_code"    />
        <result property="nodeName"    column="node_name"    />
        <result property="nodeDescription"    column="node_description"    />
        <result property="nodeType"    column="node_type"    />
        <result property="nodeLevel"    column="node_level"    />
        <result property="streamType"    column="stream_type"    />
        <result property="hasStreamType"    column="has_stream_type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="deleted"    column="deleted"    />
        <result property="nodePath"    column="node_path"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniIndustryTreeVo">
        select id, parent_id, node_code, node_name, node_description, node_type, node_level, stream_type, has_stream_type, sort_order, status, deleted, node_path, create_by, create_time, update_by, update_time, remark from mini_industry_tree
    </sql>

    <select id="selectMiniIndustryTreeList" parameterType="MiniIndustryTree" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        <where>
            and deleted = '0'
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="nodeCode != null  and nodeCode != ''"> and node_code = #{nodeCode}</if>
            <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
            <if test="nodeType != null  and nodeType != ''"> and node_type = #{nodeType}</if>
            <if test="nodeLevel != null "> and node_level = #{nodeLevel}</if>
            <if test="streamType != null  and streamType != ''"> and stream_type = #{streamType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by node_level, sort_order, id
    </select>
    
    <select id="selectMiniIndustryTreeById" parameterType="Long" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where id = #{id} and deleted = '0'
    </select>

    <!-- 查询根节点列表（产业类型） -->
    <select id="selectIndustryTypeList" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where parent_id = 0 and node_type = 'type'
        order by sort_order, id
    </select>

    <!-- 根据父节点ID查询子节点列表 -->
    <select id="selectChildrenByParentId" parameterType="Long" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where parent_id = #{parentId}
        order by sort_order, id
    </select>

    <!-- 根据节点类型和父节点ID查询节点列表 -->
    <select id="selectByNodeTypeAndParentId" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where node_type = #{nodeType} and parent_id = #{parentId}
        order by sort_order, id
    </select>

    <!-- 按层级查询节点 -->
    <select id="selectNodesByLevel" parameterType="Integer" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where node_level = #{level} and status = '0'
        order by sort_order, id
    </select>

    <!-- 根据上中下游类型查询第二层级节点 -->
    <select id="selectNodesByStreamType" parameterType="String" resultMap="MiniIndustryTreeResult">
        <include refid="selectMiniIndustryTreeVo"/>
        where node_level = 2 and stream_type = #{streamType} and status = '0'
        order by sort_order, id
    </select>
        
    <insert id="insertMiniIndustryTree" parameterType="MiniIndustryTree" useGeneratedKeys="true" keyProperty="id">
        insert into mini_industry_tree
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="nodeCode != null and nodeCode != ''">node_code,</if>
            <if test="nodeName != null and nodeName != ''">node_name,</if>
            <if test="nodeDescription != null">node_description,</if>
            <if test="nodeType != null and nodeType != ''">node_type,</if>
            <if test="nodeLevel != null">node_level,</if>
            <if test="streamType != null">stream_type,</if>
            <if test="hasStreamType != null">has_stream_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="deleted != null">deleted,</if>
            <if test="nodePath != null">node_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="nodeCode != null and nodeCode != ''">#{nodeCode},</if>
            <if test="nodeName != null and nodeName != ''">#{nodeName},</if>
            <if test="nodeDescription != null">#{nodeDescription},</if>
            <if test="nodeType != null and nodeType != ''">#{nodeType},</if>
            <if test="nodeLevel != null">#{nodeLevel},</if>
            <if test="streamType != null">#{streamType},</if>
            <if test="hasStreamType != null">#{hasStreamType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="nodePath != null">#{nodePath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniIndustryTree" parameterType="MiniIndustryTree">
        update mini_industry_tree
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="nodeCode != null and nodeCode != ''">node_code = #{nodeCode},</if>
            <if test="nodeName != null and nodeName != ''">node_name = #{nodeName},</if>
            <if test="nodeDescription != null">node_description = #{nodeDescription},</if>
            <if test="nodeType != null and nodeType != ''">node_type = #{nodeType},</if>
            <if test="nodeLevel != null">node_level = #{nodeLevel},</if>
            <if test="streamType != null">stream_type = #{streamType},</if>
            <if test="hasStreamType != null">has_stream_type = #{hasStreamType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="nodePath != null">node_path = #{nodePath},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniIndustryTreeById" parameterType="Long">
        delete from mini_industry_tree where id = #{id}
    </delete>

    <!-- 级联更新子节点状态 -->
    <update id="updateChildrenStatus">
        update mini_industry_tree
        set status = #{status}, update_time = now()
        where parent_id = #{parentId} and deleted = '0'
    </update>

    <!-- 逻辑删除节点 -->
    <update id="deleteMiniIndustryTreeByIds" parameterType="String">
        update mini_industry_tree set deleted = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 级联逻辑删除子节点 -->
    <update id="deleteMiniIndustryTreeCascade" parameterType="Long">
        update mini_industry_tree set deleted = '1'
        where (id = #{id} or parent_id = #{id} or parent_id in (
            select temp.id from (
                select id from mini_industry_tree where parent_id = #{id} and deleted = '0'
            ) temp
        )) and deleted = '0'
    </update>

    <!-- 检查节点是否被企业使用 -->
    <select id="checkNodeUsedByEnterprise" parameterType="Long" resultType="int">
        select count(1) from mini_enterprise_industry where industry_tree_id = #{id}
    </select>

    <!-- 检查节点的子节点是否被企业使用 -->
    <select id="checkChildNodesUsedByEnterprise" parameterType="Long" resultType="int">
        select count(1) from mini_enterprise_industry mei
        where mei.industry_tree_id in (
            select id from mini_industry_tree
            where (parent_id = #{id} or parent_id in (
                select temp.id from (
                    select id from mini_industry_tree where parent_id = #{id} and deleted = '0'
                ) temp
            )) and deleted = '0'
        )
    </select>
    
</mapper> 