package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniTechStar;
import com.ruoyi.miniapp.service.IMiniTechStarService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 科技之星Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "科技之星管理")
@RestController
@RequestMapping("/miniapp/techstar")
public class MiniTechStarController extends BaseController
{
    @Autowired
    private IMiniTechStarService miniTechStarService;

    /**
     * 查询科技之星列表
     */
    @ApiOperation("查询科技之星列表")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") MiniTechStar miniTechStar)
    {
        startPage();
        List<MiniTechStar> list = miniTechStarService.selectMiniTechStarList(miniTechStar);
        return getDataTable(list);
    }

    /**
     * 导出科技之星列表
     */
    @ApiOperation("导出科技之星列表")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:export')")
    @Log(title = "科技之星", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") MiniTechStar miniTechStar)
    {
        List<MiniTechStar> list = miniTechStarService.selectMiniTechStarList(miniTechStar);
        ExcelUtil<MiniTechStar> util = new ExcelUtil<MiniTechStar>(MiniTechStar.class);
        util.exportExcel(response, list, "科技之星数据");
    }

    /**
     * 获取科技之星详细信息
     */
    @ApiOperation("获取科技之星详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:query')")
    @GetMapping(value = "/{starId}")
    public AjaxResult getInfo(@ApiParam("科技之星ID") @PathVariable("starId") Long starId)
    {
        return success(miniTechStarService.selectMiniTechStarByStarId(starId));
    }

    /**
     * 新增科技之星
     */
    @ApiOperation("新增科技之星")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:add')")
    @Log(title = "科技之星", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("科技之星信息") @RequestBody MiniTechStar miniTechStar)
    {
        return toAjax(miniTechStarService.insertMiniTechStar(miniTechStar));
    }

    /**
     * 修改科技之星
     */
    @ApiOperation("修改科技之星")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:edit')")
    @Log(title = "科技之星", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("科技之星信息") @RequestBody MiniTechStar miniTechStar)
    {
        return toAjax(miniTechStarService.updateMiniTechStar(miniTechStar));
    }

    /**
     * 删除科技之星
     */
    @ApiOperation("删除科技之星")
    @PreAuthorize("@ss.hasPermi('miniapp:techstar:remove')")
    @Log(title = "科技之星", businessType = BusinessType.DELETE)
	@DeleteMapping("/{starIds}")
    public AjaxResult remove(@ApiParam("科技之星ID数组") @PathVariable Long[] starIds)
    {
        return toAjax(miniTechStarService.deleteMiniTechStarByStarIds(starIds));
    }

    /**
     * 获取启用的科技之星列表（前端小程序使用）
     */
    @ApiOperation("获取启用的科技之星列表")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<MiniTechStar> list = miniTechStarService.selectEnabledMiniTechStarList();
        return success(list);
    }

} 