package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.miniapp.mapper.GuidanceRegistrationMapper;
import com.ruoyi.miniapp.domain.GuidanceRegistration;
import com.ruoyi.miniapp.service.IGuidanceRegistrationService;

/**
 * 指导活动报名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class GuidanceRegistrationServiceImpl implements IGuidanceRegistrationService 
{
    @Autowired
    private GuidanceRegistrationMapper guidanceRegistrationMapper;

    /**
     * 查询指导活动报名
     *
     * @param registrationId 指导活动报名主键
     * @return 指导活动报名
     */
    @Override
    public GuidanceRegistration selectGuidanceRegistrationByRegistrationId(Long registrationId)
    {
        GuidanceRegistration registration = guidanceRegistrationMapper.selectGuidanceRegistrationByRegistrationId(registrationId);
        if (registration != null) {
            fillUserInfoFromFormData(registration);
        }
        return registration;
    }

    /**
     * 查询指导活动报名列表
     *
     * @param guidanceRegistration 指导活动报名
     * @return 指导活动报名
     */
    @Override
    public List<GuidanceRegistration> selectGuidanceRegistrationList(GuidanceRegistration guidanceRegistration)
    {
        List<GuidanceRegistration> list = guidanceRegistrationMapper.selectGuidanceRegistrationList(guidanceRegistration);
        // 为每个记录填充用户信息
        for (GuidanceRegistration registration : list) {
            fillUserInfoFromFormData(registration);
        }
        return list;
    }

    /**
     * 新增指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    @Override
    public int insertGuidanceRegistration(GuidanceRegistration guidanceRegistration)
    {
        guidanceRegistration.setCreateTime(DateUtils.getNowDate());
        if (guidanceRegistration.getRegistrationTime() == null) {
            guidanceRegistration.setRegistrationTime(DateUtils.getNowDate());
        }
        return guidanceRegistrationMapper.insertGuidanceRegistration(guidanceRegistration);
    }

    /**
     * 修改指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    @Override
    public int updateGuidanceRegistration(GuidanceRegistration guidanceRegistration)
    {
        guidanceRegistration.setUpdateTime(DateUtils.getNowDate());
        return guidanceRegistrationMapper.updateGuidanceRegistration(guidanceRegistration);
    }

    /**
     * 批量删除指导活动报名
     * 
     * @param registrationIds 需要删除的指导活动报名主键
     * @return 结果
     */
    @Override
    public int deleteGuidanceRegistrationByRegistrationIds(Long[] registrationIds)
    {
        return guidanceRegistrationMapper.deleteGuidanceRegistrationByRegistrationIds(registrationIds);
    }

    /**
     * 删除指导活动报名信息
     *
     * @param registrationId 指导活动报名主键
     * @return 结果
     */
    @Override
    public int deleteGuidanceRegistrationByRegistrationId(Long registrationId)
    {
        return guidanceRegistrationMapper.deleteGuidanceRegistrationByRegistrationId(registrationId);
    }

    /**
     * 从formData中填充用户信息
     * 当用户表中没有对应用户时，尝试从formData中提取用户姓名和手机号
     *
     * @param registration 指导活动报名对象
     */
    private void fillUserInfoFromFormData(GuidanceRegistration registration)
    {
        // 如果用户姓名和手机号都为空，尝试从formData中提取
        if ((registration.getUserName() == null || registration.getUserName().isEmpty()) &&
            (registration.getUserPhone() == null || registration.getUserPhone().isEmpty()) &&
            registration.getFormData() != null && !registration.getFormData().isEmpty()) {

            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(registration.getFormData());

                // 尝试从不同的字段名中提取用户姓名
                String userName = null;
                if (jsonNode.has("name")) {
                    userName = jsonNode.get("name").asText();
                } else if (jsonNode.has("team_leader")) {
                    userName = jsonNode.get("team_leader").asText();
                } else if (jsonNode.has("leader_name")) {
                    userName = jsonNode.get("leader_name").asText();
                }

                // 尝试从不同的字段名中提取手机号
                String userPhone = null;
                if (jsonNode.has("phone")) {
                    userPhone = jsonNode.get("phone").asText();
                } else if (jsonNode.has("contact")) {
                    userPhone = jsonNode.get("contact").asText();
                }

                // 设置提取到的用户信息
                if (userName != null && !userName.isEmpty()) {
                    registration.setUserName(userName);
                }
                if (userPhone != null && !userPhone.isEmpty()) {
                    registration.setUserPhone(userPhone);
                }

            } catch (Exception e) {
                // 解析失败时不做处理，保持原有数据
                System.err.println("解析formData失败: " + e.getMessage());
            }
        }
    }
}