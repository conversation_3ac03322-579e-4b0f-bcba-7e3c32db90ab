<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="弹幕内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入弹幕内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布者" prop="userNickname">
        <el-input
          v-model="queryParams.userNickname"
          placeholder="请输入发布者昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="审核通过" value="1" />
          <el-option label="审核拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchApprove"
          v-hasPermi="['miniapp:barrage:audit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="multiple"
          @click="handleBatchReject"
          v-hasPermi="['miniapp:barrage:audit']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:barrage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:barrage:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleConfigManage"
          v-hasPermi="['system:config:edit']"
        >弹幕配置</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="barrageList" @selection-change="handleSelectionChange" row-key="barrageId">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发布者" align="center" width="140">
        <template slot-scope="scope">
          <div class="user-info">
            <el-avatar
              :size="40"
              :src="scope.row.userAvatarUrl"
              class="user-avatar"
              fit="cover"
            >
              <i class="el-icon-user-solid"></i>
            </el-avatar>
            <div class="user-details">
              <div class="user-nickname">{{ scope.row.userNickName || '未设置昵称' }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="弹幕内容" align="left" prop="content" min-width="200">
        <template slot-scope="scope">
          <div class="barrage-content-cell">
            {{ scope.row.content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getAuditStatusType(scope.row.auditStatus)"
            size="small"
          >
            {{ getAuditStatusText(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <div class="time-info">
            <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>
            <div class="time-detail">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.auditTime" class="time-info">
            <div>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</div>
            <div class="time-detail">{{ parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}</div>
          </div>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.auditStatus === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row, '1')"
            v-hasPermi="['miniapp:barrage:audit']"
            style="color: #67C23A;"
          >通过</el-button>
          <el-button
            v-if="scope.row.auditStatus === '0'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleAudit(scope.row, '2')"
            v-hasPermi="['miniapp:barrage:audit']"
            style="color: #F56C6C;"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['miniapp:barrage:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:barrage:remove']"
            style="color: #F56C6C;"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹幕详情对话框 -->
    <el-dialog title="弹幕详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="发布者信息" :span="2">
          <div class="user-info-detail">
            <el-avatar
              :size="50"
              :src="barrageDetail.userAvatarUrl"
              class="user-avatar-detail"
              fit="cover"
            >
              <i class="el-icon-user-solid"></i>
            </el-avatar>
            <div class="user-details-detail">
              <div class="user-nickname-detail">{{ barrageDetail.userNickName || '未设置昵称' }}</div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="弹幕内容" :span="2">
          <div class="barrage-content">{{ barrageDetail.content }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getAuditStatusType(barrageDetail.auditStatus)">
            {{ getAuditStatusText(barrageDetail.auditStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审核时间">
          {{ barrageDetail.auditTime ? parseTime(barrageDetail.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核备注" :span="2" v-if="barrageDetail.auditRemark">
          {{ barrageDetail.auditRemark }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间" :span="2">
          {{ parseTime(barrageDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审核操作 -->
      <div v-if="barrageDetail.auditStatus === '0'" style="margin-top: 20px; text-align: center;">
        <el-button type="success" @click="handleAudit(barrageDetail, '1')">审核通过</el-button>
        <el-button type="danger" @click="handleAudit(barrageDetail, '2')">审核拒绝</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="auditTitle" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="审核结果">
          <el-tag :type="auditForm.auditStatus === '1' ? 'success' : 'danger'">
            {{ auditForm.auditStatus === '1' ? '审核通过' : '审核拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            placeholder="请输入审核备注（可选）"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量拒绝对话框 -->
    <el-dialog title="批量拒绝" :visible.sync="batchRejectOpen" width="500px" append-to-body>
      <el-form ref="batchRejectForm" :model="batchRejectForm" :rules="batchRejectRules" label-width="100px">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            v-model="batchRejectForm.rejectReason"
            type="textarea"
            placeholder="请输入拒绝原因"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchReject">确 定</el-button>
        <el-button @click="batchRejectOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 弹幕配置管理对话框 -->
    <el-dialog title="弹幕配置管理" :visible.sync="configOpen" width="600px" append-to-body>
      <el-form ref="configForm" :model="configForm" :rules="configRules" label-width="120px">
        <el-form-item label="弹幕行数" prop="rows">
          <el-input-number
            v-model="configForm.rows"
            :min="1"
            :max="3"
            controls-position="right"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">最大值为3行</span>
        </el-form-item>
        <el-form-item label="弹幕滚动速度" prop="speed">
          <el-input-number
            v-model="configForm.speed"
            :min="1"
            :max="50"
            controls-position="right"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">数值越大滚动越快</span>
        </el-form-item>
        <el-form-item label="弹幕发送间隔" prop="interval">
          <el-input-number
            v-model="configForm.interval"
            :min="1"
            :max="60"
            controls-position="right"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">单位：秒</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitConfig" :loading="configLoading">确 定</el-button>
        <el-button @click="configOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBarrage, getBarrage, delBarrage, auditBarrage, batchApproveBarrage, batchRejectBarrage, exportBarrage, getBarrageConfig } from "@/api/miniapp/barrage";
import { listConfig, updateConfig } from "@/api/system/config";

export default {
  name: "MiniBarrage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹幕表格数据
      barrageList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 是否显示批量拒绝弹出层
      batchRejectOpen: false,
      // 是否显示配置管理弹出层
      configOpen: false,
      // 配置加载状态
      configLoading: false,
      // 审核标题
      auditTitle: "",
      // 当前弹幕
      currentBarrage: {},
      // 日期范围
      dateRange: [],
      // 弹幕详情
      barrageDetail: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        content: null,
        userNickname: null,
        auditStatus: null
      },
      // 审核表单参数
      auditForm: {
        barrageId: null,
        auditStatus: null,
        auditRemark: ''
      },
      // 批量拒绝表单参数
      batchRejectForm: {
        rejectReason: ''
      },
      // 配置表单参数
      configForm: {
        rows: 2,
        speed: 10,
        interval: 6
      },
      // 审核表单校验
      auditRules: {
        // 审核备注改为非必填
      },
      // 批量拒绝表单校验
      batchRejectRules: {
        rejectReason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      },
      // 配置表单校验
      configRules: {
        rows: [
          { required: true, message: "弹幕行数不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 3, message: "弹幕行数必须在1-3之间", trigger: "blur" }
        ],
        speed: [
          { required: true, message: "弹幕滚动速度不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 50, message: "弹幕滚动速度必须在1-50之间", trigger: "blur" }
        ],
        interval: [
          { required: true, message: "弹幕发送间隔不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 60, message: "弹幕发送间隔必须在1-60秒之间", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询弹幕列表 */
    getList() {
      this.loading = true;
      let params = this.addDateRange(this.queryParams, this.dateRange);
      listBarrage(params).then(response => {
        this.barrageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.barrageId);
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      getBarrage(row.barrageId).then(response => {
        this.barrageDetail = response.data;
        this.detailOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row, auditStatus) {
      this.currentBarrage = row;
      this.auditForm = {
        barrageId: row.barrageId,
        auditStatus: auditStatus,
        auditRemark: ''
      };
      this.auditTitle = auditStatus === '1' ? '审核通过' : '审核拒绝';
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      // 直接提交，不需要表单验证
      auditBarrage(this.auditForm).then(() => {
        this.$modal.msgSuccess("审核成功");
        this.auditOpen = false;
        this.detailOpen = false;
        this.getList();
      });
    },
    /** 批量审核通过 */
    handleBatchApprove() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审核的弹幕");
        return;
      }
      this.$modal.confirm('是否确认批量审核通过选中的弹幕？').then(() => {
        return batchApproveBarrage(this.ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量审核通过成功");
      }).catch(() => {});
    },
    /** 批量审核拒绝 */
    handleBatchReject() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要拒绝的弹幕");
        return;
      }
      this.batchRejectForm.rejectReason = '';
      this.batchRejectOpen = true;
    },
    /** 提交批量拒绝 */
    submitBatchReject() {
      this.$refs.batchRejectForm.validate(valid => {
        if (valid) {
          const params = {
            barrageIds: this.ids,
            rejectReason: this.batchRejectForm.rejectReason
          };
          batchRejectBarrage(params).then(() => {
            this.$modal.msgSuccess("批量拒绝成功");
            this.batchRejectOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const barrageIds = row.barrageId || this.ids;
      this.$modal.confirm('是否确认删除弹幕编号为"' + barrageIds + '"的数据项？').then(function() {
        return delBarrage(barrageIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      let params = this.addDateRange(this.queryParams, this.dateRange);
      this.$modal.confirm('是否确认导出所有弹幕数据项？').then(() => {
        this.loading = true;
        return exportBarrage(params);
      }).then(response => {
        this.$download.excel(response, '弹幕数据.xlsx');
        this.loading = false;
      }).catch(() => {});
    },
    /** 获取审核状态类型 */
    getAuditStatusType(status) {
      const statusMap = {
        '0': 'warning',
        '1': 'success',
        '2': 'danger'
      };
      return statusMap[status] || 'info';
    },
    /** 获取审核状态文本 */
    getAuditStatusText(status) {
      const statusMap = {
        '0': '待审核',
        '1': '审核通过',
        '2': '审核拒绝'
      };
      return statusMap[status] || '未知';
    },
    /** 配置管理按钮操作 */
    handleConfigManage() {
      this.loadBarrageConfig();
      this.configOpen = true;
    },
    /** 加载弹幕配置 */
    loadBarrageConfig() {
      getBarrageConfig().then(response => {
        const config = response.data;
        this.configForm = {
          rows: config.rows || 2,
          speed: config.speed || 10,
          interval: config.interval || 6
        };
      }).catch(() => {
        this.$modal.msgError("获取配置失败");
      });
    },
    /** 提交配置 */
    submitConfig() {
      this.$refs.configForm.validate(valid => {
        if (valid) {
          this.configLoading = true;

          // 准备要更新的配置键名和值
          const configKeys = [
            'danmaku.display.rows',
            'danmaku.scroll.speed',
            'danmaku.send.interval'
          ];

          const configValues = [
            this.configForm.rows.toString(),
            this.configForm.speed.toString(),
            this.configForm.interval.toString()
          ];

          // 先获取所有相关配置
          this.getExistingConfigs(configKeys, configValues);
        }
      });
    },
    /** 获取现有配置 */
    getExistingConfigs(configKeys, configValues) {
      // 查询系统配置，获取这三个配置的详细信息
      listConfig({
        pageNum: 1,
        pageSize: 100,
        configKey: ''  // 获取所有配置，然后过滤
      }).then(response => {
        const allConfigs = response.rows || [];
        const targetConfigs = allConfigs.filter(config =>
          configKeys.includes(config.configKey)
        );

        // 更新现有配置
        this.updateExistingConfigs(targetConfigs, configKeys, configValues);
      }).catch(error => {
        this.configLoading = false;
        console.error('获取配置列表失败:', error);
        this.$modal.msgError("获取配置信息失败");
      });
    },
    /** 更新现有配置 */
    updateExistingConfigs(existingConfigs, configKeys, configValues) {
      let updateCount = 0;
      const totalCount = configKeys.length;

      configKeys.forEach((key, index) => {
        const existingConfig = existingConfigs.find(config => config.configKey === key);

        if (existingConfig) {
          // 更新现有配置
          const updateData = {
            ...existingConfig,
            configValue: configValues[index]
          };

          updateConfig(updateData).then(() => {
            updateCount++;
            if (updateCount === totalCount) {
              this.configLoading = false;
              this.$modal.msgSuccess("配置更新成功");
              this.configOpen = false;
            }
          }).catch(error => {
            this.configLoading = false;
            console.error(`更新配置 ${key} 失败:`, error);
            this.$modal.msgError(`更新配置失败: ${error.message || '未知错误'}`);
          });
        } else {
          // 配置不存在，记录错误
          console.warn(`配置 ${key} 不存在`);
          updateCount++;
          if (updateCount === totalCount) {
            this.configLoading = false;
            this.$modal.msgWarning("部分配置不存在，请联系管理员");
            this.configOpen = false;
          }
        }
      });
    },

  }
};
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
  border: 2px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-nickname {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
  text-align: center;
}

.barrage-content {
  word-break: break-all;
  white-space: pre-wrap;
}

.barrage-content-cell {
  max-width: 300px;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
  padding: 8px 0;
}

.time-info {
  text-align: center;
}

.time-detail {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

/* 操作按钮样式 */
.el-table .el-table__cell .el-button + .el-button {
  margin-left: 5px;
}

/* 防止操作按钮换行 */
::v-deep .el-table .small-padding .cell {
  white-space: nowrap;
  overflow: hidden;
}

::v-deep .el-table .fixed-width .cell {
  white-space: nowrap;
}

/* 表格行高度调整 */
::v-deep .el-table .el-table__row {
  height: auto;
}

::v-deep .el-table .el-table__cell {
  padding: 12px 0;
}

/* 详情对话框样式 */
.user-info-detail {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar-detail {
  flex-shrink: 0;
  border: 2px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-details-detail {
  flex: 1;
}

.user-nickname-detail {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .el-table .el-table__cell .el-button {
    margin: 2px;
    font-size: 12px;
  }
}
</style>
