package com.ruoyi.miniapp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 微信公众号配置DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel("微信公众号配置")
public class WechatConfigDTO
{
    /** 配置ID */
    @ApiModelProperty("配置ID")
    private String configId;

    /** 公众号名称 */
    @ApiModelProperty("公众号名称")
    private String name;

    /** 公众号AppID */
    @ApiModelProperty("公众号AppID")
    private String appId;

    /** 公众号AppSecret */
    @ApiModelProperty("公众号AppSecret")
    private String appSecret;

    /** AccessToken */
    @ApiModelProperty("AccessToken")
    private String accessToken;

    /** Token过期时间 */
    @ApiModelProperty("Token过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tokenExpireTime;

    /** 是否启用 */
    @ApiModelProperty("是否启用")
    private Boolean enabled;

    /** 最后同步时间 */
    @ApiModelProperty("最后同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSyncTime;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Date getTokenExpireTime() {
        return tokenExpireTime;
    }

    public void setTokenExpireTime(Date tokenExpireTime) {
        this.tokenExpireTime = tokenExpireTime;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getLastSyncTime() {
        return lastSyncTime;
    }

    public void setLastSyncTime(Date lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "WechatConfigDTO{" +
                "configId='" + configId + '\'' +
                ", name='" + name + '\'' +
                ", appId='" + appId + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", tokenExpireTime=" + tokenExpireTime +
                ", enabled=" + enabled +
                ", lastSyncTime=" + lastSyncTime +
                ", remark='" + remark + '\'' +
                '}';
    }
}
