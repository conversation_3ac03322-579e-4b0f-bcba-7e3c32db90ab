package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.SensitiveWordLog;
import com.ruoyi.miniapp.service.ISensitiveWordLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 敏感词检测日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/miniapp/sensitiveWordLog")
public class SensitiveWordLogController extends BaseController
{
    @Autowired
    private ISensitiveWordLogService sensitiveWordLogService;

    /**
     * 查询敏感词检测日志列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SensitiveWordLog sensitiveWordLog)
    {
        startPage();
        List<SensitiveWordLog> list = sensitiveWordLogService.selectSensitiveWordLogList(sensitiveWordLog);
        return getDataTable(list);
    }

    /**
     * 导出敏感词检测日志列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:export')")
    @Log(title = "敏感词检测日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SensitiveWordLog sensitiveWordLog)
    {
        List<SensitiveWordLog> list = sensitiveWordLogService.selectSensitiveWordLogList(sensitiveWordLog);
        ExcelUtil<SensitiveWordLog> util = new ExcelUtil<SensitiveWordLog>(SensitiveWordLog.class);
        util.exportExcel(response, list, "敏感词检测日志数据");
    }

    /**
     * 获取敏感词检测日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return AjaxResult.success(sensitiveWordLogService.selectSensitiveWordLogByLogId(logId));
    }

    /**
     * 根据用户ID查询敏感词检测日志
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getLogsByUserId(@PathVariable("userId") Long userId)
    {
        List<SensitiveWordLog> list = sensitiveWordLogService.selectSensitiveWordLogByUserId(userId);
        return AjaxResult.success(list);
    }

    /**
     * 根据模块名称查询敏感词检测日志
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:list')")
    @GetMapping("/module/{moduleName}")
    public AjaxResult getLogsByModuleName(@PathVariable("moduleName") String moduleName)
    {
        List<SensitiveWordLog> list = sensitiveWordLogService.selectSensitiveWordLogByModuleName(moduleName);
        return AjaxResult.success(list);
    }

    /**
     * 删除敏感词检测日志
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:remove')")
    @Log(title = "敏感词检测日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(sensitiveWordLogService.deleteSensitiveWordLogByLogIds(logIds));
    }

    /**
     * 清理指定天数前的日志
     */
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWordLog:clean')")
    @Log(title = "敏感词检测日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult cleanLogs(@RequestParam(value = "days", defaultValue = "30") int days)
    {
        int result = sensitiveWordLogService.cleanSensitiveWordLogByDays(days);
        return AjaxResult.success("清理完成，共清理 " + result + " 条日志");
    }
}
