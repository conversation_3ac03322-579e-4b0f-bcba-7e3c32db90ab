# 小程序注册登录API接口文档

## 概述

本文档描述了小程序用户注册登录相关的API接口，包括微信登录注册和获取用户信息功能。

## 接口列表

### 1. 微信登录注册

**接口地址**: `POST /miniapp/user/app/weixinLogin`

**接口描述**: 小程序用户通过微信登录凭证code进行登录注册，后端会自动调用微信接口获取openid，如果用户不存在则自动注册，存在则更新登录信息。

**请求参数**:
```json
{
  "code": "wx_login_code_123456789",
  "nickName": "张三",
  "avatar": "https://example.com/avatar.jpg",
  "weixinNickname": "张三",
  "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/avatar.jpg",
  "phonenumber": "13800138001",
  "realName": "张三",
  "sex": "0"
}
```

**参数说明**:
- `code` (必填): 微信登录凭证（通过wx.login()获取）
- `nickName` (可选): 用户昵称（优先级高于微信昵称）
- `avatar` (可选): 用户头像URL（优先级高于微信头像）
- `weixinNickname` (可选): 微信昵称
- `weixinAvatar` (可选): 微信头像URL
- `phonenumber` (可选): 手机号码
- `realName` (可选): 真实姓名
- `sex` (可选): 性别（0男 1女 2未知）

**注意**: openid、unionid、sessionKey等字段会由后端通过code自动获取，无需前端传递。

**响应数据**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "userId": 100,
    "userName": "mini_12345678_9012",
    "nickName": "张三",
    "weixinNickname": "张三",
    "avatar": "https://wx.qlogo.cn/mmopen/vi_32/avatar.jpg",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/avatar.jpg",
    "realName": "张三",
    "phonenumber": "13800138001",
    "sex": "0",
    "totalPoints": 0,
    "isNewUser": true
  }
}
```

### 2. 获取用户信息

**接口地址**: `GET /miniapp/user/app/getUserInfo`

**接口描述**: 获取当前登录用户的详细信息。

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "userId": 100,
    "userName": "mini_12345678_9012",
    "nickName": "张三",
    "weixinNickname": "张三",
    "avatar": "https://wx.qlogo.cn/mmopen/vi_32/avatar.jpg",
    "weixinAvatar": "https://wx.qlogo.cn/mmopen/vi_32/avatar.jpg",
    "realName": "张三",
    "phonenumber": "13800138001",
    "sex": "0",
    "totalPoints": 0,
    "isNewUser": false
  }
}
```

## 业务逻辑说明

### 用户注册流程

1. **检查用户是否存在**: 通过OpenID查询数据库中是否已存在该用户
2. **新用户注册**:
   - 生成唯一用户名（格式：mini_{openid后8位}_{时间戳后4位}）
   - 设置用户类型为"01"（小程序用户）
   - 设置昵称和头像（按优先级处理）
   - 初始化积分为0
   - 分配小程序用户角色（role_id = 101）
   - 保存用户信息到sys_user表
   - 建立用户角色关联关系到sys_user_role表
3. **已存在用户**:
   - 更新昵称和头像（按优先级处理）
   - 更新微信相关信息（UnionID、SessionKey等）
   - 更新最后登录时间
   - 如果提供了新的个人信息，则更新相应字段

### 头像和昵称优先级

系统按以下优先级处理用户头像和昵称：

**昵称优先级**:
1. `nickName` - 用户自定义昵称（最高优先级）
2. `weixinNickname` - 微信昵称
3. "小程序用户" - 默认昵称

**头像优先级**:
1. `avatar` - 用户自定义头像URL（最高优先级）
2. `weixinAvatar` - 微信头像URL
3. 空字符串 - 默认无头像

### 权限设置

- 新注册用户自动分配小程序用户角色（role_id = 101）
- 用户类型设置为"01"，便于区分系统用户和小程序用户
- 生成JWT token用于后续API调用的身份验证

### 数据库变更

1. **sys_user表**: 用户类型为"01"的记录
2. **sys_user_role表**: 用户与小程序角色的关联关系

## 错误处理

### 常见错误码

- `400`: 参数错误（如OpenID为空）
- `401`: 用户未登录或token无效
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": 400,
  "msg": "OpenID不能为空",
  "data": null
}
```

## 使用示例

### 小程序端调用示例

```javascript
// 完整的微信小程序登录流程
wx.login({
  success: function(loginRes) {
    if (loginRes.code) {
      // 获取用户信息（可选）
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function(userRes) {
          // 发送登录请求
          wx.request({
            url: 'https://api.example.com/miniapp/user/app/weixinLogin',
            method: 'POST',
            data: {
              code: loginRes.code,
              weixinNickname: userRes.userInfo.nickName,
              weixinAvatar: userRes.userInfo.avatarUrl
            },
            success: function(res) {
              if (res.data.code === 200) {
                // 保存token
                wx.setStorageSync('token', res.data.data.token);
                console.log('登录成功', res.data.data);
              }
            }
          });
        },
        fail: function() {
          // 用户拒绝授权，仍可以登录但没有用户信息
          wx.request({
            url: 'https://api.example.com/miniapp/user/app/weixinLogin',
            method: 'POST',
            data: {
              code: loginRes.code
            },
            success: function(res) {
              if (res.data.code === 200) {
                wx.setStorageSync('token', res.data.data.token);
                console.log('登录成功', res.data.data);
              }
            }
          });
        }
      });
    }
  }
});

// 简化版登录（仅使用code）
wx.login({
  success: function(res) {
    if (res.code) {
      wx.request({
        url: 'https://api.example.com/miniapp/user/app/weixinLogin',
        method: 'POST',
        data: {
          code: res.code
        },
        success: function(res) {
          if (res.data.code === 200) {
            wx.setStorageSync('token', res.data.data.token);
            console.log('登录成功', res.data.data);
          }
        }
      });
    }
  }
});

// 获取用户信息
wx.request({
  url: 'https://api.example.com/miniapp/user/app/getUserInfo',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('token')
  },
  success: function(res) {
    if (res.data.code === 200) {
      console.log('用户信息', res.data.data);
    }
  }
});
```

## 注意事项

1. **OpenID唯一性**: 每个微信用户的OpenID是唯一的，系统通过OpenID来识别用户身份
2. **Token管理**: 登录成功后返回的token需要在后续API调用中携带
3. **用户类型**: 小程序用户的user_type固定为"01"
4. **角色权限**: 小程序用户默认分配role_id为101的角色
5. **数据更新**: 每次登录都会更新用户的微信相关信息和最后登录时间
