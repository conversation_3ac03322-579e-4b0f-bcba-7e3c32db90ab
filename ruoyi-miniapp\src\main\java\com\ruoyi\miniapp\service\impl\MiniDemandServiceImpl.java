package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniDemand;
import com.ruoyi.miniapp.mapper.MiniDemandMapper;
import com.ruoyi.miniapp.service.IMiniDemandService;

/**
 * 需求信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniDemandServiceImpl implements IMiniDemandService 
{
    @Autowired
    private MiniDemandMapper miniDemandMapper;

    /**
     * 查询需求信息
     * 
     * @param demandId 需求信息主键
     * @return 需求信息
     */
    @Override
    public MiniDemand selectMiniDemandByDemandId(Long demandId)
    {
        return miniDemandMapper.selectMiniDemandByDemandId(demandId);
    }

    /**
     * 查询需求信息列表
     * 
     * @param miniDemand 需求信息
     * @return 需求信息
     */
    @Override
    public List<MiniDemand> selectMiniDemandList(MiniDemand miniDemand)
    {
        return miniDemandMapper.selectMiniDemandList(miniDemand);
    }

    /**
     * 新增需求信息
     * 
     * @param miniDemand 需求信息
     * @return 结果
     */
    @Override
    public int insertMiniDemand(MiniDemand miniDemand)
    {
        miniDemand.setCreateTime(DateUtils.getNowDate());
        return miniDemandMapper.insertMiniDemand(miniDemand);
    }

    /**
     * 修改需求信息
     * 
     * @param miniDemand 需求信息
     * @return 结果
     */
    @Override
    public int updateMiniDemand(MiniDemand miniDemand)
    {
        miniDemand.setUpdateTime(DateUtils.getNowDate());
        return miniDemandMapper.updateMiniDemand(miniDemand);
    }

    /**
     * 批量删除需求信息
     * 
     * @param demandIds 需要删除的需求信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandByDemandIds(Long[] demandIds)
    {
        return miniDemandMapper.deleteMiniDemandByDemandIds(demandIds);
    }

    /**
     * 删除需求信息信息
     * 
     * @param demandId 需求信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandByDemandId(Long demandId)
    {
        return miniDemandMapper.deleteMiniDemandByDemandId(demandId);
    }

    /**
     * 根据分类ID查询需求信息列表
     * 
     * @param categoryId 分类ID
     * @return 需求信息集合
     */
    @Override
    public List<MiniDemand> selectMiniDemandsByCategoryId(Long categoryId)
    {
        return miniDemandMapper.selectMiniDemandsByCategoryId(categoryId);
    }

    /**
     * 查询推荐的需求信息列表（小程序端调用）
     * 
     * @return 需求信息集合
     */
    @Override
    public List<MiniDemand> selectRecommendedMiniDemandList()
    {
        return miniDemandMapper.selectRecommendedMiniDemandList();
    }

    /**
     * 查询启用的需求信息列表（小程序端调用）
     * 
     * @return 需求信息集合
     */
    @Override
    public List<MiniDemand> selectEnabledMiniDemandList()
    {
        return miniDemandMapper.selectEnabledMiniDemandList();
    }

    /**
     * 查询分类下的需求信息列表（小程序端调用）
     * 
     * @param categoryId 分类ID
     * @return 需求信息集合
     */
    @Override
    public List<MiniDemand> selectMiniDemandListByCategoryId(Long categoryId)
    {
        return miniDemandMapper.selectMiniDemandListByCategoryId(categoryId);
    }

    /**
     * 增加浏览次数
     * 
     * @param demandId 需求信息主键
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long demandId)
    {
        return miniDemandMapper.increaseViewCount(demandId);
    }

    /**
     * 查询用户发布的需求信息列表
     * 
     * @param userId 用户ID
     * @return 需求信息集合
     */
    @Override
    public List<MiniDemand> selectMiniDemandListByUserId(Long userId)
    {
        return miniDemandMapper.selectMiniDemandListByUserId(userId);
    }
} 