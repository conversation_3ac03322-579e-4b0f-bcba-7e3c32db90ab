import request from '@/utils/request'

// 查询项目指导活动列表
export function listGuidance(query) {
  // 添加活动类型过滤，只查询项目指导类型的活动
  const params = { ...query, eventType: 'guidance' }
  return request({
    url: '/miniapp/event/list',
    method: 'post',
    data: params
  })
}

// 查询项目指导活动详细
export function getGuidance(guidanceId) {
  return request({
    url: '/miniapp/event/getInfo',
    method: 'post',
    data: guidanceId
  })
}

// 新增项目指导活动
export function addGuidance(data) {
  // 设置活动类型为项目指导
  const guidanceData = { ...data, eventType: 'guidance' }
  return request({
    url: '/miniapp/event/add',
    method: 'post',
    data: guidanceData
  })
}

// 修改项目指导活动
export function updateGuidance(data) {
  // 确保活动类型为项目指导
  const guidanceData = { ...data, eventType: 'guidance' }
  return request({
    url: '/miniapp/event/edit',
    method: 'post',
    data: guidanceData
  })
}

// 删除项目指导活动
export function delGuidance(guidanceId) {
  return request({
    url: '/miniapp/event/remove',
    method: 'post',
    data: guidanceId
  })
}

// 导出项目指导活动
export function exportGuidance(query) {
  // 添加活动类型过滤，只导出项目指导类型的活动
  const params = { ...query, eventType: 'guidance' }
  return request({
    url: '/miniapp/event/export',
    method: 'post',
    data: params
  })
}