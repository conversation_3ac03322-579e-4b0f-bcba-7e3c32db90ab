-- 修复新闻中心表字符集配置
-- 执行时间：2025-01-17
-- 说明：修复数据库表字符集配置，解决中文乱码问题

-- 1. 检查表是否存在，如果存在则先备份数据
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = 'mini_news_center');

-- 2. 如果表存在，先备份数据
DROP TABLE IF EXISTS `mini_news_center_backup`;
CREATE TABLE IF NOT EXISTS `mini_news_center_backup` AS 
SELECT * FROM `mini_news_center` WHERE @table_exists > 0;

-- 3. 删除原表
DROP TABLE IF EXISTS `mini_news_center`;

-- 4. 创建正确的表结构，使用utf8mb4字符集确保中文正确存储
CREATE TABLE `mini_news_center` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '新闻ID',
    `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章标题',
    `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '作者',
    `thumb_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封面图片',
    `digest` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文章摘要',
    `wechat_article_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信文章ID（唯一标识）',
    `wechat_article_url` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信文章链接',
    `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `wechat_create_time` timestamp NULL DEFAULT NULL COMMENT '微信文章创建时间',
    `sync_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mini_news_center_wechat_article_id` (`wechat_article_id`),
    KEY `idx_mini_news_center_wechat_create_time` (`wechat_create_time` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻中心表（微信公众号文章同步）';

-- 5. 如果有备份数据，尝试迁移（需要手动调整字段映射）
-- 注意：由于字段名称可能变化，需要手动执行数据迁移
-- 如果备份表存在数据，请手动执行以下语句进行数据迁移：
/*
-- 如果原表使用的是 wechat_media_id 字段
INSERT INTO mini_news_center (
    id, title, thumb_url, digest, wechat_article_id, wechat_article_url, 
    wechat_create_time, sync_time, created_at, updated_at
) 
SELECT 
    id, title, thumb_url, digest, wechat_media_id, wechat_article_url,
    wechat_create_time, sync_time, created_at, updated_at
FROM mini_news_center_backup;

-- 如果原表已经使用 wechat_article_id 字段
INSERT INTO mini_news_center (
    id, title, author, thumb_url, digest, wechat_article_id, wechat_article_url, 
    status, wechat_create_time, sync_time, created_at, updated_at
) 
SELECT 
    id, title, author, thumb_url, digest, wechat_article_id, wechat_article_url,
    status, wechat_create_time, sync_time, created_at, updated_at
FROM mini_news_center_backup;
*/

-- 6. 添加微信公众号AccessToken配置（如果不存在）
INSERT IGNORE INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES ('微信公众号AccessToken', 'wechat.access.token', '94_zCJuDXSu955FpzjxPsmFbUdG-F-1uDCC7-OqnD7z3DYHc84Pm74myqdqbW3UfAln5yvm9a6PgOcRhrxsdG1PAM7S4nsEMKPXqP_DZfvOCagqmpV5hI8Xa4mE9tILBBjADAAQQ', 'Y', 'admin', NOW(), 'admin', NOW(), '微信公众号API访问令牌，用于同步文章');

-- 7. 清理备份表（可选，建议保留一段时间后再删除）
-- DROP TABLE IF EXISTS `mini_news_center_backup`;

SELECT '新闻中心表字符集修复完成！请检查数据是否正常显示。' as message;
