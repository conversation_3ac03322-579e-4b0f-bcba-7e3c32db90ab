package com.ruoyi.miniapp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.miniapp.config.WechatMiniappConfig;
import com.ruoyi.miniapp.domain.dto.WechatJscode2sessionResponse;
import com.ruoyi.miniapp.utils.WechatDecryptUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 微信小程序API服务
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class WechatMiniappService
{
    private static final Logger logger = LoggerFactory.getLogger(WechatMiniappService.class);

    @Autowired
    private WechatMiniappConfig wechatConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 通过code换取openid和session_key
     * 
     * @param code 微信登录凭证
     * @return 微信API响应
     */
    public WechatJscode2sessionResponse jscode2session(String code)
    {
        try
        {
            // 构建请求URL
            String url = buildJscode2sessionUrl(code);
            
            logger.info("调用微信jscode2session接口，URL: {}", url.replaceAll("secret=[^&]*", "secret=[HIDDEN]"));
            
            // 调用微信API
            String response = restTemplate.getForObject(url, String.class);
            
            logger.info("微信jscode2session接口响应: {}", response);
            
            // 解析响应
            WechatJscode2sessionResponse result = objectMapper.readValue(response, WechatJscode2sessionResponse.class);
            
            if (!result.isSuccess())
            {
                logger.error("微信jscode2session接口调用失败，errcode: {}, errmsg: {}", 
                           result.getErrcode(), result.getErrmsg());
                throw new RuntimeException("微信接口调用失败: " + result.getErrmsg());
            }
            
            return result;
        }
        catch (Exception e)
        {
            logger.error("调用微信jscode2session接口异常", e);
            throw new RuntimeException("获取微信用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建jscode2session请求URL
     */
    private String buildJscode2sessionUrl(String code)
    {
        StringBuilder url = new StringBuilder();
        url.append(wechatConfig.getFullJscode2sessionUrl());
        url.append("?appid=").append(wechatConfig.getAppid());
        url.append("&secret=").append(wechatConfig.getSecret());
        url.append("&js_code=").append(code);
        url.append("&grant_type=authorization_code");
        
        return url.toString();
    }

    /**
     * 解密手机号信息
     *
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 手机号信息
     */
    public WechatDecryptUtils.WechatPhoneInfo decryptPhoneNumber(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            logger.info("开始解密手机号信息");
            WechatDecryptUtils.WechatPhoneInfo phoneInfo = WechatDecryptUtils.decryptPhoneNumber(encryptedData, sessionKey, iv);
            logger.info("手机号解密成功: {}", phoneInfo.getPurePhoneNumber());
            return phoneInfo;
        }
        catch (Exception e)
        {
            logger.error("解密手机号失败", e);
            throw new RuntimeException("解密手机号失败: " + e.getMessage());
        }
    }

    /**
     * 解密用户信息
     *
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 用户信息
     */
    public WechatDecryptUtils.WechatUserInfo decryptUserInfo(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            logger.info("开始解密用户信息");
            WechatDecryptUtils.WechatUserInfo userInfo = WechatDecryptUtils.decryptUserInfo(encryptedData, sessionKey, iv);
            logger.info("用户信息解密成功: {}", userInfo.getNickName());
            return userInfo;
        }
        catch (Exception e)
        {
            logger.error("解密用户信息失败", e);
            throw new RuntimeException("解密用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证配置是否完整
     */
    public boolean isConfigValid()
    {
        return wechatConfig.getAppid() != null && !wechatConfig.getAppid().isEmpty() &&
               wechatConfig.getSecret() != null && !wechatConfig.getSecret().isEmpty() &&
               !wechatConfig.getAppid().equals("your_miniapp_appid") &&
               !wechatConfig.getSecret().equals("your_miniapp_secret");
    }
}
