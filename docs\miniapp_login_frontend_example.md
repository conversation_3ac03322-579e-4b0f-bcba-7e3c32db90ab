# 小程序登录前端实现示例

## 概述

本文档提供了小程序登录的前端实现示例，包括智能登录流程和用户状态检查。

## API 接口说明

### 1. 检查用户状态接口
- **接口**: `POST /miniapp/user/checkUserStatus`
- **说明**: 检查用户是否已注册，是否需要手机号授权
- **参数**: 
  ```json
  {
    "code": "微信登录凭证"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "msg": "检查成功",
    "data": {
      "isRegistered": true,
      "needPhoneAuth": false,
      "openid": "用户openid",
      "userInfo": {
        "userId": 123,
        "nickName": "用户昵称",
        "avatar": "头像URL",
        "hasPhone": true,
        "hasRealName": false,
        "totalPoints": 100
      }
    }
  }
  ```

### 2. 登录注册接口
- **接口**: `POST /miniapp/user/weixinLogin`
- **说明**: 统一的登录注册接口
- **参数**: 
  ```json
  {
    "code": "微信登录凭证",
    "encryptedData": "加密数据（可选）",
    "iv": "初始向量（可选）",
    "nickName": "用户昵称（可选）",
    "avatar": "头像URL（可选）"
  }
  ```

## 前端实现示例

### 1. 登录工具类 (login-utils.js)

```javascript
/**
 * 小程序登录工具类
 */
class LoginUtils {
  
  /**
   * 智能登录流程
   */
  static smartLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            this.checkUserStatusAndLogin(res.code)
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error('获取微信登录凭证失败'));
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 检查用户状态并执行相应登录流程
   */
  static checkUserStatusAndLogin(code) {
    return new Promise((resolve, reject) => {
      // 先检查用户状态
      this.checkUserStatus(code)
        .then((statusResult) => {
          const { isRegistered, needPhoneAuth, userInfo } = statusResult.data;
          
          if (isRegistered && !needPhoneAuth) {
            // 老用户且信息完整，直接登录
            console.log('老用户直接登录');
            this.simpleLogin(code)
              .then(resolve)
              .catch(reject);
          } else {
            // 新用户或需要补充手机号
            console.log('需要手机号授权');
            this.showPhoneAuthDialog(code)
              .then(resolve)
              .catch(reject);
          }
        })
        .catch((error) => {
          console.error('检查用户状态失败，尝试直接登录:', error);
          // 如果检查状态失败，尝试直接登录
          this.simpleLogin(code)
            .then(resolve)
            .catch(reject);
        });
    });
  }

  /**
   * 检查用户状态
   */
  static checkUserStatus(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.getBaseUrl()}/miniapp/user/checkUserStatus`,
        method: 'POST',
        data: { code },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.msg || '检查用户状态失败'));
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 简单登录（无需手机号授权）
   */
  static simpleLogin(code, userInfo = {}) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.getBaseUrl()}/miniapp/user/weixinLogin`,
        method: 'POST',
        data: {
          code,
          ...userInfo
        },
        success: (res) => {
          if (res.data.code === 200) {
            const { token, isNewUser } = res.data.data;
            
            // 保存token
            wx.setStorageSync('token', token);
            wx.setStorageSync('userInfo', res.data.data);
            
            resolve({
              success: true,
              isNewUser,
              userInfo: res.data.data
            });
          } else {
            reject(new Error(res.data.msg || '登录失败'));
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 显示手机号授权对话框
   */
  static showPhoneAuthDialog(code) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '授权提示',
        content: '为了更好的服务体验，需要获取您的手机号',
        confirmText: '授权',
        cancelText: '跳过',
        success: (res) => {
          if (res.confirm) {
            // 用户同意授权
            this.getPhoneNumberAuth(code)
              .then(resolve)
              .catch(reject);
          } else {
            // 用户跳过授权，使用简单登录
            this.simpleLogin(code)
              .then(resolve)
              .catch(reject);
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 获取手机号授权
   */
  static getPhoneNumberAuth(code) {
    return new Promise((resolve, reject) => {
      // 注意：这个方法需要在用户点击按钮时调用
      // 实际使用时应该通过 button 组件的 open-type="getPhoneNumber" 来触发
      console.log('请通过按钮组件获取手机号授权');
      
      // 这里提供一个模拟的实现，实际应该在页面中使用按钮组件
      reject(new Error('请使用按钮组件获取手机号授权'));
    });
  }

  /**
   * 处理手机号授权回调
   */
  static handlePhoneAuth(code, phoneAuthResult) {
    return new Promise((resolve, reject) => {
      if (phoneAuthResult.errMsg === 'getPhoneNumber:ok') {
        // 用户同意授权
        wx.request({
          url: `${this.getBaseUrl()}/miniapp/user/weixinLogin`,
          method: 'POST',
          data: {
            code,
            encryptedData: phoneAuthResult.encryptedData,
            iv: phoneAuthResult.iv
          },
          success: (res) => {
            if (res.data.code === 200) {
              const { token, isNewUser } = res.data.data;
              
              // 保存token
              wx.setStorageSync('token', token);
              wx.setStorageSync('userInfo', res.data.data);
              
              resolve({
                success: true,
                isNewUser,
                userInfo: res.data.data,
                hasPhone: true
              });
            } else {
              reject(new Error(res.data.msg || '登录失败'));
            }
          },
          fail: reject
        });
      } else {
        // 用户拒绝授权，使用简单登录
        this.simpleLogin(code)
          .then(resolve)
          .catch(reject);
      }
    });
  }

  /**
   * 获取API基础URL
   */
  static getBaseUrl() {
    // 根据环境返回不同的API地址
    return 'https://your-api-domain.com'; // 替换为实际的API地址
  }

  /**
   * 检查登录状态
   */
  static checkLoginStatus() {
    const token = wx.getStorageSync('token');
    return !!token;
  }

  /**
   * 退出登录
   */
  static logout() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    // 可以添加其他清理逻辑
  }
}

module.exports = LoginUtils;
```

### 2. 页面使用示例 (login.js)

```javascript
const LoginUtils = require('../../utils/login-utils.js');

Page({
  data: {
    loading: false
  },

  onLoad() {
    // 页面加载时自动尝试登录
    this.autoLogin();
  },

  /**
   * 自动登录
   */
  autoLogin() {
    if (LoginUtils.checkLoginStatus()) {
      // 已经登录，跳转到主页
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 执行智能登录
    this.smartLogin();
  },

  /**
   * 智能登录
   */
  smartLogin() {
    this.setData({ loading: true });
    
    LoginUtils.smartLogin()
      .then((result) => {
        console.log('登录成功:', result);
        
        if (result.isNewUser) {
          wx.showToast({
            title: '欢迎新用户！',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '欢迎回来！',
            icon: 'success'
          });
        }

        // 跳转到主页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      })
      .catch((error) => {
        console.error('登录失败:', error);
        wx.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  /**
   * 手机号授权按钮点击
   */
  onGetPhoneNumber(e) {
    const phoneAuthResult = e.detail;
    
    // 获取当前的code
    wx.login({
      success: (res) => {
        if (res.code) {
          LoginUtils.handlePhoneAuth(res.code, phoneAuthResult)
            .then((result) => {
              console.log('手机号授权登录成功:', result);
              wx.showToast({
                title: '授权成功！',
                icon: 'success'
              });
              
              // 跳转到主页
              setTimeout(() => {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }, 1500);
            })
            .catch((error) => {
              console.error('手机号授权登录失败:', error);
              wx.showToast({
                title: error.message || '授权失败',
                icon: 'none'
              });
            });
        }
      }
    });
  }
});
```

## 使用说明

1. **自动登录**: 页面加载时自动检查登录状态并执行智能登录
2. **智能判断**: 根据用户状态自动选择登录流程
3. **手机号授权**: 仅在需要时才请求手机号授权
4. **错误处理**: 完善的错误处理和用户提示
5. **状态管理**: 自动保存和管理登录状态

## 注意事项

1. 替换 `getBaseUrl()` 中的API地址为实际地址
2. 手机号授权需要使用 `<button open-type="getPhoneNumber">` 组件
3. 建议在应用启动时调用智能登录流程
4. 可根据业务需求调整登录流程和用户提示
