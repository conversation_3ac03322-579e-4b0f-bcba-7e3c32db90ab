package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniActivity;
import com.ruoyi.miniapp.mapper.MiniActivityMapper;
import com.ruoyi.miniapp.service.IMiniActivityService;

/**
 * 精彩活动Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniActivityServiceImpl implements IMiniActivityService 
{
    @Autowired
    private MiniActivityMapper miniActivityMapper;

    /**
     * 查询精彩活动
     * 
     * @param activityId 精彩活动主键
     * @return 精彩活动
     */
    @Override
    public MiniActivity selectMiniActivityByActivityId(Long activityId)
    {
        return miniActivityMapper.selectMiniActivityByActivityId(activityId);
    }

    /**
     * 查询精彩活动列表
     * 
     * @param miniActivity 精彩活动
     * @return 精彩活动
     */
    @Override
    public List<MiniActivity> selectMiniActivityList(MiniActivity miniActivity)
    {
        return miniActivityMapper.selectMiniActivityList(miniActivity);
    }

    /**
     * 新增精彩活动
     * 
     * @param miniActivity 精彩活动
     * @return 结果
     */
    @Override
    public int insertMiniActivity(MiniActivity miniActivity)
    {
        // 只有当创建时间为空时才设置当前时间，避免覆盖微信文章的时间
        if (miniActivity.getCreateTime() == null) {
            miniActivity.setCreateTime(DateUtils.getNowDate());
        }
        return miniActivityMapper.insertMiniActivity(miniActivity);
    }

    /**
     * 修改精彩活动
     * 
     * @param miniActivity 精彩活动
     * @return 结果
     */
    @Override
    public int updateMiniActivity(MiniActivity miniActivity)
    {
        miniActivity.setUpdateTime(DateUtils.getNowDate());
        return miniActivityMapper.updateMiniActivity(miniActivity);
    }

    /**
     * 批量删除精彩活动
     * 
     * @param activityIds 需要删除的精彩活动主键
     * @return 结果
     */
    @Override
    public int deleteMiniActivityByActivityIds(Long[] activityIds)
    {
        return miniActivityMapper.deleteMiniActivityByActivityIds(activityIds);
    }

    /**
     * 删除精彩活动信息
     * 
     * @param activityId 精彩活动主键
     * @return 结果
     */
    @Override
    public int deleteMiniActivityByActivityId(Long activityId)
    {
        return miniActivityMapper.deleteMiniActivityByActivityId(activityId);
    }

    /**
     * 查询推荐的精彩活动列表（小程序端调用）
     * 
     * @return 精彩活动集合
     */
    @Override
    public List<MiniActivity> selectRecommendedMiniActivityList()
    {
        return miniActivityMapper.selectRecommendedMiniActivityList();
    }

    /**
     * 查询启用的精彩活动列表（小程序端调用）
     * 
     * @return 精彩活动集合
     */
    @Override
    public List<MiniActivity> selectEnabledMiniActivityList()
    {
        return miniActivityMapper.selectEnabledMiniActivityList();
    }
} 