<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniBannerMapper">
    
    <resultMap type="MiniBanner" id="MiniBannerResult">
        <result property="bannerId"    column="banner_id"    />
        <result property="title"    column="title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniBannerVo">
        select banner_id, title, image_url, link_url, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_banner
    </sql>

    <select id="selectMiniBannerList" parameterType="MiniBanner" resultMap="MiniBannerResult">
        <include refid="selectMiniBannerVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniBannerByBannerId" parameterType="Long" resultMap="MiniBannerResult">
        <include refid="selectMiniBannerVo"/>
        where banner_id = #{bannerId}
    </select>

    <select id="selectEnabledMiniBannerList" resultMap="MiniBannerResult">
        <include refid="selectMiniBannerVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniBanner" parameterType="MiniBanner" useGeneratedKeys="true" keyProperty="bannerId">
        insert into mini_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="linkUrl != null and linkUrl != ''">link_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="linkUrl != null and linkUrl != ''">#{linkUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniBanner" parameterType="MiniBanner">
        update mini_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="linkUrl != null and linkUrl != ''">link_url = #{linkUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where banner_id = #{bannerId}
    </update>

    <delete id="deleteMiniBannerByBannerId" parameterType="Long">
        delete from mini_banner where banner_id = #{bannerId}
    </delete>

    <delete id="deleteMiniBannerByBannerIds" parameterType="String">
        delete from mini_banner where banner_id in 
        <foreach item="bannerId" collection="array" open="(" separator="," close=")">
            #{bannerId}
        </foreach>
    </delete>

</mapper> 