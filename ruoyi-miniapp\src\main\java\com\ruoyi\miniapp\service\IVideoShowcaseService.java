package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.VideoShowcase;

/**
 * 视频展播Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IVideoShowcaseService 
{
    /**
     * 查询视频展播
     *
     * @param id 视频展播主键
     * @return 视频展播
     */
    public VideoShowcase selectVideoShowcaseById(Long id);

    /**
     * 查询视频展播列表
     *
     * @param videoShowcase 视频展播
     * @return 视频展播集合
     */
    public List<VideoShowcase> selectVideoShowcaseList(VideoShowcase videoShowcase);

    /**
     * 新增视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    public int insertVideoShowcase(VideoShowcase videoShowcase);

    /**
     * 修改视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    public int updateVideoShowcase(VideoShowcase videoShowcase);

    /**
     * 批量删除视频展播
     *
     * @param ids 需要删除的视频展播主键集合
     * @return 结果
     */
    public int deleteVideoShowcaseByIds(Long[] ids);

    /**
     * 删除视频展播信息
     *
     * @param id 视频展播主键
     * @return 结果
     */
    public int deleteVideoShowcaseById(Long id);
} 