package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniBanner;

/**
 * 轮播图Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniBannerMapper 
{
    /**
     * 查询轮播图
     * 
     * @param bannerId 轮播图主键
     * @return 轮播图
     */
    public MiniBanner selectMiniBannerByBannerId(Long bannerId);

    /**
     * 查询轮播图列表
     * 
     * @param miniBanner 轮播图
     * @return 轮播图集合
     */
    public List<MiniBanner> selectMiniBannerList(MiniBanner miniBanner);

    /**
     * 新增轮播图
     * 
     * @param miniBanner 轮播图
     * @return 结果
     */
    public int insertMiniBanner(MiniBanner miniBanner);

    /**
     * 修改轮播图
     * 
     * @param miniBanner 轮播图
     * @return 结果
     */
    public int updateMiniBanner(MiniBanner miniBanner);

    /**
     * 删除轮播图
     * 
     * @param bannerId 轮播图主键
     * @return 结果
     */
    public int deleteMiniBannerByBannerId(Long bannerId);

    /**
     * 批量删除轮播图
     * 
     * @param bannerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniBannerByBannerIds(Long[] bannerIds);

    /**
     * 查询启用的轮播图列表（小程序端调用）
     * 
     * @return 轮播图集合
     */
    public List<MiniBanner> selectEnabledMiniBannerList();
} 