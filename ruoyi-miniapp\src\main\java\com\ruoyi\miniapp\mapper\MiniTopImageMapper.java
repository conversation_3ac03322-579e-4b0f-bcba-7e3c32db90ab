package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniTopImage;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

/**
 * top图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface MiniTopImageMapper 
{
    /**
     * 查询top图片
     * 
     * @param topImageId top图片主键
     * @return top图片
     */
    public MiniTopImage selectMiniTopImageByTopImageId(Long topImageId);

    /**
     * 查询top图片列表
     * 
     * @param miniTopImage top图片
     * @return top图片集合
     */
    public List<MiniTopImage> selectMiniTopImageList(MiniTopImage miniTopImage);

    /**
     * 新增top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    public int insertMiniTopImage(MiniTopImage miniTopImage);

    /**
     * 修改top图片
     * 
     * @param miniTopImage top图片
     * @return 结果
     */
    public int updateMiniTopImage(MiniTopImage miniTopImage);

    /**
     * 删除top图片
     * 
     * @param topImageId top图片主键
     * @return 结果
     */
    public int deleteMiniTopImageByTopImageId(Long topImageId);

    /**
     * 批量删除top图片
     * 
     * @param topImageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniTopImageByTopImageIds(Long[] topImageIds);

    /**
     * 查询启用的top图片列表
     * 
     * @return top图片集合
     */
    public List<MiniTopImage> selectEnabledMiniTopImageList();

    /**
     * 根据页面标识查询启用的top图片列表
     * 
     * @param pageCode 页面标识
     * @return top图片集合
     */
    public List<MiniTopImage> selectEnabledMiniTopImageListByPage(@Param("pageCode") String pageCode);
} 