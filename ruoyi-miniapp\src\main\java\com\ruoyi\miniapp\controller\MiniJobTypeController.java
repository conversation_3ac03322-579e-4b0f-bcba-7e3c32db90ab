package com.ruoyi.miniapp.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.miniapp.domain.MiniJobType;
import com.ruoyi.miniapp.service.IMiniJobTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 职位类型Controller
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Api(tags = "职位类型管理")
@RestController
@RequestMapping("/miniapp/jobType")
public class MiniJobTypeController extends BaseController
{
    @Autowired
    private IMiniJobTypeService miniJobTypeService;

    /**
     * 查询职位类型列表
     */
    @ApiOperation("查询职位类型列表")
    @PostMapping("/list")
    public AjaxResult list(@ApiParam("查询条件") @RequestBody MiniJobType miniJobType)
    {
        List<MiniJobType> list = miniJobTypeService.selectMiniJobTypeList(miniJobType);
        return AjaxResult.success(list);
    }

    /**
     * 获取职位类型详细信息
     */
    @ApiOperation("获取职位类型详细信息")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("职位类型ID") @RequestBody Long jobTypeId)
    {
        return AjaxResult.success(miniJobTypeService.selectMiniJobTypeByJobTypeId(jobTypeId));
    }

    /**
     * 新增职位类型
     */
    @ApiOperation("新增职位类型")
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("职位类型信息") @RequestBody MiniJobType miniJobType)
    {
        return toAjax(miniJobTypeService.insertMiniJobType(miniJobType));
    }

    /**
     * 修改职位类型
     */
    @ApiOperation("修改职位类型")
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("职位类型信息") @RequestBody MiniJobType miniJobType)
    {
        return toAjax(miniJobTypeService.updateMiniJobType(miniJobType));
    }

    /**
     * 删除职位类型
     */
    @ApiOperation("删除职位类型")
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("职位类型ID数组") @RequestBody Long[] jobTypeIds)
    {
        return toAjax(miniJobTypeService.deleteMiniJobTypeByJobTypeIds(jobTypeIds));
    }

    /**
     * 获取启用的职位类型列表（小程序端调用）
     */
    @ApiOperation("获取启用的职位类型列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniJobType> list = miniJobTypeService.selectEnabledMiniJobTypeList();
        return AjaxResult.success(list);
    }
} 