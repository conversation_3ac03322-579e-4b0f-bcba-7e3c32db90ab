package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.CompetitionConfig;

/**
 * 大赛介绍Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ICompetitionConfigService 
{
        /**
     * 查询大赛介绍
     *
     * @param id 大赛介绍主键
     * @return 大赛介绍
     */
    public CompetitionConfig selectCompetitionConfigById(Long id);

        /**
     * 查询大赛介绍列表
     *
     * @param competitionConfig 大赛介绍
     * @return 大赛介绍集合
     */
    public List<CompetitionConfig> selectCompetitionConfigList(CompetitionConfig competitionConfig);

        /**
     * 新增大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    public int insertCompetitionConfig(CompetitionConfig competitionConfig);

        /**
     * 修改大赛介绍
     *
     * @param competitionConfig 大赛介绍
     * @return 结果
     */
    public int updateCompetitionConfig(CompetitionConfig competitionConfig);

        /**
     * 批量删除大赛介绍
     *
     * @param ids 需要删除的大赛介绍主键集合
     * @return 结果
     */
    public int deleteCompetitionConfigByIds(Long[] ids);

        /**
     * 删除大赛介绍信息
     *
     * @param id 大赛介绍主键
     * @return 结果
     */
    public int deleteCompetitionConfigById(Long id);

        /**
     * 获取当前启用的大赛介绍
     *
     * @return 大赛介绍
     */
    public CompetitionConfig selectEnabledConfig();
}