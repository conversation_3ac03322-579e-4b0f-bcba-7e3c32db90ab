package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.MiniEnterpriseMapper;
import com.ruoyi.miniapp.domain.MiniEnterprise;
import com.ruoyi.miniapp.mapper.MiniIndustryTreeMapper;
import com.ruoyi.miniapp.domain.MiniIndustryTree;

import com.ruoyi.miniapp.service.IMiniEnterpriseService;

import java.util.ArrayList;

/**
 * 企业管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MiniEnterpriseServiceImpl implements IMiniEnterpriseService 
{
    @Autowired
    private MiniEnterpriseMapper miniEnterpriseMapper;

    @Autowired
    private MiniIndustryTreeMapper miniIndustryTreeMapper;

    /**
     * 查询企业管理
     * 
     * @param enterpriseId 企业管理主键
     * @return 企业管理
     */
    @Override
    public MiniEnterprise selectMiniEnterpriseByEnterpriseId(Long enterpriseId)
    {
        return miniEnterpriseMapper.selectMiniEnterpriseByEnterpriseId(enterpriseId);
    }

    /**
     * 查询企业管理列表
     * 
     * @param miniEnterprise 企业管理
     * @return 企业管理
     */
    @Override
    public List<MiniEnterprise> selectMiniEnterpriseList(MiniEnterprise miniEnterprise)
    {
        return miniEnterpriseMapper.selectMiniEnterpriseList(miniEnterprise);
    }

    /**
     * 新增企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    @Override
    public int insertMiniEnterprise(MiniEnterprise miniEnterprise)
    {
        miniEnterprise.setCreateTime(DateUtils.getNowDate());
        return miniEnterpriseMapper.insertMiniEnterprise(miniEnterprise);
    }

    /**
     * 修改企业管理
     * 
     * @param miniEnterprise 企业管理
     * @return 结果
     */
    @Override
    public int updateMiniEnterprise(MiniEnterprise miniEnterprise)
    {
        miniEnterprise.setUpdateTime(DateUtils.getNowDate());
        return miniEnterpriseMapper.updateMiniEnterprise(miniEnterprise);
    }

    /**
     * 批量删除企业管理
     * 
     * @param enterpriseIds 需要删除的企业管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMiniEnterpriseByEnterpriseIds(Long[] enterpriseIds)
    {
        // 先删除企业产业关联
        miniEnterpriseMapper.deleteMiniEnterpriseIndustryByEnterpriseIds(enterpriseIds);
        // 再删除企业信息
        return miniEnterpriseMapper.deleteMiniEnterpriseByEnterpriseIds(enterpriseIds);
    }

    /**
     * 删除企业管理信息
     * 
     * @param enterpriseId 企业管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMiniEnterpriseByEnterpriseId(Long enterpriseId)
    {
        // 先删除企业产业关联
        miniEnterpriseMapper.deleteMiniEnterpriseIndustryByEnterpriseId(enterpriseId);
        // 再删除企业信息
        return miniEnterpriseMapper.deleteMiniEnterpriseByEnterpriseId(enterpriseId);
    }

    /**
     * 查询企业产业关联信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业产业关联列表
     */
    @Override
    public List<MiniEnterprise.MiniEnterpriseIndustry> selectEnterpriseIndustryByEnterpriseId(Long enterpriseId)
    {
        List<MiniEnterprise.MiniEnterpriseIndustry> list = miniEnterpriseMapper.selectEnterpriseIndustryByEnterpriseId(enterpriseId);
        
        // 遍历列表，根据选中节点的类型构建正确的层级结构
        for (MiniEnterprise.MiniEnterpriseIndustry item : list) {
            Long selectedNodeId = item.getIndustryTreeId();
            if (selectedNodeId != null) {
                // 查询用户选中的节点
                MiniIndustryTree selectedNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(selectedNodeId);
                if (selectedNode != null) {
                    // 构建完整的层级结构
                    MiniIndustryTree industryTree = buildIndustryHierarchy(selectedNode);
                    item.setIndustryTree(industryTree);
                }
            }
        }
        
        return list;
    }
    
    /**
     * 根据选中节点构建完整的产业层级结构
     * 
     * @param selectedNode 用户选中的节点
     * @return 完整的产业层级结构
     */
    private MiniIndustryTree buildIndustryHierarchy(MiniIndustryTree selectedNode) {
        MiniIndustryTree typeNode = null;
        MiniIndustryTree positionNode = null;
        MiniIndustryTree segmentNode = null;
        
        // 根据选中节点的类型，向上查找完整的层级路径
        if ("type".equals(selectedNode.getNodeType())) {
            // 用户选择了产业类型
            typeNode = selectedNode;
        } else if ("position".equals(selectedNode.getNodeType())) {
            // 用户选择了产业位置
            positionNode = selectedNode;
            // 查找父级产业类型
            if (selectedNode.getParentId() != null) {
                typeNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(selectedNode.getParentId());
            }
        } else if ("segment".equals(selectedNode.getNodeType())) {
            // 用户选择了产业细分
            segmentNode = selectedNode;
            // 查找父级产业位置
            if (selectedNode.getParentId() != null) {
                positionNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(selectedNode.getParentId());
                // 查找祖父级产业类型
                if (positionNode != null && positionNode.getParentId() != null) {
                    typeNode = miniIndustryTreeMapper.selectMiniIndustryTreeById(positionNode.getParentId());
                }
            }
        }
        
        // 构建层级结构，从顶层开始
        MiniIndustryTree result = new MiniIndustryTree();
        
        if (typeNode != null) {
            // 设置产业类型信息
            result.setId(typeNode.getId());
            result.setNodeName(typeNode.getNodeName());
            result.setNodeType(typeNode.getNodeType());
            result.setNodeCode(typeNode.getNodeCode());
            result.setNodeDescription(typeNode.getNodeDescription());
            result.setNodeLevel(typeNode.getNodeLevel());
            result.setParentId(typeNode.getParentId());
            result.setNodePath(typeNode.getNodePath());
            result.setStatus(typeNode.getStatus());
            result.setSortOrder(typeNode.getSortOrder());
            result.setStreamType(typeNode.getStreamType());
            
            List<MiniIndustryTree> children = new ArrayList<>();
            
            if (positionNode != null) {
                // 添加产业位置信息
                MiniIndustryTree positionChild = new MiniIndustryTree();
                positionChild.setId(positionNode.getId());
                positionChild.setNodeName(positionNode.getNodeName());
                positionChild.setNodeType(positionNode.getNodeType());
                positionChild.setNodeCode(positionNode.getNodeCode());
                positionChild.setNodeDescription(positionNode.getNodeDescription());
                positionChild.setNodeLevel(positionNode.getNodeLevel());
                positionChild.setParentId(positionNode.getParentId());
                positionChild.setNodePath(positionNode.getNodePath());
                positionChild.setStatus(positionNode.getStatus());
                positionChild.setSortOrder(positionNode.getSortOrder());
                positionChild.setStreamType(positionNode.getStreamType());
                
                List<MiniIndustryTree> positionChildren = new ArrayList<>();
                
                if (segmentNode != null) {
                    // 添加产业细分信息
                    MiniIndustryTree segmentChild = new MiniIndustryTree();
                    segmentChild.setId(segmentNode.getId());
                    segmentChild.setNodeName(segmentNode.getNodeName());
                    segmentChild.setNodeType(segmentNode.getNodeType());
                    segmentChild.setNodeCode(segmentNode.getNodeCode());
                    segmentChild.setNodeDescription(segmentNode.getNodeDescription());
                    segmentChild.setNodeLevel(segmentNode.getNodeLevel());
                    segmentChild.setParentId(segmentNode.getParentId());
                    segmentChild.setNodePath(segmentNode.getNodePath());
                    segmentChild.setStatus(segmentNode.getStatus());
                    segmentChild.setSortOrder(segmentNode.getSortOrder());
                    segmentChild.setStreamType(segmentNode.getStreamType());
                    
                    positionChildren.add(segmentChild);
                }
                
                positionChild.setChildren(positionChildren);
                children.add(positionChild);
            }
            
            result.setChildren(children);
        } else if (positionNode != null) {
            // 如果没有类型节点，直接以位置节点为根
            result = positionNode;
            if (segmentNode != null) {
                List<MiniIndustryTree> children = new ArrayList<>();
                children.add(segmentNode);
                result.setChildren(children);
            }
        } else if (segmentNode != null) {
            // 如果只有细分节点，直接返回
            result = segmentNode;
        }
        
        return result;
    }

    /**
     * 更新企业产业关联
     * 
     * @param miniEnterprise 企业信息（包含产业关联列表）
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEnterpriseIndustry(MiniEnterprise miniEnterprise)
    {
        Long enterpriseId = miniEnterprise.getEnterpriseId();
        // 删除原有的产业关联
        miniEnterpriseMapper.deleteMiniEnterpriseIndustryByEnterpriseId(enterpriseId);
        
        // 插入新的产业关联
        List<MiniEnterprise.MiniEnterpriseIndustry> industryList = miniEnterprise.getIndustryList();
        if (industryList != null && !industryList.isEmpty()) {
            for (MiniEnterprise.MiniEnterpriseIndustry industry : industryList) {
                industry.setEnterpriseId(enterpriseId);
                industry.setCreateTime(DateUtils.getNowDate());
                industry.setUpdateTime(DateUtils.getNowDate());
            }
            return miniEnterpriseMapper.batchInsertEnterpriseIndustry(industryList);
        }
        return 1;
    }

    /**
     * 查询企业详细信息（包含产业信息）
     * 
     * @param enterpriseId 企业ID
     * @return 企业详细信息
     */
    @Override
    public MiniEnterprise selectEnterpriseDetailByEnterpriseId(Long enterpriseId)
    {
        MiniEnterprise enterprise = miniEnterpriseMapper.selectMiniEnterpriseByEnterpriseId(enterpriseId);
        if (enterprise != null) {
            List<MiniEnterprise.MiniEnterpriseIndustry> industryList = 
                miniEnterpriseMapper.selectEnterpriseIndustryByEnterpriseId(enterpriseId);
            enterprise.setIndustryList(industryList);
        }
        return enterprise;
    }

    // VO类定义
    public static class EnterpriseIndustryVO {
        private Long id;
        private Long enterpriseId;
        private Long industryTreeId;
        private String industryTreeName;
        private Long positionId;
        private String positionName;
        private Long segmentId;
        private String segmentName;
        private java.util.Date createTime;
        private java.util.Date updateTime;
        // getter/setter 省略，可自动生成
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public Long getEnterpriseId() { return enterpriseId; }
        public void setEnterpriseId(Long enterpriseId) { this.enterpriseId = enterpriseId; }
        public Long getIndustryTreeId() { return industryTreeId; }
        public void setIndustryTreeId(Long industryTreeId) { this.industryTreeId = industryTreeId; }
        public String getIndustryTreeName() { return industryTreeName; }
        public void setIndustryTreeName(String industryTreeName) { this.industryTreeName = industryTreeName; }
        public Long getPositionId() { return positionId; }
        public void setPositionId(Long positionId) { this.positionId = positionId; }
        public String getPositionName() { return positionName; }
        public void setPositionName(String positionName) { this.positionName = positionName; }
        public Long getSegmentId() { return segmentId; }
        public void setSegmentId(Long segmentId) { this.segmentId = segmentId; }
        public String getSegmentName() { return segmentName; }
        public void setSegmentName(String segmentName) { this.segmentName = segmentName; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
        public java.util.Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.util.Date updateTime) { this.updateTime = updateTime; }
    }

} 