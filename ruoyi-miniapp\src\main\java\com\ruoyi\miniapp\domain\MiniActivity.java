package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 精彩活动对象 mini_activity
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long activityId;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String title;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String description;

    /** 封面图片 */
    @Excel(name = "封面图片")
    private String coverImage;

    /** 公众号文章链接 */
    @Excel(name = "公众号文章链接")
    private String articleUrl;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 微信文章ID */
    @Excel(name = "微信文章ID")
    private String wechatArticleId;

    /** 公众号来源 */
    @Excel(name = "公众号来源")
    private String wechatSource;

    public void setActivityId(Long activityId) 
    {
        this.activityId = activityId;
    }

    public Long getActivityId() 
    {
        return activityId;
    }
    
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setCoverImage(String coverImage) 
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage() 
    {
        return coverImage;
    }
    
    public void setArticleUrl(String articleUrl) 
    {
        this.articleUrl = articleUrl;
    }

    public String getArticleUrl() 
    {
        return articleUrl;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setWechatArticleId(String wechatArticleId)
    {
        this.wechatArticleId = wechatArticleId;
    }

    public String getWechatArticleId()
    {
        return wechatArticleId;
    }

    public void setWechatSource(String wechatSource)
    {
        this.wechatSource = wechatSource;
    }

    public String getWechatSource()
    {
        return wechatSource;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("activityId", getActivityId())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("coverImage", getCoverImage())
            .append("articleUrl", getArticleUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("wechatArticleId", getWechatArticleId())
            .append("wechatSource", getWechatSource())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 