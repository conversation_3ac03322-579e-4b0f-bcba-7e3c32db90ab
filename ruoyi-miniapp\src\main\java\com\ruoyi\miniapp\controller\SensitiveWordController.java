package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.SensitiveWord;
import com.ruoyi.miniapp.service.ISensitiveWordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 敏感词Controller
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Api(tags = "敏感词管理")
@RestController
@RequestMapping("/miniapp/sensitiveWord")
public class SensitiveWordController extends BaseController
{
    @Autowired
    private ISensitiveWordService sensitiveWordService;

    /**
     * 查询敏感词列表
     */
    @ApiOperation("查询敏感词列表")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") SensitiveWord sensitiveWord)
    {
        startPage();
        List<SensitiveWord> list = sensitiveWordService.selectSensitiveWordList(sensitiveWord);
        return getDataTable(list);
    }

    /**
     * 导出敏感词列表
     */
    @ApiOperation("导出敏感词列表")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:export')")
    @Log(title = "敏感词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") SensitiveWord sensitiveWord)
    {
        List<SensitiveWord> list = sensitiveWordService.selectSensitiveWordList(sensitiveWord);
        ExcelUtil<SensitiveWord> util = new ExcelUtil<SensitiveWord>(SensitiveWord.class);
        util.exportExcel(response, list, "敏感词数据");
    }

    /**
     * 获取敏感词详细信息
     */
    @ApiOperation("获取敏感词详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:query')")
    @GetMapping(value = "/{wordId}")
    public AjaxResult getInfo(@ApiParam("敏感词ID") @PathVariable("wordId") Long wordId)
    {
        return AjaxResult.success(sensitiveWordService.selectSensitiveWordByWordId(wordId));
    }

    /**
     * 新增敏感词
     */
    @ApiOperation("新增敏感词")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:add')")
    @Log(title = "敏感词", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("敏感词信息") @RequestBody SensitiveWord sensitiveWord)
    {
        // 检查敏感词内容是否唯一
        if (!sensitiveWordService.checkWordContentUnique(sensitiveWord.getWordContent(), null))
        {
            return AjaxResult.error("新增敏感词失败，敏感词内容已存在");
        }
        sensitiveWord.setCreateBy(getUsername());
        return toAjax(sensitiveWordService.insertSensitiveWord(sensitiveWord));
    }

    /**
     * 修改敏感词
     */
    @ApiOperation("修改敏感词")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:edit')")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("敏感词信息") @RequestBody SensitiveWord sensitiveWord)
    {
        // 检查敏感词内容是否唯一
        if (!sensitiveWordService.checkWordContentUnique(sensitiveWord.getWordContent(), sensitiveWord.getWordId()))
        {
            return AjaxResult.error("修改敏感词失败，敏感词内容已存在");
        }
        sensitiveWord.setUpdateBy(getUsername());
        return toAjax(sensitiveWordService.updateSensitiveWord(sensitiveWord));
    }

    /**
     * 删除敏感词
     */
    @ApiOperation("删除敏感词")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:remove')")
    @Log(title = "敏感词", businessType = BusinessType.DELETE)
	@DeleteMapping("/{wordIds}")
    public AjaxResult remove(@ApiParam("敏感词ID数组") @PathVariable Long[] wordIds)
    {
        return toAjax(sensitiveWordService.deleteSensitiveWordByWordIds(wordIds));
    }

    /**
     * 检测文本是否包含敏感词
     */
    @ApiOperation("检测文本是否包含敏感词")
    @PostMapping("/check")
    public AjaxResult checkSensitiveWord(@ApiParam("待检测文本") @RequestParam("text") String text)
    {
        if (StringUtils.isEmpty(text)) {
            return AjaxResult.success("文本为空", false);
        }

        boolean contains = sensitiveWordService.containsSensitiveWord(text);
        List<String> hitWords = null;
        if (contains) {
            hitWords = sensitiveWordService.findSensitiveWords(text);
            // 更新命中次数
            sensitiveWordService.updateHitCount(hitWords);
        }

        return AjaxResult.success()
                .put("contains", contains)
                .put("hitWords", hitWords);
    }

    /**
     * 过滤文本中的敏感词
     */
    @ApiOperation("过滤文本中的敏感词")
    @PostMapping("/filter")
    public AjaxResult filterSensitiveWord(@ApiParam("待过滤文本") @RequestParam("text") String text,
                                        @ApiParam("替换字符") @RequestParam(value = "replacement", required = false, defaultValue = "*") String replacement)
    {
        if (StringUtils.isEmpty(text)) {
            return AjaxResult.success("文本为空", text);
        }

        String filteredText;
        if (replacement.length() == 1) {
            filteredText = sensitiveWordService.replaceSensitiveWords(text, replacement.charAt(0));
        } else {
            filteredText = sensitiveWordService.replaceSensitiveWords(text);
        }

        List<String> hitWords = sensitiveWordService.findSensitiveWords(text);
        if (hitWords != null && !hitWords.isEmpty()) {
            // 更新命中次数
            sensitiveWordService.updateHitCount(hitWords);
        }

        return AjaxResult.success()
                .put("originalText", text)
                .put("filteredText", filteredText)
                .put("hitWords", hitWords);
    }

    /**
     * 刷新敏感词缓存
     */
    @ApiOperation("刷新敏感词缓存")
    @PreAuthorize("@ss.hasPermi('miniapp:sensitiveWord:refresh')")
    @Log(title = "敏感词", businessType = BusinessType.OTHER)
    @PostMapping("/refresh")
    public AjaxResult refreshCache()
    {
        sensitiveWordService.refreshSensitiveWordCache();
        return AjaxResult.success("敏感词缓存刷新成功");
    }
}
