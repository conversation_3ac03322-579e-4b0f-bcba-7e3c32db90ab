package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniActivity;
import com.ruoyi.miniapp.domain.dto.WechatConfigDTO;
import com.ruoyi.miniapp.service.IMiniActivityService;
import com.ruoyi.miniapp.service.IWechatActivityService;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 精彩活动Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "精彩活动管理")
@RestController
@RequestMapping("/miniapp/activity")
public class MiniActivityController extends BaseController
{
    @Autowired
    private IMiniActivityService miniActivityService;

    @Autowired
    private IWechatActivityService wechatActivityService;

    /**
     * 查询精彩活动列表
     */
    @ApiOperation("查询精彩活动列表")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniActivity miniActivity)
    {
        startPage();
        List<MiniActivity> list = miniActivityService.selectMiniActivityList(miniActivity);
        return getDataTable(list);
    }

    /**
     * 导出精彩活动列表
     */
    @ApiOperation("导出精彩活动列表")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:export')")
    @Log(title = "精彩活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniActivity miniActivity)
    {
        List<MiniActivity> list = miniActivityService.selectMiniActivityList(miniActivity);
        ExcelUtil<MiniActivity> util = new ExcelUtil<MiniActivity>(MiniActivity.class);
        util.exportExcel(response, list, "精彩活动数据");
    }

    /**
     * 获取精彩活动详细信息
     */
    @ApiOperation("获取精彩活动详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("活动ID") @RequestBody Long activityId)
    {
        return AjaxResult.success(miniActivityService.selectMiniActivityByActivityId(activityId));
    }

    /**
     * 新增精彩活动
     */
    @ApiOperation("新增精彩活动")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:add')")
    @Log(title = "精彩活动", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("活动信息") @RequestBody MiniActivity miniActivity)
    {
        return toAjax(miniActivityService.insertMiniActivity(miniActivity));
    }

    /**
     * 修改精彩活动
     */
    @ApiOperation("修改精彩活动")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:edit')")
    @Log(title = "精彩活动", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("活动信息") @RequestBody MiniActivity miniActivity)
    {
        return toAjax(miniActivityService.updateMiniActivity(miniActivity));
    }

    /**
     * 删除精彩活动
     */
    @ApiOperation("删除精彩活动")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:remove')")
    @Log(title = "精彩活动", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("活动ID数组") @RequestBody Long[] activityIds)
    {
        return toAjax(miniActivityService.deleteMiniActivityByActivityIds(activityIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的精彩活动列表
     */
    @ApiOperation("获取启用的精彩活动列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniActivity> list = miniActivityService.selectEnabledMiniActivityList();
        return AjaxResult.success(list);
    }

    /**
     * 获取推荐的精彩活动列表
     */
    @ApiOperation("获取推荐的精彩活动列表")
    @PostMapping("/app/getRecommendedList")
    public AjaxResult getRecommendedList()
    {
        List<MiniActivity> list = miniActivityService.selectRecommendedMiniActivityList();
        return AjaxResult.success(list);
    }

    /**
     * 获取精彩活动详情
     */
    @ApiOperation("获取精彩活动详情")
    @PostMapping("/app/getDetail")
    public AjaxResult getDetail(@ApiParam("活动ID") @RequestBody Long activityId)
    {
        return AjaxResult.success(miniActivityService.selectMiniActivityByActivityId(activityId));
    }

    // ================================ 微信公众号配置接口 ================================

    /**
     * 获取微信公众号配置列表
     */
    @ApiOperation("获取微信公众号配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:list')")
    @PostMapping("/wechat/getConfigs")
    public AjaxResult getWechatConfigs()
    {
        return AjaxResult.success(wechatActivityService.getWechatConfigList());
    }

    /**
     * 保存微信公众号配置
     */
    @ApiOperation("保存微信公众号配置")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:edit')")
    @Log(title = "微信公众号配置", businessType = BusinessType.UPDATE)
    @PostMapping("/wechat/saveConfig")
    public AjaxResult saveWechatConfig(@ApiParam("配置信息") @RequestBody WechatConfigDTO config)
    {
        return wechatActivityService.saveWechatConfig(config);
    }

    /**
     * 删除微信公众号配置
     */
    @ApiOperation("删除微信公众号配置")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:remove')")
    @Log(title = "微信公众号配置", businessType = BusinessType.DELETE)
    @PostMapping("/wechat/deleteConfig")
    public AjaxResult deleteWechatConfig(@ApiParam("配置ID") @RequestParam String configId)
    {
        return wechatActivityService.deleteWechatConfig(configId);
    }

    /**
     * 测试微信公众号配置
     */
    @ApiOperation("测试微信公众号配置")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:list')")
    @PostMapping("/wechat/testConfig")
    public AjaxResult testWechatConfig(@ApiParam("配置信息") @RequestBody WechatConfigDTO config)
    {
        return wechatActivityService.testWechatConfig(config);
    }

    /**
     * 从微信公众号同步活动
     */
    @ApiOperation("从微信公众号同步活动")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:add')")
    @Log(title = "同步微信活动", businessType = BusinessType.INSERT)
    @PostMapping("/wechat/syncActivities")
    public AjaxResult syncActivitiesFromWechat()
    {
        return wechatActivityService.syncActivitiesFromWechat();
    }

    /**
     * 从指定微信公众号同步活动
     */
    @ApiOperation("从指定微信公众号同步活动")
    @PreAuthorize("@ss.hasPermi('miniapp:activity:add')")
    @Log(title = "同步微信活动", businessType = BusinessType.INSERT)
    @PostMapping("/wechat/syncFromSingle")
    public AjaxResult syncActivitiesFromSingleWechat(@ApiParam("配置ID") @RequestParam String configId)
    {
        return wechatActivityService.syncActivitiesFromSingleWechat(configId);
    }

    /**
     * 图片代理接口（解决微信图片防盗链问题）
     */
    @ApiOperation("图片代理")
    @GetMapping("/imageProxy")
    public void imageProxy(@ApiParam("图片URL") @RequestParam String url, HttpServletResponse response)
    {
        try {
            // 设置响应头
            response.setContentType("image/jpeg");
            response.setHeader("Cache-Control", "max-age=3600");

            // 创建HTTP请求
            URL imageUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) imageUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Referer", "https://mp.weixin.qq.com/");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);

            // 读取图片数据并写入响应
            try (InputStream inputStream = connection.getInputStream();
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

        } catch (Exception e) {
            logger.error("图片代理失败: " + url, e);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }
}