<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模块名称" prop="moduleName">
        <el-input
          v-model="queryParams.moduleName"
          placeholder="请输入模块名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="operationType">
        <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
          <el-option label="检测" value="检测" />
          <el-option label="过滤" value="过滤" />
          <el-option label="替换" value="替换" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:sensitiveWordLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:sensitiveWordLog:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['miniapp:sensitiveWordLog:clean']"
        >清理日志</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志ID" align="center" prop="logId" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="模块名称" align="center" prop="moduleName" />
      <el-table-column label="操作类型" align="center" prop="operationType">
        <template slot-scope="scope">
          <el-tag :type="getOperationTypeTag(scope.row.operationType)">
            {{ scope.row.operationType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="原始内容" align="center" prop="originalContent" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="过滤后内容" align="center" prop="filteredContent" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="命中数量" align="center" prop="hitCount" />
      <el-table-column label="客户端IP" align="center" prop="clientIp" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:sensitiveWordLog:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:sensitiveWordLog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名：">
              <span>{{ form.userName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模块名称：">
              <span>{{ form.moduleName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作类型：">
              <el-tag :type="getOperationTypeTag(form.operationType)">
                {{ form.operationType }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="命中数量：">
              <span>{{ form.hitCount }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="原始内容：">
          <el-input v-model="form.originalContent" type="textarea" :rows="3" readonly />
        </el-form-item>
        <el-form-item label="过滤后内容：" v-if="form.filteredContent">
          <el-input v-model="form.filteredContent" type="textarea" :rows="3" readonly />
        </el-form-item>
        <el-form-item label="命中敏感词：" v-if="hitWordsList.length > 0">
          <el-tag v-for="word in hitWordsList" :key="word" type="danger" style="margin-right: 5px;">
            {{ word }}
          </el-tag>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户端IP：">
              <span>{{ form.clientIp }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间：">
              <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用户代理：" v-if="form.userAgent">
          <el-input v-model="form.userAgent" type="textarea" :rows="2" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 清理日志对话框 -->
    <el-dialog title="清理日志" :visible.sync="cleanOpen" width="400px" append-to-body>
      <el-form ref="cleanForm" :model="cleanForm" label-width="100px">
        <el-form-item label="保留天数：" prop="days">
          <el-input-number v-model="cleanForm.days" :min="1" :max="365" controls-position="right" />
          <div style="color: #999; font-size: 12px; margin-top: 5px;">
            将删除 {{ cleanForm.days }} 天前的所有日志记录
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitClean">确 定</el-button>
        <el-button @click="cleanOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSensitiveWordLog, getSensitiveWordLog, delSensitiveWordLog, cleanSensitiveWordLog } from "@/api/miniapp/sensitiveWord";

export default {
  name: "SensitiveWordLog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 敏感词检测日志表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示清理对话框
      cleanOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        moduleName: null,
        operationType: null
      },
      // 表单参数
      form: {},
      // 清理表单参数
      cleanForm: {
        days: 30
      },
      // 命中敏感词列表
      hitWordsList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询敏感词检测日志列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.params["beginTime"] = this.dateRange[0];
        this.queryParams.params["endTime"] = this.dateRange[1];
      }
      listSensitiveWordLog(this.queryParams).then(response => {
        this.logList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取操作类型标签
    getOperationTypeTag(type) {
      const tagMap = {
        '检测': 'info',
        '过滤': 'warning',
        '替换': 'success'
      };
      return tagMap[type] || 'info';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId)
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      getSensitiveWordLog(row.logId).then(response => {
        this.form = response.data;
        this.hitWordsList = [];
        if (this.form.hitWords) {
          try {
            this.hitWordsList = JSON.parse(this.form.hitWords);
          } catch (e) {
            console.error('解析命中敏感词失败:', e);
          }
        }
        this.open = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row.logId || this.ids;
      this.$confirm('是否确认删除日志编号为"' + logIds + '"的数据项？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        return delSensitiveWordLog(logIds);
      }).then(() => {
        this.getList();
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/sensitiveWordLog/export', {
        ...this.queryParams
      }, `sensitiveWordLog_${new Date().getTime()}.xlsx`)
    },
    /** 清理日志按钮操作 */
    handleClean() {
      this.cleanOpen = true;
    },
    /** 提交清理 */
    submitClean() {
      this.$confirm(`确认清理 ${this.cleanForm.days} 天前的所有日志记录吗？`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return cleanSensitiveWordLog(this.cleanForm.days);
      }).then((response) => {
        this.cleanOpen = false;
        this.getList();
        this.$message({
          type: 'success',
          message: response.msg
        });
      }).catch(() => {});
    }
  }
};
</script>
