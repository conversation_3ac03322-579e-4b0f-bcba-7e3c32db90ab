<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.SensitiveWordCategoryMapper">
    
    <resultMap type="SensitiveWordCategory" id="SensitiveWordCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="description"    column="description"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSensitiveWordCategoryVo">
        select category_id, category_name, category_code, description, sort_order, status, create_by, create_time, update_by, update_time, remark from sensitive_word_category
    </sql>

    <select id="selectSensitiveWordCategoryList" parameterType="SensitiveWordCategory" resultMap="SensitiveWordCategoryResult">
        <include refid="selectSensitiveWordCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectSensitiveWordCategoryByCategoryId" parameterType="Long" resultMap="SensitiveWordCategoryResult">
        <include refid="selectSensitiveWordCategoryVo"/>
        where category_id = #{categoryId}
    </select>

    <select id="selectEnabledSensitiveWordCategoryList" resultMap="SensitiveWordCategoryResult">
        <include refid="selectSensitiveWordCategoryVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectSensitiveWordCategoryByCategoryCode" parameterType="String" resultMap="SensitiveWordCategoryResult">
        <include refid="selectSensitiveWordCategoryVo"/>
        where category_code = #{categoryCode}
    </select>

    <select id="checkCategoryCodeUnique" parameterType="map" resultType="int">
        select count(1) from sensitive_word_category
        where category_code = #{categoryCode}
        <if test="categoryId != null">
            and category_id != #{categoryId}
        </if>
    </select>
        
    <insert id="insertSensitiveWordCategory" parameterType="SensitiveWordCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into sensitive_word_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryCode != null and categoryCode != ''">category_code,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">#{categoryCode},</if>
            <if test="description != null">#{description},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSensitiveWordCategory" parameterType="SensitiveWordCategory">
        update sensitive_word_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">category_code = #{categoryCode},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteSensitiveWordCategoryByCategoryId" parameterType="Long">
        delete from sensitive_word_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteSensitiveWordCategoryByCategoryIds" parameterType="String">
        delete from sensitive_word_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

</mapper>
