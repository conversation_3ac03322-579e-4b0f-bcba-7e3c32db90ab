# 微信小程序手机号获取和解密指南

## 概述

本文档详细说明了如何在微信小程序中获取用户手机号并在后端进行解密的完整流程。

## 🔄 手机号获取流程

### 1. 前端获取手机号授权

微信小程序提供了两种方式获取手机号：

#### 方式一：button组件授权（推荐）
```javascript
// WXML
<button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
  获取手机号
</button>

// JS
Page({
  getPhoneNumber: function(e) {
    console.log('手机号授权结果:', e.detail);
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 获取到加密数据
      const encryptedData = e.detail.encryptedData;
      const iv = e.detail.iv;
      
      // 先获取登录凭证
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            this.decryptPhoneNumber(loginRes.code, encryptedData, iv);
          }
        }
      });
    } else {
      wx.showToast({
        title: '用户拒绝授权',
        icon: 'none'
      });
    }
  },

  // 发送到后端解密
  decryptPhoneNumber: function(code, encryptedData, iv) {
    wx.request({
      url: 'https://your-api-domain.com/miniapp/user/decryptPhoneNumber',
      method: 'POST',
      data: {
        code: code,
        encryptedData: encryptedData,
        iv: iv
      },
      success: (res) => {
        if (res.data.code === 200) {
          const phoneInfo = res.data.data;
          console.log('解密成功:', phoneInfo);
          console.log('手机号:', phoneInfo.purePhoneNumber);
          
          // 保存手机号或进行其他操作
          this.setData({
            phoneNumber: phoneInfo.purePhoneNumber
          });
          
          wx.showToast({
            title: '获取手机号成功',
            icon: 'success'
          });
        } else {
          console.error('解密失败:', res.data.msg);
          wx.showToast({
            title: '获取手机号失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('请求失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  }
});
```

#### 方式二：结合登录流程
```javascript
// 在登录时同时获取手机号
Page({
  loginWithPhone: function() {
    // 1. 先获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 2. 获取用户信息（可选）
          wx.getUserProfile({
            desc: '用于完善用户资料',
            success: (userRes) => {
              // 3. 获取手机号授权
              this.getPhoneNumberForLogin(loginRes.code, userRes.userInfo);
            },
            fail: () => {
              // 即使用户信息获取失败，也可以继续获取手机号
              this.getPhoneNumberForLogin(loginRes.code, null);
            }
          });
        }
      }
    });
  },

  getPhoneNumberForLogin: function(code, userInfo) {
    // 显示手机号授权按钮
    this.setData({
      showPhoneAuth: true,
      loginCode: code,
      userInfo: userInfo
    });
  },

  onPhoneAuth: function(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 发送完整登录请求
      wx.request({
        url: 'https://your-api-domain.com/miniapp/user/weixinLogin',
        method: 'POST',
        data: {
          code: this.data.loginCode,
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv,
          weixinNickname: this.data.userInfo?.nickName,
          weixinAvatar: this.data.userInfo?.avatarUrl
        },
        success: (res) => {
          if (res.data.code === 200) {
            // 登录成功，手机号已自动解密并保存
            wx.setStorageSync('token', res.data.data.token);
            wx.setStorageSync('userInfo', res.data.data);
            
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            // 跳转到主页
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      });
    }
  }
});
```

### 2. 后端接口说明

#### 接口1：专门的手机号解密接口
```
POST /miniapp/user/decryptPhoneNumber
```

**请求参数：**
```json
{
  "code": "wx_login_code_123456789",
  "encryptedData": "encrypted_phone_data",
  "iv": "initialization_vector"
}
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "解密成功",
  "data": {
    "phoneNumber": "+86-13800138001",
    "purePhoneNumber": "13800138001",
    "countryCode": "86"
  }
}
```

#### 接口2：登录时自动解密手机号
```
POST /miniapp/user/weixinLogin
```

**请求参数：**
```json
{
  "code": "wx_login_code_123456789",
  "encryptedData": "encrypted_phone_data",
  "iv": "initialization_vector",
  "weixinNickname": "用户昵称",
  "weixinAvatar": "头像URL"
}
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "jwt_token",
    "userId": 100,
    "userName": "mini_12345678_9012",
    "nickName": "用户昵称",
    "phonenumber": "13800138001",
    "isNewUser": true
  }
}
```

## 🔧 技术实现细节

### 1. 解密算法
- **算法**: AES-128-CBC
- **密钥**: 微信session_key（Base64解码后）
- **初始向量**: 前端传递的iv（Base64解码后）
- **填充**: PKCS5Padding

### 2. 数据格式
解密后的手机号数据格式：
```json
{
  "phoneNumber": "+86-13800138001",
  "purePhoneNumber": "13800138001", 
  "countryCode": "86",
  "watermark": {
    "timestamp": 1625097600,
    "appid": "wx40849aed9922844f"
  }
}
```

### 3. 安全注意事项

1. **session_key安全**：
   - session_key只能在服务器端使用
   - 不要将session_key传递给前端
   - session_key有有效期，需要及时更新

2. **数据验证**：
   - 验证watermark中的appid是否匹配
   - 验证timestamp是否在合理范围内
   - 验证解密后的数据格式是否正确

3. **错误处理**：
   - 妥善处理解密失败的情况
   - 提供友好的错误提示
   - 记录详细的错误日志

## ⚠️ 常见问题

### 1. 解密失败
**可能原因**：
- session_key过期或无效
- encryptedData或iv格式错误
- appid不匹配

**解决方案**：
- 重新调用wx.login()获取新的code
- 检查数据传输过程中是否有编码问题
- 确认appid配置正确

### 2. 用户拒绝授权
**处理方式**：
- 提供友好的引导说明
- 允许用户手动输入手机号
- 在合适的时机再次请求授权

### 3. 网络请求失败
**处理方式**：
- 添加重试机制
- 提供离线缓存
- 显示明确的错误提示

## 📱 完整示例

查看 `miniapp/phone-auth-example.js` 文件获取完整的前端实现示例。

## 🔗 相关文档

- [微信小程序官方文档 - 获取手机号](https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html)
- [微信小程序登录实现指南](./miniapp_wechat_login_implementation.md)
- [小程序用户API设计](./miniapp_user_api_design.md)
