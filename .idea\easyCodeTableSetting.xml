<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="national_debt.bond_registrations">
          <value>
            <TableInfoDTO>
              <option name="comment" value="国债购买意向登记" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="bondRegistrationsId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="客户姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="联系方式" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="phone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="所在地区" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="region" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="购买金额" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="amount" />
                    <option name="type" value="java.lang.Double" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="意向银行" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="bankName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="国债品种(电子式/凭证式)" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="bondType" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="国债期限(3年期/5年期)" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="bondPeriod" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="状态：pending待处理/processed已处理" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="BondRegistrations" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.police_data">
          <value>
            <TableInfoDTO>
              <option name="comment" value="公安人口信息" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="数据ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="policeDataId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="name" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="曾用名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="usedName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="性别" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sex" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="籍贯" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="nativePlace" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="户籍地区" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hukouRegion" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="户籍地" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hukouAddress" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="PoliceData" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.promotion_comment">
          <value>
            <TableInfoDTO>
              <option name="comment" value="活动评论" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="评论ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="commentId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="活动ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="promotionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="父评论ID，NULL表示一级评论" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="parentId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="回复的评论ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="replyToId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="评论内容" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="content" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="评论人昵称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="PromotionComment" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.quiz_questions">
          <value>
            <TableInfoDTO>
              <option name="comment" value="问题配置" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="questionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="问题内容" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="questionText" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="问题类型：single单选/multiple多选/text填空" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="questionType" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="选项配置，单选和多选题必填" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="options" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="排序" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sortOrder" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否必填" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isRequired" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否启用" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isActive" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="QuizQuestions" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.quiz_submissions">
          <value>
            <TableInfoDTO>
              <option name="comment" value="答题记录" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="submissionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="填写人姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="答题数据，包含题目和答案" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="answers" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="状态：pending待处理/contacted已联系/verified已核实/invalid无效" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理员备注" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="adminNote" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="QuizSubmissions" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_article">
          <value>
            <TableInfoDTO>
              <option name="comment" value="文章管理" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="文章ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="articleId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="标题" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="title" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="类型：0国债知识/1国债历史" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="type" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="内容" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="content" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="审核状态：0待审核/1审核成功/2审核失败" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="auditStatus" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="审核失败原因" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="reason" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="发布日期" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="publishDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysArticle" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_hotline">
          <value>
            <TableInfoDTO>
              <option name="comment" value="热线电话" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="热线ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hotlineId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="机构名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="联系电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="phoneNumber" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="显示顺序" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sortOrder" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysHotline" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_institution">
          <value>
            <TableInfoDTO>
              <option name="comment" value="银行机构" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="银行ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="银行名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否删除" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDeleted" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="排序" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sort" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysInstitution" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_notice">
          <value>
            <TableInfoDTO>
              <option name="comment" value="公告管理" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="公告ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="noticeId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="公告标题" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="title" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="公告内容" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="content" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="公告来源" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="source" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysNotice" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_outlet">
          <value>
            <TableInfoDTO>
              <option name="comment" value="银行网点" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="网点ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="outletId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="所属银行ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="网点名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="outletName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="所属区县" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="districtCounty" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="地址" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="address" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="经度" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="longitude" />
                    <option name="type" value="java.lang.Double" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="纬度" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="latitude" />
                    <option name="type" value="java.lang.Double" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="联系电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="contactPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysOutlet" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_promotion">
          <value>
            <TableInfoDTO>
              <option name="comment" value="宣传活动" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="活动ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="promotionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="活动标题" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="title" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="封面图片" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="coverImage" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="活动内容" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="content" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="发布人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="publisher" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysPromotion" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_role">
          <value>
            <TableInfoDTO>
              <option name="comment" value="角色管理" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="角色ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="roleId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="角色名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="roleName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="角色权限字符串" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="roleKey" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="显示顺序" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="roleSort" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否删除" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDeleted" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysRole" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.sys_user">
          <value>
            <TableInfoDTO>
              <option name="comment" value="用户管理" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="用户ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="机构ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="角色ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="roleId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="用户名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="手机号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="phoneNumber" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="密码" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="password" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="微信的唯一标识" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="openId" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="账号状态：normal正常/locked锁定/disabled禁用" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否删除" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDeleted" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="SysUser" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.unpaid_person">
          <value>
            <TableInfoDTO>
              <option name="comment" value="未兑付人员信息" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="未兑付人ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="personId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="name" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="身份证号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="idCard" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="手机号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="phoneNumber" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="承销网点ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="outletId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否未兑付人员 (0:否, 1:是)" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isUnpaidPerson" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="UnpaidPerson" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="national_debt.user_promotion">
          <value>
            <TableInfoDTO>
              <option name="comment" value="用户点赞状态" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="用户ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="文章ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="promotionId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="状态：0未读/1已读" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="UserPromotion" />
              <option name="preName" value="" />
              <option name="saveModelName" value="national-debt" />
              <option name="savePackageName" value="com.youalltech.nationaldebt" />
              <option name="savePath" value="./src/main/java/com/youalltech/nationaldebt" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="ren_hang.task_officer_assignment">
          <value>
            <TableInfoDTO>
              <option name="comment" value="信贷员任务分发表" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="officerAssignmentId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="taskId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="机构id" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="institutionId" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="地址" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="address" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="地址是否匹配" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isMatch" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="信贷员id" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="任务状态（跟进中，拟同意，完结）" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="调查报告审核状态" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="auditStatus" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否转交" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isTransfered" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="签名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="signature" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="原因" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="reason" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdBy" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="跟进时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="followTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="拟同意时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="proposeTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="完结时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="finishTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否放款" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDisbursed" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="完结报告" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="completionReport" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="TaskOfficerAssignment" />
              <option name="preName" value="" />
              <option name="saveModelName" value="renhang" />
              <option name="savePackageName" value="com.youalltech.renhang" />
              <option name="savePath" value="./src/main/java/com/youalltech/renhang" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>