package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniJobType;

/**
 * 职位类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Mapper
public interface MiniJobTypeMapper 
{
    /**
     * 查询职位类型
     * 
     * @param jobTypeId 职位类型主键
     * @return 职位类型
     */
    public MiniJobType selectMiniJobTypeByJobTypeId(Long jobTypeId);

    /**
     * 查询职位类型列表
     * 
     * @param miniJobType 职位类型
     * @return 职位类型集合
     */
    public List<MiniJobType> selectMiniJobTypeList(MiniJobType miniJobType);

    /**
     * 新增职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    public int insertMiniJobType(MiniJobType miniJobType);

    /**
     * 修改职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    public int updateMiniJobType(MiniJobType miniJobType);

    /**
     * 删除职位类型
     * 
     * @param jobTypeId 职位类型主键
     * @return 结果
     */
    public int deleteMiniJobTypeByJobTypeId(Long jobTypeId);

    /**
     * 批量删除职位类型
     * 
     * @param jobTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniJobTypeByJobTypeIds(Long[] jobTypeIds);

    /**
     * 查询所有正常状态的职位类型
     * 
     * @return 职位类型集合
     */
    public List<MiniJobType> selectMiniJobTypeAll();

    /**
     * 查询启用状态的职位类型列表
     * 
     * @return 职位类型集合
     */
    public List<MiniJobType> selectEnabledMiniJobTypeList();
} 