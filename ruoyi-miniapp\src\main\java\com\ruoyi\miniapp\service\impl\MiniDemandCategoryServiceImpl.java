package com.ruoyi.miniapp.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ruoyi.miniapp.mapper.MiniDemandCategoryMapper;
import com.ruoyi.miniapp.domain.MiniDemandCategory;
import com.ruoyi.miniapp.service.IMiniDemandCategoryService;

/**
 * 需求分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniDemandCategoryServiceImpl implements IMiniDemandCategoryService 
{
    @Autowired
    private MiniDemandCategoryMapper miniDemandCategoryMapper;

    /**
     * 查询需求分类
     * 
     * @param categoryId 需求分类主键
     * @return 需求分类
     */
    @Override
    public MiniDemandCategory selectMiniDemandCategoryByCategoryId(Long categoryId)
    {
        return miniDemandCategoryMapper.selectMiniDemandCategoryByCategoryId(categoryId);
    }

    /**
     * 查询需求分类列表
     * 
     * @param miniDemandCategory 需求分类
     * @return 需求分类
     */
    @Override
    public List<MiniDemandCategory> selectMiniDemandCategoryList(MiniDemandCategory miniDemandCategory)
    {
        return miniDemandCategoryMapper.selectMiniDemandCategoryList(miniDemandCategory);
    }

    /**
     * 新增需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    @Override
    public int insertMiniDemandCategory(MiniDemandCategory miniDemandCategory)
    {
        return miniDemandCategoryMapper.insertMiniDemandCategory(miniDemandCategory);
    }

    /**
     * 修改需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    @Override
    public int updateMiniDemandCategory(MiniDemandCategory miniDemandCategory)
    {
        return miniDemandCategoryMapper.updateMiniDemandCategory(miniDemandCategory);
    }

    /**
     * 批量删除需求分类
     * 
     * @param categoryIds 需要删除的需求分类主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandCategoryByCategoryIds(Long[] categoryIds)
    {
        return miniDemandCategoryMapper.deleteMiniDemandCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除需求分类信息
     * 
     * @param categoryId 需求分类主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandCategoryByCategoryId(Long categoryId)
    {
        return miniDemandCategoryMapper.deleteMiniDemandCategoryByCategoryId(categoryId);
    }

    /**
     * 查询启用的需求分类列表（小程序端调用）
     *
     * @return 需求分类集合
     */
    @Override
    public List<MiniDemandCategory> selectEnabledMiniDemandCategoryList()
    {
        return miniDemandCategoryMapper.selectEnabledMiniDemandCategoryList();
    }

    /**
     * 过滤表单字段配置中的隐藏字段（小程序端调用）
     *
     * @param formFields 原始表单字段配置JSON字符串
     * @return 过滤后的表单字段配置JSON字符串
     */
    @Override
    public String getFilteredFormFields(String formFields)
    {
        if (!StringUtils.hasText(formFields)) {
            return formFields;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(formFields);

            if (rootNode.isArray()) {
                ArrayNode filteredArray = objectMapper.createArrayNode();

                for (JsonNode moduleNode : rootNode) {
                    if (moduleNode.isObject()) {
                        ObjectNode filteredModule = ((ObjectNode) moduleNode).deepCopy();

                        // 处理模块中的字段
                        JsonNode fieldsNode = moduleNode.get("fields");
                        if (fieldsNode != null && fieldsNode.isArray()) {
                            ArrayNode filteredFields = objectMapper.createArrayNode();

                            for (JsonNode fieldNode : fieldsNode) {
                                if (fieldNode.isObject()) {
                                    // 检查字段是否隐藏
                                    JsonNode hiddenNode = fieldNode.get("hidden");
                                    boolean isHidden = hiddenNode != null && hiddenNode.asBoolean(false);

                                    // 只添加非隐藏字段
                                    if (!isHidden) {
                                        filteredFields.add(fieldNode);
                                    }
                                }
                            }

                            filteredModule.set("fields", filteredFields);
                        }

                        filteredArray.add(filteredModule);
                    }
                }

                return objectMapper.writeValueAsString(filteredArray);
            }

            return formFields;
        } catch (Exception e) {
            // 如果解析失败，返回原始数据
            return formFields;
        }
    }
}