package com.ruoyi.miniapp.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 企业管理对象 mini_enterprise
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class MiniEnterprise extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 企业ID */
    private Long enterpriseId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String enterpriseName;

    /** 法人 */
    @Excel(name = "法人代表")
    private String legalPerson;

    /** 企业地址 */
    @Excel(name = "企业地址")
    private String address;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 企业简介 */
    @Excel(name = "企业简介")
    private String description;

    /** 企业规模 */
    @Excel(name = "企业规模", readConverterExp = "small=小型,medium=中型,large=大型")
    private String scale;

    /** 成立日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "成立日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date foundingDate;

    /** 官网地址 */
    @Excel(name = "官网地址")
    private String website;

    /** 企业logo */
    private String logoUrl;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 企业产业关联列表 */
    private List<MiniEnterpriseIndustry> industryList;

    public void setEnterpriseId(Long enterpriseId) 
    {
        this.enterpriseId = enterpriseId;
    }

    public Long getEnterpriseId() 
    {
        return enterpriseId;
    }
    
    public void setEnterpriseName(String enterpriseName) 
    {
        this.enterpriseName = enterpriseName;
    }

    public String getEnterpriseName() 
    {
        return enterpriseName;
    }
    
    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }
    
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setScale(String scale) 
    {
        this.scale = scale;
    }

    public String getScale() 
    {
        return scale;
    }
    
    public void setFoundingDate(Date foundingDate) 
    {
        this.foundingDate = foundingDate;
    }

    public Date getFoundingDate() 
    {
        return foundingDate;
    }
    
    public void setWebsite(String website) 
    {
        this.website = website;
    }

    public String getWebsite() 
    {
        return website;
    }
    
    public void setLogoUrl(String logoUrl) 
    {
        this.logoUrl = logoUrl;
    }

    public String getLogoUrl() 
    {
        return logoUrl;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<MiniEnterpriseIndustry> getIndustryList() 
    {
        return industryList;
    }

    public void setIndustryList(List<MiniEnterpriseIndustry> industryList) 
    {
        this.industryList = industryList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("enterpriseId", getEnterpriseId())
            .append("enterpriseName", getEnterpriseName())
            .append("legalPerson", getLegalPerson())
            .append("address", getAddress())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("description", getDescription())
            .append("scale", getScale())
            .append("foundingDate", getFoundingDate())
            .append("website", getWebsite())
            .append("logoUrl", getLogoUrl())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    /**
     * 企业产业关联对象 mini_enterprise_industry
     */
    public static class MiniEnterpriseIndustry
    {
        /** 主键ID */
        private Long id;

        /** 企业ID */
        private Long enterpriseId;

        /** 产业树节点ID */
        private Long industryTreeId;

        /** 产业树节点信息 */
        private MiniIndustryTree industryTree;

        /** 创建时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        /** 更新时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date updateTime;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getEnterpriseId() {
            return enterpriseId;
        }

        public void setEnterpriseId(Long enterpriseId) {
            this.enterpriseId = enterpriseId;
        }

        public Long getIndustryTreeId() {
            return industryTreeId;
        }

        public void setIndustryTreeId(Long industryTreeId) {
            this.industryTreeId = industryTreeId;
        }

        public MiniIndustryTree getIndustryTree() {
            return industryTree;
        }

        public void setIndustryTree(MiniIndustryTree industryTree) {
            this.industryTree = industryTree;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public Date getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(Date updateTime) {
            this.updateTime = updateTime;
        }

        @Override
        public String toString() {
            return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("enterpriseId", getEnterpriseId())
                .append("industryTreeId", getIndustryTreeId())
                .append("industryTree", getIndustryTree())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
        }
    }
} 