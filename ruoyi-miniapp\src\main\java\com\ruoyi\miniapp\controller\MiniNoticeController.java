package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniNotice;
import com.ruoyi.miniapp.service.IMiniNoticeService;
import com.ruoyi.miniapp.annotation.SensitiveWordFilter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 滚动通知Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "滚动通知管理")
@RestController
@RequestMapping("/miniapp/notice")
public class MiniNoticeController extends BaseController
{
    @Autowired
    private IMiniNoticeService miniNoticeService;

    /**
     * 查询滚动通知列表
     */
    @ApiOperation("查询滚动通知列表")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniNotice miniNotice)
    {
        startPage();
        List<MiniNotice> list = miniNoticeService.selectMiniNoticeList(miniNotice);
        return getDataTable(list);
    }

    /**
     * 导出滚动通知列表
     */
    @ApiOperation("导出滚动通知列表")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:export')")
    @Log(title = "滚动通知", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniNotice miniNotice)
    {
        List<MiniNotice> list = miniNoticeService.selectMiniNoticeList(miniNotice);
        ExcelUtil<MiniNotice> util = new ExcelUtil<MiniNotice>(MiniNotice.class);
        util.exportExcel(response, list, "滚动通知数据");
    }

    /**
     * 获取滚动通知详细信息
     */
    @ApiOperation("获取滚动通知详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("通知ID") @RequestBody Long noticeId)
    {
        return AjaxResult.success(miniNoticeService.selectMiniNoticeByNoticeId(noticeId));
    }

    /**
     * 新增滚动通知
     */
    @ApiOperation("新增滚动通知")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:add')")
    @Log(title = "滚动通知", businessType = BusinessType.INSERT)
//    @SensitiveWordFilter(moduleName = "通知管理", strategy = SensitiveWordFilter.FilterStrategy.REPLACE)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("通知信息") @RequestBody MiniNotice miniNotice)
    {
        return toAjax(miniNoticeService.insertMiniNotice(miniNotice));
    }

    /**
     * 修改滚动通知（包括排序修改）
     */
    @ApiOperation("修改滚动通知")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:edit')")
    @Log(title = "滚动通知", businessType = BusinessType.UPDATE)
//    @SensitiveWordFilter(moduleName = "通知管理", strategy = SensitiveWordFilter.FilterStrategy.REPLACE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("通知信息") @RequestBody MiniNotice miniNotice)
    {
        return toAjax(miniNoticeService.updateMiniNotice(miniNotice));
    }

    /**
     * 删除滚动通知
     */
    @ApiOperation("删除滚动通知")
    @PreAuthorize("@ss.hasPermi('miniapp:notice:remove')")
    @Log(title = "滚动通知", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("通知ID数组") @RequestBody Long[] noticeIds)
    {
        return toAjax(miniNoticeService.deleteMiniNoticeByNoticeIds(noticeIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取启用的滚动通知列表
     */
    @ApiOperation("获取启用的滚动通知列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniNotice> list = miniNoticeService.selectEnabledMiniNoticeList();
        return AjaxResult.success(list);
    }
} 