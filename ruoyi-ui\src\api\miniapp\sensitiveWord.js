import request from '@/utils/request'

// 查询敏感词分类列表
export function listSensitiveWordCategory(query) {
  return request({
    url: '/miniapp/sensitiveWordCategory/list',
    method: 'get',
    params: query
  })
}

// 查询所有启用的敏感词分类
export function getEnabledSensitiveWordCategoryList() {
  return request({
    url: '/miniapp/sensitiveWordCategory/enabled',
    method: 'get'
  })
}

// 查询敏感词分类详细
export function getSensitiveWordCategory(categoryId) {
  return request({
    url: '/miniapp/sensitiveWordCategory/' + categoryId,
    method: 'get'
  })
}

// 新增敏感词分类
export function addSensitiveWordCategory(data) {
  return request({
    url: '/miniapp/sensitiveWordCategory',
    method: 'post',
    data: data
  })
}

// 修改敏感词分类
export function updateSensitiveWordCategory(data) {
  return request({
    url: '/miniapp/sensitiveWordCategory',
    method: 'put',
    data: data
  })
}

// 删除敏感词分类
export function delSensitiveWordCategory(categoryId) {
  return request({
    url: '/miniapp/sensitiveWordCategory/' + categoryId,
    method: 'delete'
  })
}

// 查询敏感词列表
export function listSensitiveWord(query) {
  return request({
    url: '/miniapp/sensitiveWord/list',
    method: 'get',
    params: query
  })
}

// 查询敏感词详细
export function getSensitiveWord(wordId) {
  return request({
    url: '/miniapp/sensitiveWord/' + wordId,
    method: 'get'
  })
}

// 新增敏感词
export function addSensitiveWord(data) {
  return request({
    url: '/miniapp/sensitiveWord',
    method: 'post',
    data: data
  })
}

// 修改敏感词
export function updateSensitiveWord(data) {
  return request({
    url: '/miniapp/sensitiveWord',
    method: 'put',
    data: data
  })
}

// 删除敏感词
export function delSensitiveWord(wordId) {
  return request({
    url: '/miniapp/sensitiveWord/' + wordId,
    method: 'delete'
  })
}

// 检测敏感词
export function checkSensitiveWord(text) {
  return request({
    url: '/miniapp/sensitiveWord/check',
    method: 'post',
    params: { text: text }
  })
}

// 过滤敏感词
export function filterSensitiveWord(text, replacement) {
  return request({
    url: '/miniapp/sensitiveWord/filter',
    method: 'post',
    params: { 
      text: text,
      replacement: replacement
    }
  })
}

// 刷新敏感词缓存
export function refreshSensitiveWordCache() {
  return request({
    url: '/miniapp/sensitiveWord/refresh',
    method: 'post'
  })
}

// 查询敏感词检测日志列表
export function listSensitiveWordLog(query) {
  return request({
    url: '/miniapp/sensitiveWordLog/list',
    method: 'get',
    params: query
  })
}

// 查询敏感词检测日志详细
export function getSensitiveWordLog(logId) {
  return request({
    url: '/miniapp/sensitiveWordLog/' + logId,
    method: 'get'
  })
}

// 根据用户ID查询敏感词检测日志
export function getSensitiveWordLogByUserId(userId) {
  return request({
    url: '/miniapp/sensitiveWordLog/user/' + userId,
    method: 'get'
  })
}

// 根据模块名称查询敏感词检测日志
export function getSensitiveWordLogByModuleName(moduleName) {
  return request({
    url: '/miniapp/sensitiveWordLog/module/' + moduleName,
    method: 'get'
  })
}

// 删除敏感词检测日志
export function delSensitiveWordLog(logId) {
  return request({
    url: '/miniapp/sensitiveWordLog/' + logId,
    method: 'delete'
  })
}

// 清理敏感词检测日志
export function cleanSensitiveWordLog(days) {
  return request({
    url: '/miniapp/sensitiveWordLog/clean',
    method: 'delete',
    params: { days: days }
  })
}
