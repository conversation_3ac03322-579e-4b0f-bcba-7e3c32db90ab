package com.ruoyi.miniapp.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.miniapp.domain.MiniActivity;
import com.ruoyi.miniapp.domain.dto.WechatArticleDTO;
import com.ruoyi.miniapp.domain.dto.WechatArticleResponseDTO;
import com.ruoyi.miniapp.domain.dto.WechatConfigDTO;
import com.ruoyi.miniapp.service.IMiniActivityService;
import com.ruoyi.miniapp.service.IWechatActivityService;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 微信公众号活动同步Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class WechatActivityServiceImpl implements IWechatActivityService
{
    private static final Logger logger = LoggerFactory.getLogger(WechatActivityServiceImpl.class);

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IMiniActivityService activityService;

    private static final String WECHAT_CONFIG_PREFIX = "wechat.activity.config.";
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    private static final String ARTICLE_LIST_URL = "https://api.weixin.qq.com/cgi-bin/freepublish/batchget";

    // 同步配置
    private static final int DEFAULT_MAX_PAGES = 1; // 默认最大页数
    private static final int DEFAULT_PAGE_SIZE = 20; // 默认每页大小
    private static final int DEFAULT_REQUEST_DELAY = 100; // 默认请求间隔（毫秒）

    /**
     * 获取微信公众号配置列表
     */
    @Override
    public List<WechatConfigDTO> getWechatConfigList()
    {
        List<WechatConfigDTO> configList = new ArrayList<>();
        
        try {
            // 获取所有微信配置
            String configsJson = configService.selectConfigByKey("wechat.activity.configs");
            if (StringUtils.isNotEmpty(configsJson)) {
                List<WechatConfigDTO> configs = JSON.parseArray(configsJson, WechatConfigDTO.class);
                if (configs != null) {
                    configList.addAll(configs);
                }
            }
        } catch (Exception e) {
            logger.error("获取微信公众号配置失败", e);
        }
        
        return configList;
    }

    /**
     * 保存微信公众号配置
     */
    @Override
    public AjaxResult saveWechatConfig(WechatConfigDTO config)
    {
        try {
            List<WechatConfigDTO> configList = getWechatConfigList();
            
            // 生成配置ID
            if (StringUtils.isEmpty(config.getConfigId())) {
                config.setConfigId(UUID.randomUUID().toString());
            }
            
            // 检查是否已存在
            boolean exists = false;
            for (int i = 0; i < configList.size(); i++) {
                if (configList.get(i).getConfigId().equals(config.getConfigId())) {
                    configList.set(i, config);
                    exists = true;
                    break;
                }
            }
            
            if (!exists) {
                configList.add(config);
            }
            
            // 保存到配置表
            String configsJson = JSON.toJSONString(configList);
            updateOrInsertConfig("wechat.activity.configs", configsJson, "微信公众号活动配置");
            
            return AjaxResult.success("保存成功");
        } catch (Exception e) {
            logger.error("保存微信公众号配置失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除微信公众号配置
     */
    @Override
    public AjaxResult deleteWechatConfig(String configId)
    {
        try {
            List<WechatConfigDTO> configList = getWechatConfigList();
            configList.removeIf(config -> config.getConfigId().equals(configId));
            
            String configsJson = JSON.toJSONString(configList);
            updateOrInsertConfig("wechat.activity.configs", configsJson, "微信公众号活动配置");
            
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            logger.error("删除微信公众号配置失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 测试微信公众号配置
     */
    @Override
    public AjaxResult testWechatConfig(WechatConfigDTO config)
    {
        try {
            String accessToken = getAccessToken(config.getAppId(), config.getAppSecret());
            if (StringUtils.isNotEmpty(accessToken)) {
                return AjaxResult.success("连接成功", accessToken);
            } else {
                return AjaxResult.error("连接失败，请检查AppID和AppSecret");
            }
        } catch (Exception e) {
            logger.error("测试微信公众号配置失败", e);
            return AjaxResult.error("连接失败：" + e.getMessage());
        }
    }

    /**
     * 从微信公众号同步活动
     */
    @Override
    public AjaxResult syncActivitiesFromWechat()
    {
        try {
            List<WechatConfigDTO> configList = getWechatConfigList();
            if (configList.isEmpty()) {
                return AjaxResult.error("未配置微信公众号");
            }
            
            int totalSynced = 0;
            StringBuilder resultMsg = new StringBuilder();
            
            for (WechatConfigDTO config : configList) {
                if (config.getEnabled() != null && config.getEnabled()) {
                    AjaxResult result = syncActivitiesFromSingleWechat(config.getConfigId());
                    if (result.isSuccess()) {
                        int synced = (Integer) result.get("synced");
                        totalSynced += synced;
                        resultMsg.append(config.getName()).append("：").append(synced).append("条；");
                    } else {
                        resultMsg.append(config.getName()).append("：失败；");
                    }
                }
            }
            
            return AjaxResult.success("同步完成，共同步" + totalSynced + "条活动。详情：" + resultMsg.toString());
        } catch (Exception e) {
            logger.error("同步微信活动失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从指定微信公众号同步活动
     */
    @Override
    public AjaxResult syncActivitiesFromSingleWechat(String configId)
    {
        try {
            List<WechatConfigDTO> configList = getWechatConfigList();
            WechatConfigDTO config = configList.stream()
                    .filter(c -> c.getConfigId().equals(configId))
                    .findFirst()
                    .orElse(null);
            
            if (config == null) {
                return AjaxResult.error("配置不存在");
            }
            
            // 获取AccessToken
            String accessToken = getAccessToken(config.getAppId(), config.getAppSecret());
            if (StringUtils.isEmpty(accessToken)) {
                return AjaxResult.error("获取AccessToken失败");
            }
            
            // 获取文章列表
            logger.info("开始获取公众号 {} 的文章列表", config.getName());
            List<WechatArticleDTO> articles = getArticleList(accessToken);
            logger.info("公众号 {} 共获取到 {} 篇文章", config.getName(), articles.size());

            int syncedCount = 0;
            int processedCount = 0;

            for (WechatArticleDTO article : articles) {
                processedCount++;
                if (article.getContent() != null && article.getContent().getNewsItem() != null) {
                    for (WechatArticleDTO.NewsItemDTO newsItem : article.getContent().getNewsItem()) {
                        if (newsItem.getIsDeleted() == null || !newsItem.getIsDeleted()) {
                            // 检查是否已存在
                            MiniActivity existingActivity = new MiniActivity();
                            existingActivity.setWechatArticleId(article.getArticleId());
                            List<MiniActivity> existingList = activityService.selectMiniActivityList(existingActivity);
                            
                            if (existingList.isEmpty()) {
                                // 创建新活动
                                MiniActivity activity = new MiniActivity();
                                activity.setTitle(newsItem.getTitle());
                                activity.setDescription(newsItem.getDigest());
                                String thumbUrl = newsItem.getThumbUrl();
                                thumbUrl = thumbUrl.replaceFirst("^http://", "https://");
                                activity.setCoverImage(thumbUrl);
                                activity.setArticleUrl(newsItem.getUrl());
                                activity.setWechatArticleId(article.getArticleId());
                                activity.setWechatSource(config.getName());
                                activity.setSortOrder(0); // 使用默认排序值
                                activity.setStatus("0");

                                // 设置创建时间为文章的更新时间（如果有的话）
                                if (article.getUpdateTime() != null) {
                                    activity.setCreateTime(new Date(article.getUpdateTime() * 1000L));
                                } else {
                                    activity.setCreateTime(new Date());
                                }
                                
                                activityService.insertMiniActivity(activity);
                                syncedCount++;

                                // 格式化时间用于日志显示
                                String timeStr = "未知时间";
                                if (article.getUpdateTime() != null) {
                                    timeStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(article.getUpdateTime() * 1000L));
                                }

                                logger.info("同步文章成功: {} | 更新时间: {} | 进度: {}/{}",
                                    newsItem.getTitle(), timeStr, processedCount, articles.size());
                            } else {
                                logger.debug("文章已存在，跳过: {}", newsItem.getTitle());
                            }
                        }
                    }
                }

                // 每处理10篇文章输出一次进度
                if (processedCount % 10 == 0) {
                    logger.info("同步进度: {}/{}, 已同步: {}", processedCount, articles.size(), syncedCount);
                }
            }
            
            // 更新最后同步时间
            updateLastSyncTime(configId);

            logger.info("公众号 {} 同步完成，共处理 {} 篇文章，新增 {} 个活动", config.getName(), processedCount, syncedCount);
            return AjaxResult.success("同步成功，共处理 " + processedCount + " 篇文章，新增 " + syncedCount + " 个活动").put("synced", syncedCount).put("processed", processedCount);
        } catch (Exception e) {
            logger.error("同步单个微信公众号活动失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取微信AccessToken
     */
    @Override
    public String getAccessToken(String appId, String appSecret)
    {
        try {
            String url = ACCESS_TOKEN_URL + "?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
            String response = HttpUtils.sendGet(url);
            
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject.containsKey("access_token")) {
                return jsonObject.getString("access_token");
            } else {
                logger.error("获取AccessToken失败：{}", response);
                return null;
            }
        } catch (Exception e) {
            logger.error("获取AccessToken异常", e);
            return null;
        }
    }

    /**
     * 获取文章列表（分页获取所有文章）
     */
    private List<WechatArticleDTO> getArticleList(String accessToken)
    {
        List<WechatArticleDTO> allArticles = new ArrayList<>();
        int offset = 0;
        int pageSize = DEFAULT_PAGE_SIZE; // 每页20条，微信API限制
        int maxPages = DEFAULT_MAX_PAGES; // 最大获取50页，防止无限循环（总共1000篇文章）

        // 从配置中获取同步限制
        String maxPagesConfig = configService.selectConfigByKey("wechat.sync.max_pages");
        if (StringUtils.isNotEmpty(maxPagesConfig)) {
            try {
                maxPages = Integer.parseInt(maxPagesConfig);
                logger.info("使用配置的最大页数: {}", maxPages);
            } catch (NumberFormatException e) {
                logger.warn("配置的最大页数格式错误，使用默认值: {}", DEFAULT_MAX_PAGES);
            }
        }

        try {
            String url = ARTICLE_LIST_URL + "?access_token=" + accessToken;

            for (int page = 0; page < maxPages; page++) {
                // 构建请求参数
                JSONObject params = new JSONObject();
                params.put("offset", offset);
                params.put("count", pageSize);
                params.put("no_content", 0);

                logger.info("获取文章列表 - 第{}页，offset: {}, count: {}", page + 1, offset, pageSize);

                String response = HttpUtils.sendPost(url, params.toJSONString());

                WechatArticleResponseDTO responseDTO = JSON.parseObject(response, WechatArticleResponseDTO.class);
                if (responseDTO != null) {
                    logger.info("获取到文章总数: {}, 本次返回: {}", responseDTO.getTotalCount(), responseDTO.getItemCount());

                    if (responseDTO.getItem() != null && !responseDTO.getItem().isEmpty()) {
                        allArticles.addAll(responseDTO.getItem());

                        // 如果本次返回的数量小于请求的数量，说明已经是最后一页
                        if (responseDTO.getItemCount() < pageSize) {
                            logger.info("已获取所有文章，总数: {}", allArticles.size());
                            break;
                        }

                        // 如果已经获取了所有文章
                        if (allArticles.size() >= responseDTO.getTotalCount()) {
                            logger.info("已获取所有文章，总数: {}", allArticles.size());
                            break;
                        }

                        offset += pageSize;
                    } else {
                        // 没有更多文章了
                        logger.info("没有更多文章，结束获取");
                        break;
                    }
                } else {
                    logger.error("解析响应失败: {}", response);
                    break;
                }

                // 添加延迟，避免请求过于频繁
                Thread.sleep(DEFAULT_REQUEST_DELAY);
            }

            logger.info("文章获取完成，总共获取到 {} 篇文章", allArticles.size());

            // 按更新时间降序排序（最新的文章在前面）
            allArticles.sort((a, b) -> {
                Long timeA = a.getUpdateTime();
                Long timeB = b.getUpdateTime();
                if (timeA == null && timeB == null) return 0;
                if (timeA == null) return 1;
                if (timeB == null) return -1;
                return timeB.compareTo(timeA); // 降序排序
            });

            logger.info("文章按更新时间降序排序完成");
            return allArticles;

        } catch (Exception e) {
            logger.error("获取文章列表失败", e);
            return allArticles; // 返回已获取的文章，而不是空列表
        }
    }

    /**
     * 更新或插入配置
     */
    private void updateOrInsertConfig(String configKey, String configValue, String configName)
    {
        try {
            // 查询现有配置
            SysConfig queryConfig = new SysConfig();
            queryConfig.setConfigKey(configKey);
            List<SysConfig> existingConfigs = configService.selectConfigList(queryConfig);

            if (!existingConfigs.isEmpty()) {
                // 配置存在，更新
                SysConfig config = existingConfigs.get(0);
                config.setConfigValue(configValue);
                configService.updateConfig(config);
            } else {
                // 配置不存在，插入
                SysConfig config = new SysConfig();
                config.setConfigKey(configKey);
                config.setConfigValue(configValue);
                config.setConfigName(configName);
                config.setConfigType("N");
                config.setRemark("微信公众号活动同步配置");
                configService.insertConfig(config);
            }
        } catch (Exception e) {
            logger.error("更新配置失败: " + configKey, e);
            throw new RuntimeException("更新配置失败", e);
        }
    }

    /**
     * 更新指定配置的最后同步时间
     */
    private void updateLastSyncTime(String configId)
    {
        try {
            List<WechatConfigDTO> configList = getWechatConfigList();
            boolean updated = false;

            for (WechatConfigDTO config : configList) {
                if (config.getConfigId().equals(configId)) {
                    config.setLastSyncTime(new Date());
                    updated = true;
                    break;
                }
            }

            if (updated) {
                String configsJson = JSON.toJSONString(configList);
                updateOrInsertConfig("wechat.activity.configs", configsJson, "微信公众号活动配置");
            }
        } catch (Exception e) {
            logger.error("更新最后同步时间失败: " + configId, e);
        }
    }
}
