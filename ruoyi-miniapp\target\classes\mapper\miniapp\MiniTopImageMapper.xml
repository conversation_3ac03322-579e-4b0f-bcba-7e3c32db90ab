<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniTopImageMapper">
    
    <resultMap type="MiniTopImage" id="MiniTopImageResult">
        <result property="topImageId"    column="top_image_id"    />
        <result property="title"    column="title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="pageCode"    column="page_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniTopImageVo">
        select top_image_id, title, image_url, link_url, sort_order, status, page_code, create_by, create_time, update_by, update_time, remark from mini_top_image
    </sql>

    <select id="selectMiniTopImageList" parameterType="MiniTopImage" resultMap="MiniTopImageResult">
        <include refid="selectMiniTopImageVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="pageCode != null  and pageCode != ''"> and page_code = #{pageCode}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniTopImageByTopImageId" parameterType="Long" resultMap="MiniTopImageResult">
        <include refid="selectMiniTopImageVo"/>
        where top_image_id = #{topImageId}
    </select>

    <select id="selectEnabledMiniTopImageList" resultMap="MiniTopImageResult">
        <include refid="selectMiniTopImageVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectEnabledMiniTopImageListByPage" parameterType="String" resultMap="MiniTopImageResult">
        <include refid="selectMiniTopImageVo"/>
        where status = '0' and page_code = #{pageCode}
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniTopImage" parameterType="MiniTopImage" useGeneratedKeys="true" keyProperty="topImageId">
        insert into mini_top_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="linkUrl != null and linkUrl != ''">link_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="pageCode != null and pageCode != ''">page_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="linkUrl != null and linkUrl != ''">#{linkUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="pageCode != null and pageCode != ''">#{pageCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniTopImage" parameterType="MiniTopImage">
        update mini_top_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="linkUrl != null and linkUrl != ''">link_url = #{linkUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="pageCode != null and pageCode != ''">page_code = #{pageCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where top_image_id = #{topImageId}
    </update>

    <delete id="deleteMiniTopImageByTopImageId" parameterType="Long">
        delete from mini_top_image where top_image_id = #{topImageId}
    </delete>

    <delete id="deleteMiniTopImageByTopImageIds" parameterType="String">
        delete from mini_top_image where top_image_id in 
        <foreach item="topImageId" collection="array" open="(" separator="," close=")">
            #{topImageId}
        </foreach>
    </delete>

</mapper> 