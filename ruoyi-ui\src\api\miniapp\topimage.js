import request from '@/utils/request'

// 查询top图片列表
export function listTopImage(query) {
  return request({
    url: '/miniapp/topimage/list',
    method: 'post',
    data: query
  })
}

// 查询top图片详细
export function getTopImage(topImageId) {
  return request({
    url: '/miniapp/topimage/getInfo',
    method: 'post',
    data: topImageId
  })
}

// 新增top图片
export function addTopImage(data) {
  return request({
    url: '/miniapp/topimage/add',
    method: 'post',
    data: data
  })
}

// 修改top图片
export function updateTopImage(data) {
  return request({
    url: '/miniapp/topimage/edit',
    method: 'post',
    data: data
  })
}

// 删除top图片
export function delTopImage(topImageId) {
  return request({
    url: '/miniapp/topimage/remove',
    method: 'post',
    data: topImageId
  })
}

// 导出top图片
export function exportTopImage(query) {
  return request({
    url: '/miniapp/topimage/export',
    method: 'post',
    data: query
  })
} 