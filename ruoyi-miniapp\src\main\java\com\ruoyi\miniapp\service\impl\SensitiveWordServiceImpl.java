package com.ruoyi.miniapp.service.impl;

import java.util.List;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.SensitiveWordMapper;
import com.ruoyi.miniapp.domain.SensitiveWord;
import com.ruoyi.miniapp.service.ISensitiveWordService;
import com.ruoyi.miniapp.utils.SensitiveWordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 敏感词Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SensitiveWordServiceImpl implements ISensitiveWordService
{
    private static final Logger log = LoggerFactory.getLogger(SensitiveWordServiceImpl.class);

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    @Autowired
    private SensitiveWordUtil sensitiveWordUtil;

    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词主键
     * @return 敏感词
     */
    @Override
    public SensitiveWord selectSensitiveWordByWordId(Long wordId)
    {
        return sensitiveWordMapper.selectSensitiveWordByWordId(wordId);
    }

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词
     */
    @Override
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord)
    {
        return sensitiveWordMapper.selectSensitiveWordList(sensitiveWord);
    }

    /**
     * 查询所有启用的敏感词
     * 
     * @return 敏感词集合
     */
    @Override
    public List<SensitiveWord> selectEnabledSensitiveWordList()
    {
        return sensitiveWordMapper.selectEnabledSensitiveWordList();
    }

    /**
     * 根据词类型查询敏感词
     * 
     * @param wordType 词类型（1敏感词 2白名单）
     * @return 敏感词集合
     */
    @Override
    public List<SensitiveWord> selectSensitiveWordByType(String wordType)
    {
        return sensitiveWordMapper.selectSensitiveWordByType(wordType);
    }

    /**
     * 根据分类ID查询敏感词
     * 
     * @param categoryId 分类ID
     * @return 敏感词集合
     */
    @Override
    public List<SensitiveWord> selectSensitiveWordByCategoryId(Long categoryId)
    {
        return sensitiveWordMapper.selectSensitiveWordByCategoryId(categoryId);
    }

    /**
     * 新增敏感词
     *
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSensitiveWord(SensitiveWord sensitiveWord)
    {
        sensitiveWord.setCreateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.insertSensitiveWord(sensitiveWord);

        // 如果是敏感词类型，动态添加到缓存
        if (result > 0 && "1".equals(sensitiveWord.getWordType()) && "0".equals(sensitiveWord.getStatus())) {
            sensitiveWordUtil.addWord(sensitiveWord.getWordContent());
            log.info("新增敏感词并添加到缓存: {}", sensitiveWord.getWordContent());
        }

        return result;
    }

    /**
     * 修改敏感词
     *
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    @Transactional
    public int updateSensitiveWord(SensitiveWord sensitiveWord)
    {
        // 获取原始数据
        SensitiveWord oldWord = sensitiveWordMapper.selectSensitiveWordByWordId(sensitiveWord.getWordId());

        sensitiveWord.setUpdateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.updateSensitiveWord(sensitiveWord);

        if (result > 0) {
            // 如果原来是启用的敏感词，先从缓存中移除
            if (oldWord != null && "1".equals(oldWord.getWordType()) && "0".equals(oldWord.getStatus())) {
                sensitiveWordUtil.removeWord(oldWord.getWordContent());
            }

            // 如果现在是启用的敏感词，添加到缓存
            if ("1".equals(sensitiveWord.getWordType()) && "0".equals(sensitiveWord.getStatus())) {
                sensitiveWordUtil.addWord(sensitiveWord.getWordContent());
            }

            log.info("修改敏感词并更新缓存: {} -> {}",
                    oldWord != null ? oldWord.getWordContent() : "null",
                    sensitiveWord.getWordContent());
        }

        return result;
    }

    /**
     * 批量删除敏感词
     *
     * @param wordIds 需要删除的敏感词主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSensitiveWordByWordIds(Long[] wordIds)
    {
        // 先获取要删除的敏感词内容
        List<SensitiveWord> wordsToDelete = sensitiveWordMapper.selectSensitiveWordByWordIds(wordIds);

        int result = sensitiveWordMapper.deleteSensitiveWordByWordIds(wordIds);

        if (result > 0) {
            // 从缓存中移除删除的敏感词
            for (SensitiveWord word : wordsToDelete) {
                if ("1".equals(word.getWordType()) && "0".equals(word.getStatus())) {
                    sensitiveWordUtil.removeWord(word.getWordContent());
                    log.info("删除敏感词并从缓存移除: {}", word.getWordContent());
                }
            }
        }

        return result;
    }

    /**
     * 删除敏感词信息
     *
     * @param wordId 敏感词主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSensitiveWordByWordId(Long wordId)
    {
        // 先获取要删除的敏感词内容
        SensitiveWord wordToDelete = sensitiveWordMapper.selectSensitiveWordByWordId(wordId);

        int result = sensitiveWordMapper.deleteSensitiveWordByWordId(wordId);

        if (result > 0 && wordToDelete != null && "1".equals(wordToDelete.getWordType()) && "0".equals(wordToDelete.getStatus())) {
            sensitiveWordUtil.removeWord(wordToDelete.getWordContent());
            log.info("删除敏感词并从缓存移除: {}", wordToDelete.getWordContent());
        }

        return result;
    }

    /**
     * 检查敏感词内容是否唯一
     * 
     * @param wordContent 敏感词内容
     * @param wordId 敏感词ID（排除自己）
     * @return 结果
     */
    @Override
    public boolean checkWordContentUnique(String wordContent, Long wordId)
    {
        int count = sensitiveWordMapper.checkWordContentUnique(wordContent, wordId);
        return count == 0;
    }

    /**
     * 检测文本是否包含敏感词
     * 
     * @param text 待检测文本
     * @return 是否包含敏感词
     */
    @Override
    public boolean containsSensitiveWord(String text)
    {
        if (StringUtils.isEmpty(text)) {
            return false;
        }
        return sensitiveWordUtil.contains(text);
    }

    /**
     * 查找文本中的所有敏感词
     * 
     * @param text 待检测文本
     * @return 敏感词列表
     */
    @Override
    public List<String> findSensitiveWords(String text)
    {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        return sensitiveWordUtil.findAll(text);
    }

    /**
     * 替换文本中的敏感词
     * 
     * @param text 待处理文本
     * @return 处理后的文本
     */
    @Override
    public String replaceSensitiveWords(String text)
    {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        return sensitiveWordUtil.replace(text);
    }

    /**
     * 替换文本中的敏感词（指定替换字符）
     * 
     * @param text 待处理文本
     * @param replacement 替换字符
     * @return 处理后的文本
     */
    @Override
    public String replaceSensitiveWords(String text, char replacement)
    {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        return sensitiveWordUtil.replace(text, replacement);
    }

    /**
     * 更新敏感词命中次数
     * 
     * @param hitWords 命中的敏感词列表
     */
    @Override
    public void updateHitCount(List<String> hitWords)
    {
        if (hitWords != null && !hitWords.isEmpty()) {
            sensitiveWordMapper.batchUpdateSensitiveWordHitCount(hitWords);
        }
    }

    /**
     * 刷新敏感词缓存
     */
    @Override
    public void refreshSensitiveWordCache()
    {
        sensitiveWordUtil.refresh();
    }
}
