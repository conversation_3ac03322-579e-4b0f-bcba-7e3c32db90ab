package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniPark;

/**
 * 园区管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface IMiniParkService 
{
    /**
     * 查询园区管理
     * 
     * @param parkId 园区管理主键
     * @return 园区管理
     */
    public MiniPark selectMiniParkByParkId(Long parkId);

    /**
     * 查询园区管理列表
     * 
     * @param miniPark 园区管理
     * @return 园区管理集合
     */
    public List<MiniPark> selectMiniParkList(MiniPark miniPark);

    /**
     * 新增园区管理
     * 
     * @param miniPark 园区管理
     * @return 结果
     */
    public int insertMiniPark(MiniPark miniPark);

    /**
     * 修改园区管理
     * 
     * @param miniPark 园区管理
     * @return 结果
     */
    public int updateMiniPark(MiniPark miniPark);

    /**
     * 批量删除园区管理
     * 
     * @param parkIds 需要删除的园区管理主键集合
     * @return 结果
     */
    public int deleteMiniParkByParkIds(Long[] parkIds);

    /**
     * 删除园区管理信息
     * 
     * @param parkId 园区管理主键
     * @return 结果
     */
    public int deleteMiniParkByParkId(Long parkId);

    /**
     * 查询启用的园区管理列表（小程序端调用）
     * 
     * @return 园区管理集合
     */
    public List<MiniPark> selectEnabledMiniParkList();

    /**
     * 查询推荐的园区管理列表
     * 
     * @return 园区管理集合
     */
    public List<MiniPark> selectRecommendedMiniParkList();
}
