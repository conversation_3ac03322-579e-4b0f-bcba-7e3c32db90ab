package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.VideoShowcaseMapper;
import com.ruoyi.miniapp.domain.VideoShowcase;
import com.ruoyi.miniapp.service.IVideoShowcaseService;

/**
 * 视频展播Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class VideoShowcaseServiceImpl implements IVideoShowcaseService 
{
    @Autowired
    private VideoShowcaseMapper videoShowcaseMapper;

    /**
     * 查询视频展播
     *
     * @param id 视频展播主键
     * @return 视频展播
     */
    @Override
    public VideoShowcase selectVideoShowcaseById(Long id)
    {
        return videoShowcaseMapper.selectVideoShowcaseById(id);
    }

    /**
     * 查询视频展播列表
     *
     * @param videoShowcase 视频展播
     * @return 视频展播
     */
    @Override
    public List<VideoShowcase> selectVideoShowcaseList(VideoShowcase videoShowcase)
    {
        return videoShowcaseMapper.selectVideoShowcaseList(videoShowcase);
    }

    /**
     * 新增视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    @Override
    public int insertVideoShowcase(VideoShowcase videoShowcase)
    {
        if (videoShowcase.getStatus() == null) {
            videoShowcase.setStatus(1);
        }
        if (videoShowcase.getSortOrder() == null) {
            videoShowcase.setSortOrder(0);
        }
        return videoShowcaseMapper.insertVideoShowcase(videoShowcase);
    }

    /**
     * 修改视频展播
     *
     * @param videoShowcase 视频展播
     * @return 结果
     */
    @Override
    public int updateVideoShowcase(VideoShowcase videoShowcase)
    {
        return videoShowcaseMapper.updateVideoShowcase(videoShowcase);
    }

    /**
     * 批量删除视频展播
     *
     * @param ids 需要删除的视频展播主键
     * @return 结果
     */
    @Override
    public int deleteVideoShowcaseByIds(Long[] ids)
    {
        return videoShowcaseMapper.deleteVideoShowcaseByIds(ids);
    }

    /**
     * 删除视频展播信息
     *
     * @param id 视频展播主键
     * @return 结果
     */
    @Override
    public int deleteVideoShowcaseById(Long id)
    {
        return videoShowcaseMapper.deleteVideoShowcaseById(id);
    }
} 