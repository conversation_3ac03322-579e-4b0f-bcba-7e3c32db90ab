<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniNewsCenterMapper">
    
    <resultMap type="MiniNewsCenter" id="MiniNewsCenterResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="author"    column="author"    />
        <result property="thumbUrl"    column="thumb_url"    />
        <result property="digest"    column="digest"    />
        <result property="wechatArticleId"    column="wechat_article_id"    />
        <result property="wechatArticleUrl"    column="wechat_article_url"    />
        <result property="status"    column="status"    />
        <result property="wechatCreateTime"    column="wechat_create_time"    />
        <result property="syncTime"    column="sync_time"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectMiniNewsCenterVo">
        select id, title, author, thumb_url, digest, wechat_article_id, wechat_article_url, status, wechat_create_time, sync_time, created_at, updated_at from mini_news_center
    </sql>

    <select id="selectMiniNewsCenterList" parameterType="MiniNewsCenter" resultMap="MiniNewsCenterResult">
        <include refid="selectMiniNewsCenterVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="author != null  and author != ''"> and author like concat('%', #{author}, '%')</if>
            <if test="wechatArticleId != null  and wechatArticleId != ''"> and wechat_article_id = #{wechatArticleId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="wechatCreateTime != null "> and wechat_create_time = #{wechatCreateTime}</if>
        </where>
        order by wechat_create_time desc, id desc
    </select>
    
    <select id="selectMiniNewsCenterById" parameterType="Long" resultMap="MiniNewsCenterResult">
        <include refid="selectMiniNewsCenterVo"/>
        where id = #{id}
    </select>
    
    <select id="selectMiniNewsCenterByWechatArticleId" parameterType="String" resultMap="MiniNewsCenterResult">
        <include refid="selectMiniNewsCenterVo"/>
        where wechat_article_id = #{wechatArticleId}
    </select>
        
    <insert id="insertMiniNewsCenter" parameterType="MiniNewsCenter" useGeneratedKeys="true" keyProperty="id">
        insert into mini_news_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="author != null">author,</if>
            <if test="thumbUrl != null">thumb_url,</if>
            <if test="digest != null">digest,</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">wechat_article_id,</if>
            <if test="wechatArticleUrl != null and wechatArticleUrl != ''">wechat_article_url,</if>
            <if test="status != null">status,</if>
            <if test="wechatCreateTime != null">wechat_create_time,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="author != null">#{author},</if>
            <if test="thumbUrl != null">#{thumbUrl},</if>
            <if test="digest != null">#{digest},</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">#{wechatArticleId},</if>
            <if test="wechatArticleUrl != null and wechatArticleUrl != ''">#{wechatArticleUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="wechatCreateTime != null">#{wechatCreateTime},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateMiniNewsCenter" parameterType="MiniNewsCenter">
        update mini_news_center
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="author != null">author = #{author},</if>
            <if test="thumbUrl != null">thumb_url = #{thumbUrl},</if>
            <if test="digest != null">digest = #{digest},</if>
            <if test="wechatArticleId != null and wechatArticleId != ''">wechat_article_id = #{wechatArticleId},</if>
            <if test="wechatArticleUrl != null and wechatArticleUrl != ''">wechat_article_url = #{wechatArticleUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="wechatCreateTime != null">wechat_create_time = #{wechatCreateTime},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniNewsCenterById" parameterType="Long">
        delete from mini_news_center where id = #{id}
    </delete>

    <delete id="deleteMiniNewsCenterByIds" parameterType="String">
        delete from mini_news_center where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO mini_news_center (
            title, author, thumb_url, digest, wechat_article_id,
            wechat_article_url, status, wechat_create_time, sync_time
        ) VALUES
        <foreach collection="list" item="item" separator="," >
            (
                #{item.title}, #{item.author}, #{item.thumbUrl}, #{item.digest},
                #{item.wechatArticleId}, #{item.wechatArticleUrl}, #{item.status},
                #{item.wechatCreateTime}, #{item.syncTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            title = VALUES(title),
            author = VALUES(author),
            thumb_url = VALUES(thumb_url),
            digest = VALUES(digest),
            wechat_article_url = VALUES(wechat_article_url),
            status = VALUES(status),
            wechat_create_time = VALUES(wechat_create_time),
            sync_time = VALUES(sync_time),
            updated_at = CURRENT_TIMESTAMP
    </insert>

    <delete id="deleteAllMiniNewsCenter">
        delete from mini_news_center
    </delete>

</mapper>