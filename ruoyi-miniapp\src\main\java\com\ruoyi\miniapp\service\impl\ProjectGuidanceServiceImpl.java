package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.ProjectGuidanceMapper;
import com.ruoyi.miniapp.domain.ProjectGuidance;
import com.ruoyi.miniapp.service.IProjectGuidanceService;

/**
 * 项目指导活动Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProjectGuidanceServiceImpl implements IProjectGuidanceService 
{
    @Autowired
    private ProjectGuidanceMapper projectGuidanceMapper;

    /**
     * 查询项目指导活动
     * 
     * @param guidanceId 项目指导活动主键
     * @return 项目指导活动
     */
    @Override
    public ProjectGuidance selectProjectGuidanceByGuidanceId(Long guidanceId)
    {
        return projectGuidanceMapper.selectProjectGuidanceByGuidanceId(guidanceId);
    }

    /**
     * 查询项目指导活动列表
     * 
     * @param projectGuidance 项目指导活动
     * @return 项目指导活动
     */
    @Override
    public List<ProjectGuidance> selectProjectGuidanceList(ProjectGuidance projectGuidance)
    {
        return projectGuidanceMapper.selectProjectGuidanceList(projectGuidance);
    }

    /**
     * 新增项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    @Override
    public int insertProjectGuidance(ProjectGuidance projectGuidance)
    {
        projectGuidance.setCreateTime(DateUtils.getNowDate());
        return projectGuidanceMapper.insertProjectGuidance(projectGuidance);
    }

    /**
     * 修改项目指导活动
     * 
     * @param projectGuidance 项目指导活动
     * @return 结果
     */
    @Override
    public int updateProjectGuidance(ProjectGuidance projectGuidance)
    {
        projectGuidance.setUpdateTime(DateUtils.getNowDate());
        return projectGuidanceMapper.updateProjectGuidance(projectGuidance);
    }

    /**
     * 批量删除项目指导活动
     * 
     * @param guidanceIds 需要删除的项目指导活动主键
     * @return 结果
     */
    @Override
    public int deleteProjectGuidanceByGuidanceIds(Long[] guidanceIds)
    {
        return projectGuidanceMapper.deleteProjectGuidanceByGuidanceIds(guidanceIds);
    }

    /**
     * 删除项目指导活动信息
     * 
     * @param guidanceId 项目指导活动主键
     * @return 结果
     */
    @Override
    public int deleteProjectGuidanceByGuidanceId(Long guidanceId)
    {
        return projectGuidanceMapper.deleteProjectGuidanceByGuidanceId(guidanceId);
    }
} 