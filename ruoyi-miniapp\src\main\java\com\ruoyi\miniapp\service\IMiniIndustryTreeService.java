package com.ruoyi.miniapp.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.miniapp.domain.MiniIndustryTree;

/**
 * 产业树状结构Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMiniIndustryTreeService 
{
    /**
     * 查询产业树状结构
     * 
     * @param id 产业树状结构主键
     * @return 产业树状结构
     */
    public MiniIndustryTree selectMiniIndustryTreeById(Long id);

    /**
     * 查询产业树状结构列表
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 产业树状结构集合
     */
    public List<MiniIndustryTree> selectMiniIndustryTreeList(MiniIndustryTree miniIndustryTree);

    /**
     * 查询根节点列表（产业类型）
     * 
     * @return 产业类型列表
     */
    public List<MiniIndustryTree> selectIndustryTypeList();

    /**
     * 根据父节点ID查询子节点列表
     * 
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    public List<MiniIndustryTree> selectChildrenByParentId(Long parentId);

    /**
     * 根据节点类型和父节点ID查询节点列表
     * 
     * @param nodeType 节点类型
     * @param parentId 父节点ID
     * @return 节点列表
     */
    public List<MiniIndustryTree> selectByNodeTypeAndParentId(String nodeType, Long parentId);

    /**
     * 新增产业树状结构
     * 
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    public int insertMiniIndustryTree(MiniIndustryTree miniIndustryTree);

    /**
     * 修改产业树状结构
     *
     * @param miniIndustryTree 产业树状结构
     * @return 结果
     */
    public int updateMiniIndustryTree(MiniIndustryTree miniIndustryTree);

    /**
     * 级联更新子节点状态
     *
     * @param parentId 父节点ID
     * @param status 状态
     * @return 结果
     */
    public int updateChildrenStatus(Long parentId, String status);

    /**
     * 批量删除产业树状结构（逻辑删除）
     *
     * @param ids 需要删除的产业树状结构主键集合
     * @return 结果
     */
    public int deleteMiniIndustryTreeByIds(Long[] ids);

    /**
     * 级联删除产业树节点及其子节点（逻辑删除）
     *
     * @param id 节点主键
     * @return 结果
     */
    public int deleteMiniIndustryTreeCascade(Long id);

    /**
     * 检查节点删除前的约束条件
     *
     * @param id 节点主键
     * @return 检查结果消息，null表示可以删除
     */
    public String checkDeleteConstraints(Long id);

    /**
     * 删除产业树状结构信息
     * 
     * @param id 产业树状结构主键
     * @return 结果
     */
    public int deleteMiniIndustryTreeById(Long id);

    /**
     * 构建树状结构
     * 
     * @param nodes 节点列表
     * @return 树状结构
     */
    public List<MiniIndustryTree> buildTree(List<MiniIndustryTree> nodes);

    /**
     * 查询完整的产业树（仅启用的）
     *
     * @return 产业树
     */
    public List<MiniIndustryTree> selectIndustryTree();

    /**
     * 查询所有产业树（包括禁用的，用于后台管理）
     *
     * @return 产业树
     */
    public List<MiniIndustryTree> selectAllIndustryTree();

    /**
     * 判断行业类型是否被企业绑定
     * @param industryTypeIds 行业类型ID数组
     * @return 被绑定数量
     */
    int countEnterpriseBindIndustryTypes(Long[] industryTypeIds);

    /**
     * 按层级查询节点
     * @param level 层级
     * @return 节点列表
     */
    List<MiniIndustryTree> selectNodesByLevel(Integer level);

    /**
     * 获取三层级结构树
     * @return 三层级树结构
     */
    List<MiniIndustryTree> selectThreeLevelTree();

    /**
     * 验证节点层级规则
     * @param node 节点信息
     * @return 验证结果
     */
    boolean validateNodeLevelRules(MiniIndustryTree node);

    /**
     * 根据上中下游类型查询第二层级节点
     * @param streamType 上中下游类型
     * @return 节点列表
     */
    List<MiniIndustryTree> selectNodesByStreamType(String streamType);

    /**
     * 获取节点的完整路径
     * @param nodeId 节点ID
     * @return 完整路径
     */
    String getNodeFullPath(Long nodeId);

    /**
     * 批量查询行业节点信息（包含一级节点信息）
     * @param industryIds 行业ID列表
     * @return 行业信息列表
     */
    List<Map<String, Object>> selectBatchIndustryWithRoot(List<Long> industryIds);
}