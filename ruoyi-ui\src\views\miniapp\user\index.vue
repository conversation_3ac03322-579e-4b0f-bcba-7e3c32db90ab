<template>
  <div class="app-container">
    <!--用户数据-->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="关键字" prop="searchValue">
            <el-input
              v-model="queryParams.searchValue"
              placeholder="搜索姓名、昵称、手机号等"
              clearable
              style="width: 280px"
              @keyup.enter.native="handleQuery"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="用户名称"
              clearable
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="微信昵称" prop="weixinNickname">
            <el-input
              v-model="queryParams.weixinNickname"
              placeholder="微信昵称"
              clearable
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="真实姓名" prop="realName">
            <el-input
              v-model="queryParams.realName"
              placeholder="真实姓名"
              clearable
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input
              v-model="queryParams.phonenumber"
              placeholder="手机号码"
              clearable
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="毕业年份" prop="graduationYear">
            <el-select
              v-model="queryParams.graduationYear"
              placeholder="请选择毕业年份"
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="year in graduationYears"
                :key="year"
                :label="year + '年'"
                :value="year"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="地区" prop="region">
            <el-select
              v-model="queryParams.region"
              placeholder="请选择地区"
              clearable
              filterable
              style="width: 150px"
            >
              <el-option
                v-for="province in provinces"
                :key="province"
                :label="province"
                :value="province"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="行业领域" prop="industryField">
            <el-select
              v-model="queryParams.industryField"
              placeholder="请选择行业领域"
              clearable
              filterable
              style="width: 150px"
            >
              <el-option
                v-for="industry in firstLevelIndustries"
                :key="industry.id"
                :label="industry.nodeName"
                :value="industry.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 200px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->
            <!-- <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['miniapp:user:edit']"
            >修改</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-close"
              size="mini"
              :disabled="multiple"
              @click="handleDisable"
              v-hasPermi="['miniapp:user:edit']"
            >停用</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['miniapp:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" width="80" />
          <el-table-column label="微信昵称" align="center" key="weixinNickname" prop="weixinNickname" v-if="columns[1].visible" :show-overflow-tooltip="true" />
          <el-table-column label="微信头像" align="center" key="weixinAvatar" v-if="columns[2].visible" width="80">
            <template slot-scope="scope">
              <el-avatar v-if="scope.row.weixinAvatar" :src="scope.row.weixinAvatar" :size="40"></el-avatar>
              <el-avatar v-else :size="40" icon="el-icon-user-solid"></el-avatar>
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center" key="realName" prop="realName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
          <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber" v-if="columns[4].visible" width="120" />
          <el-table-column label="形象照" align="center" key="portraitUrl" v-if="columns[5].visible" width="80">
            <template slot-scope="scope">
              <image-preview :src="scope.row.portraitUrl" :width="50" :height="50" v-if="scope.row.portraitUrl"/>
              <el-avatar v-else :size="50" icon="el-icon-picture"></el-avatar>
            </template>
          </el-table-column>
          <el-table-column label="毕业院校" align="center" key="graduateSchool" prop="graduateSchool" v-if="columns[6].visible" :show-overflow-tooltip="true" />
          <el-table-column label="所属企业" align="center" key="currentCompany" prop="currentCompany" v-if="columns[7].visible" :show-overflow-tooltip="true" />
          <el-table-column label="行业领域" align="center" key="industryField" v-if="columns[8].visible" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span v-if="scope.row.industryNames">{{ scope.row.industryNames }}</span>
              <span v-else style="color: #C0C4CC;">未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" key="status" v-if="columns[9].visible">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[10].visible" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
                v-hasPermi="['miniapp:user:query']"
              >详情</el-button>
              <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['miniapp:user:edit']"
              >修改</el-button> -->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-close"
                @click="handleDisable(scope.row)"
                v-hasPermi="['miniapp:user:edit']"
                :disabled="scope.row.status === '1'"
              >停用</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

    <!-- 修改用户对话框已移除：用户信息不应由管理员修改 -->


    <!-- 用户详情对话框 -->
    <el-dialog title="用户详情" :visible.sync="openView" width="1000px" append-to-body>
      <div class="user-detail-container">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
          </div>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="用户编号">{{ viewForm.userId }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ viewForm.realName || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="微信昵称">{{ viewForm.weixinNickname || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ viewForm.phonenumber || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="性别">
              <dict-tag :options="dict.type.sys_user_sex" :value="viewForm.sex"/>
            </el-descriptions-item>
            <el-descriptions-item label="出生日期">{{ parseTime(viewForm.birthDate, '{y}-{m}-{d}') || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="籍贯">{{ viewForm.region || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="dict.type.sys_normal_disable" :value="viewForm.status"/>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 头像信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">头像信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="avatar-item">
                <div class="avatar-label">微信头像</div>
                <div class="avatar-content">
                  <image-preview :src="viewForm.weixinAvatar" :width="80" :height="80" v-if="viewForm.weixinAvatar"/>
                  <div v-else class="no-avatar">未设置</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="avatar-item">
                <div class="avatar-label">形象照</div>
                <div class="avatar-content">
                  <image-preview :src="viewForm.portraitUrl" :width="80" :height="80" v-if="viewForm.portraitUrl"/>
                  <div v-else class="no-avatar">未设置</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 教育背景 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">教育背景</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="毕业院校">{{ viewForm.graduateSchool || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="毕业年份">{{ viewForm.graduationYear || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="专业">{{ viewForm.major || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="学院">{{ viewForm.college || '未设置' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 职业信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">职业信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="当前公司">{{ viewForm.currentCompany || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="行业领域">
              <div v-if="viewForm.industryTags && viewForm.industryTags.length > 0" class="industry-tags">
                <el-tag
                  v-for="tag in viewForm.industryTags"
                  :key="tag.id"
                  size="small"
                  style="margin-right: 5px; margin-bottom: 5px;"
                >
                  {{ tag.nodeName }}
                </el-tag>
              </div>
              <span v-else>未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="职位名称">{{ viewForm.positionTitle || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="个人介绍" :span="2">
              <div class="personal-intro">
                {{ viewForm.personalIntroduction || '未设置' }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 积分信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">积分信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="总积分">{{ viewForm.totalPoints || 0 }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 系统信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">系统信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="最后登录时间">{{ parseTime(viewForm.lastLoginTime) || '未登录' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ viewForm.remark || '无备注' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openView = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMiniUser, getMiniUser, changeMiniUserStatus, batchDisableMiniUser } from "@/api/miniapp/user";
import { getNodesByLevel, getBatchIndustryInfo } from "@/api/miniapp/industry";

export default {
  name: "MiniUser",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: {
    ImageUpload: () => import("@/components/ImageUpload"),
    ImagePreview: () => import("@/components/ImagePreview")
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 日期范围
      dateRange: [],
      // 用户详情对话框
      openView: false,
      // 用户详情数据
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: undefined,
        userName: undefined,
        weixinNickname: undefined,
        realName: undefined,
        phonenumber: undefined,
        status: undefined,
        graduationYear: undefined,
        region: undefined,
        industryField: undefined
      },
      // 毕业年份选项
      graduationYears: [],
      // 省份选项
      provinces: [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾',
        '香港', '澳门'
      ],
      // 一级行业选项
      firstLevelIndustries: [],
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `微信昵称`, visible: true },
        { key: 2, label: `微信头像`, visible: true },
        { key: 3, label: `姓名`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `形象照`, visible: true },
        { key: 6, label: `毕业院校`, visible: true },
        { key: 7, label: `所属企业`, visible: true },
        { key: 8, label: `行业领域`, visible: true },
        { key: 9, label: `状态`, visible: true },
        { key: 10, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        weixinNickname: [
          { required: true, message: "微信昵称不能为空", trigger: "blur" },
          { min: 1, max: 30, message: "微信昵称长度必须在1到30个字符之间", trigger: "blur" }
        ],
        realName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
          { min: 2, max: 30, message: "姓名长度必须在2到30个字符之间", trigger: "blur" }
        ],
        phonenumber: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.initGraduationYears();
    this.initFirstLevelIndustries();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listMiniUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      // 使用真实姓名或微信昵称作为显示名称
      let displayName = row.realName || row.weixinNickname || row.userName || '该用户';
      this.$modal.confirm('确认要"' + text + '""' + displayName + '"用户吗？').then(function() {
        return changeMiniUserStatus(row.userId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮和表单重置方法已移除：不再需要修改功能
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },


    /** 查看详情按钮操作 */
    async handleView(row) {
      const userId = row.userId;
      try {
        const response = await getMiniUser(userId);
        this.viewForm = response.data;
        // 解析行业标签
        await this.parseIndustryTags(this.viewForm);
        this.openView = true;
      } catch (error) {
        console.error('获取用户详情失败', error);
        this.$modal.msgError('获取用户详情失败');
      }
    },
    /** 解析行业标签 */
    async parseIndustryTags(user) {
      if (!user.industryField) {
        user.industryTags = [];
        return;
      }

      try {
        const industryIds = user.industryField.split(',')
          .filter(id => id.trim())
          .map(id => parseInt(id.trim()))
          .filter(id => !isNaN(id));

        if (industryIds.length === 0) {
          user.industryTags = [];
          return;
        }

        // 批量查询行业信息
        const response = await getBatchIndustryInfo(industryIds);
        if (response.data && Array.isArray(response.data)) {
          user.industryTags = response.data.map(item => ({
            id: item.id,
            nodeName: item.nodeName,
            nodeType: item.nodeType,
            nodeLevel: item.nodeLevel,
            streamType: item.streamType,
            rootNode: item.rootNode
          }));
        } else {
          user.industryTags = [];
        }
      } catch (error) {
        console.error('解析行业标签失败', error);
        user.industryTags = [];
      }
    },
    /** 解析编辑表单中的行业标签 - 已禁用 */
    /*
    async parseFormIndustryTags() {
      if (!this.form.industryField) {
        this.form.industryTags = [];
        return;
      }

      try {
        const industryIds = this.form.industryField.split(',').filter(id => id.trim());
        const industryTags = [];

        for (const industryId of industryIds) {
          if (industryId.trim()) {
            try {
              const response = await getIndustryNodeInfo(industryId.trim());
              if (response.data) {
                industryTags.push({
                  id: response.data.id,
                  nodeName: response.data.nodeName,
                  nodeType: response.data.nodeType,
                  nodeLevel: response.data.nodeLevel
                });
              }
            } catch (error) {
              console.warn(`获取行业信息失败，ID: ${industryId}`, error);
            }
          }
        }

        this.form.industryTags = industryTags;
      } catch (error) {
        console.error('解析编辑表单行业标签失败', error);
        this.form.industryTags = [];
      }
    },
    */
    /** 修改按钮操作 - 已禁用 */
    /*
    async handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      try {
        const response = await getMiniUser(userId);
        this.form = response.data;
        // 解析行业标签
        await this.parseFormIndustryTags();
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      } catch (error) {
        console.error('获取用户信息失败', error);
        this.$modal.msgError('获取用户信息失败');
      }
    },
    */

    /** 提交按钮 - 已禁用 */
    /*
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateMiniUser(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    */
    /** 停用按钮操作 */
    handleDisable(row) {
      let confirmMessage;
      let userIds = [];

      if (row.userId) {
        // 单个停用
        userIds = [row.userId];
        let displayName = row.realName || row.weixinNickname || row.userName || '该用户';
        confirmMessage = '是否确认停用用户"' + displayName + '"？停用后该用户将无法登录小程序。';
      } else {
        // 批量停用
        userIds = this.ids;
        confirmMessage = '是否确认停用选中的' + this.ids.length + '个用户？停用后这些用户将无法登录小程序。';
      }

      this.$modal.confirm(confirmMessage).then(() => {
        // 调用批量停用API
        return batchDisableMiniUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("停用成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 初始化毕业年份选项 */
    initGraduationYears() {
      const currentYear = new Date().getFullYear();
      const startYear = 1980;
      this.graduationYears = [];
      for (let year = currentYear; year >= startYear; year--) {
        this.graduationYears.push(year.toString());
      }
    },
    /** 初始化一级行业选项 */
    async initFirstLevelIndustries() {
      try {
        const response = await getNodesByLevel(1);
        this.firstLevelIndustries = response.data || [];
      } catch (error) {
        console.error('获取一级行业失败', error);
        this.firstLevelIndustries = [];
      }
    }
  }
};
</script>

<style scoped>
.user-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #409EFF;
  border-radius: 2px;
}

.avatar-item {
  text-align: center;
  padding: 20px;
}

.avatar-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
  font-weight: 500;
}

.avatar-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.no-avatar {
  color: #C0C4CC;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 2px dashed #E4E7ED;
  border-radius: 50%;
  background-color: #FAFAFA;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-detail-container {
    max-height: 60vh;
  }

  .avatar-item {
    padding: 15px;
  }

  .avatar-content {
    min-height: 60px;
  }

  .no-avatar {
    width: 60px;
    height: 60px;
  }

  .industry-field-container .industry-tags-display {
    margin-bottom: 8px;
  }

  .industry-tags .el-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  .personal-intro {
    max-width: 100%;
    word-wrap: break-word;
    white-space: pre-wrap;
    line-height: 1.5;
  }
}
</style>
