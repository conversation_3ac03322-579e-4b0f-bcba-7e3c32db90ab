package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniNewsCenter;

/**
 * 新闻中心Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
public interface IMiniNewsCenterService 
{
    /**
     * 查询新闻中心
     * 
     * @param id 新闻中心主键
     * @return 新闻中心
     */
    public MiniNewsCenter selectMiniNewsCenterById(Long id);

    /**
     * 查询新闻中心列表
     * 
     * @param miniNewsCenter 新闻中心
     * @return 新闻中心集合
     */
    public List<MiniNewsCenter> selectMiniNewsCenterList(MiniNewsCenter miniNewsCenter);

    /**
     * 新增新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    public int insertMiniNewsCenter(MiniNewsCenter miniNewsCenter);

    /**
     * 修改新闻中心
     * 
     * @param miniNewsCenter 新闻中心
     * @return 结果
     */
    public int updateMiniNewsCenter(MiniNewsCenter miniNewsCenter);

    /**
     * 批量删除新闻中心
     * 
     * @param ids 需要删除的新闻中心主键集合
     * @return 结果
     */
    public int deleteMiniNewsCenterByIds(Long[] ids);

    /**
     * 删除新闻中心信息
     * 
     * @param id 新闻中心主键
     * @return 结果
     */
    public int deleteMiniNewsCenterById(Long id);

    /**
     * 从微信公众号同步文章
     * 
     * @return 同步结果
     */
    public String syncFromWechat();

    /**
     * 手动同步指定数量的文章
     *
     * @param count 同步数量
     * @param offset 偏移量
     * @return 同步结果
     */
    public String syncFromWechat(int count, int offset);

    /**
     * 清空所有新闻中心数据
     *
     * @return 结果
     */
    public int deleteAllMiniNewsCenter();
} 