package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniEventRegistration;

/**
 * 用户报名记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Mapper
public interface MiniEventRegistrationMapper 
{
    /**
     * 查询用户报名记录
     * 
     * @param registrationId 用户报名记录主键
     * @return 用户报名记录
     */
    public MiniEventRegistration selectMiniEventRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询用户报名记录列表
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 用户报名记录集合
     */
    public List<MiniEventRegistration> selectMiniEventRegistrationList(MiniEventRegistration miniEventRegistration);

    /**
     * 新增用户报名记录
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 结果
     */
    public int insertMiniEventRegistration(MiniEventRegistration miniEventRegistration);

    /**
     * 修改用户报名记录
     * 
     * @param miniEventRegistration 用户报名记录
     * @return 结果
     */
    public int updateMiniEventRegistration(MiniEventRegistration miniEventRegistration);

    /**
     * 删除用户报名记录
     * 
     * @param registrationId 用户报名记录主键
     * @return 结果
     */
    public int deleteMiniEventRegistrationByRegistrationId(Long registrationId);

    /**
     * 批量删除用户报名记录
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniEventRegistrationByRegistrationIds(Long[] registrationIds);

    /**
     * 根据活动ID查询报名记录列表
     * 
     * @param eventId 活动ID
     * @return 报名记录集合
     */
    public List<MiniEventRegistration> selectMiniEventRegistrationByEventId(Long eventId);

    /**
     * 根据用户ID查询报名记录列表
     * 
     * @param userId 用户ID
     * @return 报名记录集合
     */
    public List<MiniEventRegistration> selectMiniEventRegistrationByUserId(Long userId);

    /**
     * 查询用户是否已报名某活动
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @return 报名记录
     */
    public MiniEventRegistration selectRegistrationByEventAndUser(Long eventId, Long userId);

    /**
     * 统计活动报名人数
     * 
     * @param eventId 活动ID
     * @return 报名人数
     */
    public int countRegistrationByEventId(Long eventId);
} 