package com.ruoyi.mq.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class RabbitMQConfig {

    /**
     * 直接交换机名称
     */
    public static final String DIRECT_EXCHANGE = "ruoyi.direct";

    /**
     * 主题交换机名称
     */
    public static final String TOPIC_EXCHANGE = "ruoyi.topic";

    /**
     * 延迟交换机名称
     */
    public static final String DELAYED_EXCHANGE = "ruoyi.delayed";

    /**
     * 默认队列名称
     */
    public static final String DEFAULT_QUEUE = "ruoyi.default.queue";

    /**
     * 示例队列名称
     */
    public static final String DEMO_QUEUE = "ruoyi.demo.queue";

    /**
     * 延迟队列名称
     */
    public static final String DELAYED_QUEUE = "ruoyi.delayed.queue";

    /**
     * 默认路由键
     */
    public static final String DEFAULT_ROUTING_KEY = "ruoyi.default";

    /**
     * 示例路由键
     */
    public static final String DEMO_ROUTING_KEY = "ruoyi.demo";

    /**
     * 延迟路由键
     */
    public static final String DELAYED_ROUTING_KEY = "ruoyi.delayed";

    /**
     * 配置JSON消息转换器
     */
    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * 配置RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter());
        return rabbitTemplate;
    }

    /**
     * 声明直接交换机
     */
    @Bean
    public DirectExchange directExchange() {
        return new DirectExchange(DIRECT_EXCHANGE);
    }

    /**
     * 声明主题交换机
     */
    @Bean
    public TopicExchange topicExchange() {
        return new TopicExchange(TOPIC_EXCHANGE);
    }

    /**
     * 声明延迟交换机
     * 使用普通的交换机替代，不使用需要插件的x-delayed-message类型
     */
    @Bean
    public DirectExchange delayedExchange() {
        return new DirectExchange(DELAYED_EXCHANGE);
    }

    /**
     * 声明默认队列
     */
    @Bean
    public Queue defaultQueue() {
        return QueueBuilder.durable(DEFAULT_QUEUE).build();
    }

    /**
     * 声明示例队列
     */
    @Bean
    public Queue demoQueue() {
        return QueueBuilder.durable(DEMO_QUEUE).build();
    }

    /**
     * 声明延迟队列
     * 使用TTL和死信队列实现延迟功能
     */
    @Bean
    public Queue delayedQueue() {
        Map<String, Object> args = new HashMap<>(3);
        // 设置死信交换机
        args.put("x-dead-letter-exchange", DIRECT_EXCHANGE);
        // 设置死信路由键
        args.put("x-dead-letter-routing-key", DEFAULT_ROUTING_KEY);
        
        return QueueBuilder.durable(DELAYED_QUEUE)
                .withArguments(args)
                .build();
    }

    /**
     * 将默认队列绑定到直接交换机
     */
    @Bean
    public Binding defaultBinding() {
        return BindingBuilder.bind(defaultQueue()).to(directExchange()).with(DEFAULT_ROUTING_KEY);
    }

    /**
     * 将示例队列绑定到直接交换机
     */
    @Bean
    public Binding demoBinding() {
        return BindingBuilder.bind(demoQueue()).to(directExchange()).with(DEMO_ROUTING_KEY);
    }

    /**
     * 将延迟队列绑定到延迟交换机
     */
    @Bean
    public Binding delayedBinding() {
        return BindingBuilder.bind(delayedQueue()).to(delayedExchange()).with(DELAYED_ROUTING_KEY);
    }
} 