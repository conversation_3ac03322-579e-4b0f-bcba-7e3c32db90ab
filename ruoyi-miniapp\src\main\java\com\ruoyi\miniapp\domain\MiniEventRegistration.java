package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户报名记录对象 mini_event_registration
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public class MiniEventRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID */
    private Long registrationId;

    /** 活动ID */
    @Excel(name = "活动ID")
    private Long eventId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 报名表单数据（JSON格式） */
    @Excel(name = "报名表单数据")
    private String formData;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;

    /** 活动名称（关联查询字段） */
    @Excel(name = "活动名称")
    private String eventName;

    /** 活动类型（关联查询字段） */
    @Excel(name = "活动类型", readConverterExp = "activity=活动,guidance=项目指导")
    private String eventType;

    /** 用户名称（关联查询字段） */
    @Excel(name = "用户名称")
    private String userName;

    /** 用户手机号（关联查询字段） */
    @Excel(name = "用户手机号")
    private String userPhone;

    public void setRegistrationId(Long registrationId) 
    {
        this.registrationId = registrationId;
    }

    public Long getRegistrationId() 
    {
        return registrationId;
    }
    
    public void setEventId(Long eventId) 
    {
        this.eventId = eventId;
    }

    public Long getEventId() 
    {
        return eventId;
    }
    
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    
    public void setFormData(String formData) 
    {
        this.formData = formData;
    }

    public String getFormData() 
    {
        return formData;
    }
    
    public void setRegistrationTime(Date registrationTime) 
    {
        this.registrationTime = registrationTime;
    }

    public Date getRegistrationTime() 
    {
        return registrationTime;
    }
    
    public void setEventName(String eventName)
    {
        this.eventName = eventName;
    }

    public String getEventName()
    {
        return eventName;
    }

    public void setEventType(String eventType)
    {
        this.eventType = eventType;
    }

    public String getEventType()
    {
        return eventType;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setUserPhone(String userPhone) 
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone() 
    {
        return userPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("registrationId", getRegistrationId())
            .append("eventId", getEventId())
            .append("userId", getUserId())
            .append("formData", getFormData())
            .append("registrationTime", getRegistrationTime())
            .append("eventName", getEventName())
            .append("eventType", getEventType())
            .append("userName", getUserName())
            .append("userPhone", getUserPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 