import request from '@/utils/request'

// 查询招聘职位列表
export function listJob(query) {
  return request({
    url: '/miniapp/job/list',
    method: 'get',
    params: query
  })
}

// 查询招聘职位详细
export function getJob(jobId) {
  return request({
    url: '/miniapp/job/' + jobId,
    method: 'get'
  })
}

// 新增招聘职位
export function addJob(data) {
  return request({
    url: '/miniapp/job',
    method: 'post',
    data: data
  })
}

// 修改招聘职位
export function updateJob(data) {
  return request({
    url: '/miniapp/job',
    method: 'put',
    data: data
  })
}

// 删除招聘职位
export function delJob(jobId) {
  return request({
    url: '/miniapp/job/' + jobId,
    method: 'delete'
  })
}

// 获取启用的招聘职位列表
export function getEnabledJobList() {
  return request({
    url: '/miniapp/job/enabled',
    method: 'get'
  })
}

// 获取推荐的招聘职位列表
export function getRecommendedJobList() {
  return request({
    url: '/miniapp/job/recommended',
    method: 'get'
  })
} 