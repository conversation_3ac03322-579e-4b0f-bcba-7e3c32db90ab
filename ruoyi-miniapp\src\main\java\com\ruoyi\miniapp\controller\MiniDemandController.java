package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniDemand;
import com.ruoyi.miniapp.service.IMiniDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 需求信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "需求信息管理")
@RestController
@RequestMapping("/miniapp/demand")
public class MiniDemandController extends BaseController
{
    @Autowired
    private IMiniDemandService miniDemandService;

    /**
     * 查询需求信息列表
     */
    @ApiOperation("查询需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniDemand miniDemand)
    {
        startPage();
        List<MiniDemand> list = miniDemandService.selectMiniDemandList(miniDemand);
        return getDataTable(list);
    }

    /**
     * 导出需求信息列表
     */
    @ApiOperation("导出需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:export')")
    @Log(title = "需求信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniDemand miniDemand)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandList(miniDemand);
        ExcelUtil<MiniDemand> util = new ExcelUtil<MiniDemand>(MiniDemand.class);
        util.exportExcel(response, list, "需求信息数据");
    }

    /**
     * 获取需求信息详细信息
     */
    @ApiOperation("获取需求信息详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return AjaxResult.success(miniDemandService.selectMiniDemandByDemandId(demandId));
    }

    /**
     * 新增需求信息
     */
    @ApiOperation("新增需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:add')")
    @Log(title = "需求信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("需求信息") @RequestBody MiniDemand miniDemand)
    {
        return toAjax(miniDemandService.insertMiniDemand(miniDemand));
    }

    /**
     * 修改需求信息
     */
    @ApiOperation("修改需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("需求信息") @RequestBody MiniDemand miniDemand)
    {
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 删除需求信息
     */
    @ApiOperation("删除需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:remove')")
    @Log(title = "需求信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("需求ID数组") @RequestBody Long[] demandIds)
    {
        return toAjax(miniDemandService.deleteMiniDemandByDemandIds(demandIds));
    }

    /**
     * 下架需求信息
     */
    @ApiOperation("下架需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/offShelf")
    public AjaxResult offShelf(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        MiniDemand miniDemand = new MiniDemand();
        miniDemand.setDemandId(demandId);
        miniDemand.setDemandStatus("2"); // 2表示已下架
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 上架需求信息
     */
    @ApiOperation("上架需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/onShelf")
    public AjaxResult onShelf(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        MiniDemand miniDemand = new MiniDemand();
        miniDemand.setDemandId(demandId);
        miniDemand.setDemandStatus("0"); // 0表示已发布
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 根据分类ID查询需求信息列表
     */
    @ApiOperation("根据分类ID查询需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @PostMapping("/getDemandsByCategory")
    public AjaxResult getDemandsByCategory(@ApiParam("分类ID") @RequestBody Long categoryId)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandsByCategoryId(categoryId);
        return AjaxResult.success(list);
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取推荐的需求信息列表
     */
    @ApiOperation("获取推荐的需求信息列表")
    @PostMapping("/app/getRecommendedList")
    public AjaxResult getRecommendedList()
    {
        List<MiniDemand> list = miniDemandService.selectRecommendedMiniDemandList();
        return AjaxResult.success(list);
    }

//    /**
//     * 获取启用的需求信息列表
//     */
//    @ApiOperation("获取启用的需求信息列表")
//    @PostMapping("/app/getEnabledList")
//    public AjaxResult getEnabledList()
//    {
//        List<MiniDemand> list = miniDemandService.selectEnabledMiniDemandList();
//        return AjaxResult.success(list);
//    }

//    /**
//     * 根据分类获取需求信息列表
//     */
//    @ApiOperation("根据分类获取需求信息列表")
//    @PostMapping("/app/getDemandsByCategory")
//    public AjaxResult getDemandsByCategoryForApp(@ApiParam("分类ID") @RequestBody Long categoryId)
//    {
//        List<MiniDemand> list = miniDemandService.selectMiniDemandsByCategoryId(categoryId);
//        return AjaxResult.success(list);
//    }

    /**
     * 获取需求详情
     */
    @ApiOperation("获取需求详情")
    @PostMapping("/app/getDetail")
    public AjaxResult getDetail(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return AjaxResult.success(miniDemandService.selectMiniDemandByDemandId(demandId));
    }

    /**
     * 增加需求浏览次数
     */
    @ApiOperation("增加需求浏览次数")
    @PostMapping("/app/increaseViewCount")
    public AjaxResult increaseViewCount(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return toAjax(miniDemandService.increaseViewCount(demandId));
    }

    /**
     * 获取我的发布需求列表
     */
    @ApiOperation("获取我的发布需求列表")
    @PostMapping("/app/getMyPublishedDemands")
    public AjaxResult getMyPublishedDemands(@ApiParam("用户ID") @RequestBody Long userId)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandListByUserId(userId);
        return AjaxResult.success(list);
    }
}