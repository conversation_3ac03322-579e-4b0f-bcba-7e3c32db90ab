<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.HaitangTopImageMapper">
    
    <resultMap type="HaitangTopImage" id="HaitangTopImageResult">
        <result property="topImageId"    column="top_image_id"    />
        <result property="title"    column="title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHaitangTopImageVo">
        select top_image_id, title, image_url, link_url, status, create_by, create_time, update_by, update_time, remark from mini_haitang_top_image
    </sql>

    <select id="selectHaitangTopImageList" parameterType="HaitangTopImage" resultMap="HaitangTopImageResult">
        <include refid="selectHaitangTopImageVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="linkUrl != null  and linkUrl != ''"> and link_url = #{linkUrl}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectHaitangTopImageByTopImageId" parameterType="Long" resultMap="HaitangTopImageResult">
        <include refid="selectHaitangTopImageVo"/>
        where top_image_id = #{topImageId}
    </select>

    <select id="selectEnabledHaitangTopImage" resultMap="HaitangTopImageResult">
        <include refid="selectHaitangTopImageVo"/>
        where status = '0'
        order by create_time desc
        limit 1
    </select>
        
    <insert id="insertHaitangTopImage" parameterType="HaitangTopImage" useGeneratedKeys="true" keyProperty="topImageId">
        insert into mini_haitang_top_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHaitangTopImage" parameterType="HaitangTopImage">
        update mini_haitang_top_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where top_image_id = #{topImageId}
    </update>

    <delete id="deleteHaitangTopImageByTopImageId" parameterType="Long">
        delete from mini_haitang_top_image where top_image_id = #{topImageId}
    </delete>

    <delete id="deleteHaitangTopImageByTopImageIds" parameterType="String">
        delete from mini_haitang_top_image where top_image_id in 
        <foreach item="topImageId" collection="array" open="(" separator="," close=")">
            #{topImageId}
        </foreach>
    </delete>
</mapper>
