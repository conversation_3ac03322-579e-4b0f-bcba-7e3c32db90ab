package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniEvent;
import com.ruoyi.miniapp.mapper.MiniEventMapper;
import com.ruoyi.miniapp.service.IMiniEventService;

/**
 * 活动报名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniEventServiceImpl implements IMiniEventService 
{
    @Autowired
    private MiniEventMapper miniEventMapper;

    /**
     * 查询活动报名
     * 
     * @param eventId 活动报名主键
     * @return 活动报名
     */
    @Override
    public MiniEvent selectMiniEventByEventId(Long eventId)
    {
        return miniEventMapper.selectMiniEventByEventId(eventId);
    }

    /**
     * 查询活动报名列表
     * 
     * @param miniEvent 活动报名
     * @return 活动报名
     */
    @Override
    public List<MiniEvent> selectMiniEventList(MiniEvent miniEvent)
    {
        return miniEventMapper.selectMiniEventList(miniEvent);
    }

    /**
     * 新增活动报名
     * 
     * @param miniEvent 活动报名
     * @return 结果
     */
    @Override
    public int insertMiniEvent(MiniEvent miniEvent)
    {
        miniEvent.setCreateTime(DateUtils.getNowDate());
        return miniEventMapper.insertMiniEvent(miniEvent);
    }

    /**
     * 修改活动报名
     * 
     * @param miniEvent 活动报名
     * @return 结果
     */
    @Override
    public int updateMiniEvent(MiniEvent miniEvent)
    {
        miniEvent.setUpdateTime(DateUtils.getNowDate());
        return miniEventMapper.updateMiniEvent(miniEvent);
    }

    /**
     * 批量删除活动报名
     * 
     * @param eventIds 需要删除的活动报名主键
     * @return 结果
     */
    @Override
    public int deleteMiniEventByEventIds(Long[] eventIds)
    {
        return miniEventMapper.deleteMiniEventByEventIds(eventIds);
    }

    /**
     * 删除活动报名信息
     * 
     * @param eventId 活动报名主键
     * @return 结果
     */
    @Override
    public int deleteMiniEventByEventId(Long eventId)
    {
        return miniEventMapper.deleteMiniEventByEventId(eventId);
    }

    /**
     * 查询正在进行的活动列表（小程序端调用）
     * 
     * @return 活动报名集合
     */
    @Override
    public List<MiniEvent> selectActiveMiniEventList()
    {
        return miniEventMapper.selectActiveMiniEventList();
    }

    /**
     * 查询启用的活动列表（小程序端调用）
     * 
     * @return 活动报名集合
     */
    @Override
    public List<MiniEvent> selectEnabledMiniEventList()
    {
        return miniEventMapper.selectEnabledMiniEventList();
    }

    /**
     * 查询正在进行的活动列表（小程序端调用）
     *
     * @return 活动报名集合
     */
    @Override
    public List<MiniEvent> selectOngoingMiniEventList()
    {
        return miniEventMapper.selectOngoingMiniEventList();
    }

    /**
     * 小程序端搜索活动
     *
     * @param keyword 搜索关键词
     * @param status 活动状态 (all, active, ended)
     * @param eventType 活动类型 (activity, guidance)
     * @param userId 用户ID (用于"我参与的"筛选)
     * @return 活动集合
     */
    @Override
    public List<MiniEvent> searchEventsForApp(String keyword, String status, String eventType, Long userId)
    {
        return miniEventMapper.searchEventsForApp(keyword, status, eventType, userId);
    }

    /**
     * 查询用户参与的活动列表
     *
     * @param userId 用户ID
     * @return 活动集合
     */
    @Override
    public List<MiniEvent> selectEventsByUserId(Long userId)
    {
        return miniEventMapper.selectEventsByUserId(userId);
    }
}