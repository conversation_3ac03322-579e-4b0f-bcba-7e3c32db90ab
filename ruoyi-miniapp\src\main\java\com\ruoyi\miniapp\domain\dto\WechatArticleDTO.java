package com.ruoyi.miniapp.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 微信文章DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel("微信文章")
public class WechatArticleDTO
{
    /** 文章ID */
    @ApiModelProperty("文章ID")
    @JsonProperty("article_id")
    private String articleId;

    /** 文章内容 */
    @ApiModelProperty("文章内容")
    private ContentDTO content;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonProperty("update_time")
    private Long updateTime;

    public String getArticleId() {
        return articleId;
    }

    public void setArticleId(String articleId) {
        this.articleId = articleId;
    }

    public ContentDTO getContent() {
        return content;
    }

    public void setContent(ContentDTO content) {
        this.content = content;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 文章内容DTO
     */
    @ApiModel("文章内容")
    public static class ContentDTO {
        /** 图文内容列表 */
        @ApiModelProperty("图文内容列表")
        @JsonProperty("news_item")
        private List<NewsItemDTO> newsItem;

        public List<NewsItemDTO> getNewsItem() {
            return newsItem;
        }

        public void setNewsItem(List<NewsItemDTO> newsItem) {
            this.newsItem = newsItem;
        }
    }

    /**
     * 图文内容项DTO
     */
    @ApiModel("图文内容项")
    public static class NewsItemDTO {
        /** 标题 */
        @ApiModelProperty("标题")
        private String title;

        /** 作者 */
        @ApiModelProperty("作者")
        private String author;

        /** 摘要 */
        @ApiModelProperty("摘要")
        private String digest;

        /** 内容 */
        @ApiModelProperty("内容")
        private String content;

        /** 原文链接 */
        @ApiModelProperty("原文链接")
        @JsonProperty("content_source_url")
        private String contentSourceUrl;

        /** 封面图片素材ID */
        @ApiModelProperty("封面图片素材ID")
        @JsonProperty("thumb_media_id")
        private String thumbMediaId;

        /** 封面图片URL */
        @ApiModelProperty("封面图片URL")
        @JsonProperty("thumb_url")
        private String thumbUrl;

        /** 文章URL */
        @ApiModelProperty("文章URL")
        private String url;

        /** 是否删除 */
        @ApiModelProperty("是否删除")
        @JsonProperty("is_deleted")
        private Boolean isDeleted;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getAuthor() {
            return author;
        }

        public void setAuthor(String author) {
            this.author = author;
        }

        public String getDigest() {
            return digest;
        }

        public void setDigest(String digest) {
            this.digest = digest;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getContentSourceUrl() {
            return contentSourceUrl;
        }

        public void setContentSourceUrl(String contentSourceUrl) {
            this.contentSourceUrl = contentSourceUrl;
        }

        public String getThumbMediaId() {
            return thumbMediaId;
        }

        public void setThumbMediaId(String thumbMediaId) {
            this.thumbMediaId = thumbMediaId;
        }

        public String getThumbUrl() {
            return thumbUrl;
        }

        public void setThumbUrl(String thumbUrl) {
            this.thumbUrl = thumbUrl;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public Boolean getIsDeleted() {
            return isDeleted;
        }

        public void setIsDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
        }
    }
}
