package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniNewsCenter;
import com.ruoyi.miniapp.service.IMiniNewsCenterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 新闻中心Controller
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@RestController
@RequestMapping("/miniapp/haitang/news")
public class MiniNewsCenterController extends BaseController
{
    @Autowired
    private IMiniNewsCenterService miniNewsCenterService;

    /**
     * 查询新闻中心列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniNewsCenter miniNewsCenter)
    {
        startPage();
        List<MiniNewsCenter> list = miniNewsCenterService.selectMiniNewsCenterList(miniNewsCenter);
        return getDataTable(list);
    }

    /**
     * 导出新闻中心列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:export')")
    @Log(title = "新闻中心", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniNewsCenter miniNewsCenter)
    {
        List<MiniNewsCenter> list = miniNewsCenterService.selectMiniNewsCenterList(miniNewsCenter);
        ExcelUtil<MiniNewsCenter> util = new ExcelUtil<MiniNewsCenter>(MiniNewsCenter.class);
        util.exportExcel(response, list, "新闻中心数据");
    }

    /**
     * 获取新闻中心详细信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(miniNewsCenterService.selectMiniNewsCenterById(id));
    }

    /**
     * 新增新闻中心
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:add')")
    @Log(title = "新闻中心", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniNewsCenter miniNewsCenter)
    {
        return toAjax(miniNewsCenterService.insertMiniNewsCenter(miniNewsCenter));
    }

    /**
     * 修改新闻中心
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:edit')")
    @Log(title = "新闻中心", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniNewsCenter miniNewsCenter)
    {
        return toAjax(miniNewsCenterService.updateMiniNewsCenter(miniNewsCenter));
    }

    /**
     * 删除新闻中心
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:remove')")
    @Log(title = "新闻中心", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(miniNewsCenterService.deleteMiniNewsCenterByIds(ids));
    }

    /**
     * 从微信公众号同步文章
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:sync')")
    @Log(title = "新闻中心", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public AjaxResult syncFromWechat()
    {
        String result = miniNewsCenterService.syncFromWechat();
        return success(result);
    }

    /**
     * 手动同步指定数量的文章
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:sync')")
    @Log(title = "新闻中心", businessType = BusinessType.OTHER)
    @PostMapping("/sync/{count}/{offset}")
    public AjaxResult syncFromWechat(@PathVariable int count, @PathVariable int offset)
    {
        String result = miniNewsCenterService.syncFromWechat(count, offset);
        return success(result);
    }

    /**
     * 清理并重新同步文章（用于测试图片下载功能）
     */
    @PreAuthorize("@ss.hasPermi('miniapp:news:sync')")
    @Log(title = "新闻中心", businessType = BusinessType.OTHER)
    @PostMapping("/resync/{count}")
    public AjaxResult resyncFromWechat(@PathVariable int count)
    {
        // 先清理现有数据
        int deletedCount = miniNewsCenterService.deleteAllMiniNewsCenter();

        // 重新同步
        String result = miniNewsCenterService.syncFromWechat(count, 0);
        return success("清理了" + deletedCount + "条数据，" + result);
    }
} 