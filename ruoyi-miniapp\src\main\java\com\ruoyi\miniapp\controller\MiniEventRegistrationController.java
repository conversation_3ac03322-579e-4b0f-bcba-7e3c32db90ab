package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniEventRegistration;
import com.ruoyi.miniapp.service.IMiniEventRegistrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 用户报名记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "用户报名记录管理")
@RestController
@RequestMapping("/miniapp/registration")
public class MiniEventRegistrationController extends BaseController
{
    @Autowired
    private IMiniEventRegistrationService miniEventRegistrationService;

    /**
     * 查询用户报名记录列表
     */
    @ApiOperation("查询用户报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniEventRegistration miniEventRegistration)
    {
        startPage();
        List<MiniEventRegistration> list = miniEventRegistrationService.selectMiniEventRegistrationList(miniEventRegistration);
        return getDataTable(list);
    }

    /**
     * 导出用户报名记录列表
     */
    @ApiOperation("导出用户报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:export')")
    @Log(title = "用户报名记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniEventRegistration miniEventRegistration)
    {
        List<MiniEventRegistration> list = miniEventRegistrationService.selectMiniEventRegistrationList(miniEventRegistration);
        ExcelUtil<MiniEventRegistration> util = new ExcelUtil<MiniEventRegistration>(MiniEventRegistration.class);
        util.exportExcel(response, list, "用户报名记录数据");
    }

    /**
     * 获取用户报名记录详细信息
     */
    @ApiOperation("获取用户报名记录详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:query')")
    @GetMapping("/{registrationId}")
    public AjaxResult getInfo(@ApiParam("报名记录ID") @PathVariable("registrationId") Long registrationId)
    {
        return AjaxResult.success(miniEventRegistrationService.selectMiniEventRegistrationByRegistrationId(registrationId));
    }

    /**
     * 新增用户报名记录
     */
    @ApiOperation("新增用户报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:add')")
    @Log(title = "用户报名记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("报名记录信息") @RequestBody MiniEventRegistration miniEventRegistration)
    {
        return toAjax(miniEventRegistrationService.insertMiniEventRegistration(miniEventRegistration));
    }

    /**
     * 修改用户报名记录
     */
    @ApiOperation("修改用户报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:edit')")
    @Log(title = "用户报名记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("报名记录信息") @RequestBody MiniEventRegistration miniEventRegistration)
    {
        return toAjax(miniEventRegistrationService.updateMiniEventRegistration(miniEventRegistration));
    }

    /**
     * 删除用户报名记录
     */
    @ApiOperation("删除用户报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:remove')")
    @Log(title = "用户报名记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@ApiParam("报名记录ID数组") @PathVariable Long[] registrationIds)
    {
        return toAjax(miniEventRegistrationService.deleteMiniEventRegistrationByRegistrationIds(registrationIds));
    }

    /**
     * 根据用户ID查询报名记录列表
     */
    @ApiOperation("根据用户ID查询报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getUserRegistrations(@ApiParam("用户ID") @PathVariable Long userId)
    {
        List<MiniEventRegistration> list = miniEventRegistrationService.selectMiniEventRegistrationByUserId(userId);
        return AjaxResult.success(list);
    }

    /**
     * 根据活动ID查询报名记录列表
     */
    @ApiOperation("根据活动ID查询报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:list')")
    @GetMapping("/event/{eventId}")
    public AjaxResult getEventRegistrations(@ApiParam("活动ID") @PathVariable Long eventId)
    {
        List<MiniEventRegistration> list = miniEventRegistrationService.selectMiniEventRegistrationByEventId(eventId);
        return AjaxResult.success(list);
    }

    /**
     * 查询用户是否已报名某活动
     */
    @ApiOperation("查询用户是否已报名某活动")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:query')")
    @GetMapping("/check/{eventId}/{userId}")
    public AjaxResult checkUserRegistered(@ApiParam("活动ID") @PathVariable Long eventId, @ApiParam("用户ID") @PathVariable Long userId)
    {
        MiniEventRegistration registration = miniEventRegistrationService.selectRegistrationByEventAndUser(eventId, userId);
        boolean isRegistered = registration != null;
        return AjaxResult.success(isRegistered);
    }

    /**
     * 统计活动报名人数
     */
    @ApiOperation("统计活动报名人数")
    @PreAuthorize("@ss.hasPermi('miniapp:registration:query')")
    @GetMapping("/count/{eventId}")
    public AjaxResult countRegistrations(@ApiParam("活动ID") @PathVariable Long eventId)
    {
        int count = miniEventRegistrationService.countRegistrationByEventId(eventId);
        return AjaxResult.success(count);
    }

    // ================================ 小程序端接口 ================================

    /**
     * 用户报名活动
     */
    @ApiOperation("用户报名活动")
    @PostMapping("/app/register")
    public AjaxResult register(@ApiParam("报名信息") @RequestBody MiniEventRegistration miniEventRegistration)
    {
        // 先检查是否已报名
        MiniEventRegistration existingRegistration = miniEventRegistrationService.selectRegistrationByEventAndUser(
            miniEventRegistration.getEventId(),
            miniEventRegistration.getUserId()
        );
        if (existingRegistration != null) {
            return AjaxResult.error("您已经报名过该活动");
        }
        
        return toAjax(miniEventRegistrationService.insertMiniEventRegistration(miniEventRegistration));
    }

    /**
     * 获取用户报名记录
     */
    @ApiOperation("获取用户报名记录")
    @PostMapping("/app/getUserRegistrations")
    public AjaxResult getUserRegistrationsForApp(@ApiParam("用户ID") @RequestBody Long userId)
    {
        List<MiniEventRegistration> list = miniEventRegistrationService.selectMiniEventRegistrationByUserId(userId);
        return AjaxResult.success(list);
    }

    /**
     * 检查报名状态
     */
    @ApiOperation("检查报名状态")
    @PostMapping("/app/checkRegistrationStatus")
    public AjaxResult checkRegistrationStatus(@ApiParam("检查参数") @RequestBody CheckRegistrationRequest request)
    {
        MiniEventRegistration registration = miniEventRegistrationService.selectRegistrationByEventAndUser(
            request.getEventId(), 
            request.getUserId()
        );
        boolean isRegistered = registration != null;
        return AjaxResult.success(isRegistered);
    }

    /**
     * 检查报名请求参数
     */
    public static class CheckRegistrationRequest {
        private Long userId;
        private Long eventId;

        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
    }
} 