import request from '@/utils/request'

// 查询园区管理列表
export function listPark(query) {
  return request({
    url: '/miniapp/park/list',
    method: 'get',
    params: query
  })
}

// 查询园区管理详细
export function getPark(parkId) {
  return request({
    url: '/miniapp/park/' + parkId,
    method: 'get'
  })
}

// 新增园区管理
export function addPark(data) {
  return request({
    url: '/miniapp/park',
    method: 'post',
    data: data
  })
}

// 修改园区管理
export function updatePark(data) {
  return request({
    url: '/miniapp/park',
    method: 'put',
    data: data
  })
}

// 删除园区管理
export function delPark(parkId) {
  return request({
    url: '/miniapp/park/' + parkId,
    method: 'delete'
  })
}

// 导出园区管理
export function exportPark(query) {
  return request({
    url: '/miniapp/park/export',
    method: 'post',
    data: query
  })
}
