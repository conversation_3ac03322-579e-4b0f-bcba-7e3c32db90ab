package com.ruoyi.miniapp.domain;

import java.util.List;
import java.util.ArrayList;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产业树状结构对象 mini_industry_tree
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class MiniIndustryTree extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 父节点ID */
    @Excel(name = "父节点ID")
    private Long parentId;

    /** 节点编码 */
    @Excel(name = "节点编码")
    private String nodeCode;

    /** 节点名称 */
    @Excel(name = "节点名称")
    private String nodeName;

    /** 节点描述 */
    @Excel(name = "节点描述")
    private String nodeDescription;

    /** 节点类型 */
    @Excel(name = "节点类型", readConverterExp = "type=产业类型,position=产业位置,segment=产业细分")
    private String nodeType;

    /** 节点层级 */
    @Excel(name = "节点层级")
    private Integer nodeLevel;

    /** 产业链位置 */
    @Excel(name = "产业链位置", readConverterExp = "upstream=上游,midstream=中游,downstream=下游")
    private String streamType;

    /** 是否有上中下游 */
    @Excel(name = "是否有上中下游", readConverterExp = "0=否,1=是")
    private String hasStreamType;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String deleted;

    /** 节点路径 */
    @Excel(name = "节点路径")
    private String nodePath;

    /** 子节点列表 */
    private List<MiniIndustryTree> children = new ArrayList<>();

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    public void setNodeCode(String nodeCode) 
    {
        this.nodeCode = nodeCode;
    }

    public String getNodeCode() 
    {
        return nodeCode;
    }

    public void setNodeName(String nodeName) 
    {
        this.nodeName = nodeName;
    }

    public String getNodeName() 
    {
        return nodeName;
    }

    public void setNodeDescription(String nodeDescription) 
    {
        this.nodeDescription = nodeDescription;
    }

    public String getNodeDescription() 
    {
        return nodeDescription;
    }

    public void setNodeType(String nodeType) 
    {
        this.nodeType = nodeType;
    }

    public String getNodeType() 
    {
        return nodeType;
    }

    public void setNodeLevel(Integer nodeLevel) 
    {
        this.nodeLevel = nodeLevel;
    }

    public Integer getNodeLevel() 
    {
        return nodeLevel;
    }

    public void setStreamType(String streamType) 
    {
        this.streamType = streamType;
    }

    public String getStreamType()
    {
        return streamType;
    }

    public void setHasStreamType(String hasStreamType)
    {
        this.hasStreamType = hasStreamType;
    }

    public String getHasStreamType()
    {
        return hasStreamType;
    }

    public void setSortOrder(Integer sortOrder)
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setDeleted(String deleted)
    {
        this.deleted = deleted;
    }

    public String getDeleted()
    {
        return deleted;
    }

    public void setNodePath(String nodePath)
    {
        this.nodePath = nodePath;
    }

    public String getNodePath() 
    {
        return nodePath;
    }

    public List<MiniIndustryTree> getChildren() 
    {
        return children;
    }

    public void setChildren(List<MiniIndustryTree> children) 
    {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentId", getParentId())
            .append("nodeCode", getNodeCode())
            .append("nodeName", getNodeName())
            .append("nodeDescription", getNodeDescription())
            .append("nodeType", getNodeType())
            .append("nodeLevel", getNodeLevel())
            .append("streamType", getStreamType())
            .append("hasStreamType", getHasStreamType())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("deleted", getDeleted())
            .append("nodePath", getNodePath())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 