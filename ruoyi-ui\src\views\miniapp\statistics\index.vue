<template>
  <div class="app-container">
    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
            <div class="stat-trend">
              <i class="el-icon-arrow-up" style="color: #67c23a;"></i>
              <span class="trend-text">+{{ statistics.newUsersToday }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalBarrages }}</div>
            <div class="stat-label">弹幕总数</div>
            <div class="stat-trend">
              <i class="el-icon-arrow-up" style="color: #67c23a;"></i>
              <span class="trend-text">+{{ statistics.newBarragesToday }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.pendingAuditCount }}</div>
            <div class="stat-label">待审核弹幕</div>
            <div class="stat-trend">
              <el-button 
                type="primary" 
                size="mini" 
                @click="goToAudit"
                v-if="statistics.pendingAuditCount > 0"
              >
                去审核
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalActivities }}</div>
            <div class="stat-label">活动总数</div>
            <div class="stat-trend">
              <i class="el-icon-arrow-up" style="color: #67c23a;"></i>
              <span class="trend-text">+{{ statistics.newActivitiesToday }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>用户注册趋势</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeChartPeriod('week')" :type="chartPeriod === 'week' ? 'primary' : 'default'">近7天</el-button>
              <el-button size="mini" @click="changeChartPeriod('month')" :type="chartPeriod === 'month' ? 'primary' : 'default'">近30天</el-button>
            </el-button-group>
          </div>
          <div ref="userChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>弹幕审核状态</span>
          </div>
          <div ref="auditChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="8">
        <el-card>
          <div slot="header" class="clearfix">
            <span>快速操作</span>
          </div>
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-plus" @click="$router.push('/miniapp/content/banner')">
              新增轮播图
            </el-button>
            <el-button type="success" icon="el-icon-plus" @click="$router.push('/miniapp/content/notice')">
              发布通知
            </el-button>
            <el-button type="warning" icon="el-icon-check" @click="$router.push('/miniapp/content/barrage')">
              审核弹幕
            </el-button>
            <el-button type="info" icon="el-icon-plus" @click="$router.push('/miniapp/business/event')">
              创建活动
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div slot="header" class="clearfix">
            <span>用户积分排行</span>
          </div>
          <div class="ranking-list">
            <div 
              v-for="(user, index) in topUsers" 
              :key="user.userId"
              class="ranking-item"
            >
              <div class="ranking-number">{{ index + 1 }}</div>
              <el-avatar :size="30" :src="user.avatarUrl" />
              <div class="user-info">
                <div class="user-name">{{ user.nickname }}</div>
                <div class="user-points">{{ user.totalPoints }} 积分</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div slot="header" class="clearfix">
            <span>最新弹幕</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="$router.push('/miniapp/content/barrage')"
            >
              查看全部
            </el-button>
          </div>
          <div class="latest-barrages">
            <div 
              v-for="barrage in latestBarrages" 
              :key="barrage.barrageId"
              class="barrage-item"
            >
              <div class="barrage-user">
                <el-avatar :size="20" :src="barrage.userAvatarUrl" />
                <span class="user-name">{{ barrage.userNickname }}</span>
              </div>
              <div class="barrage-content">{{ barrage.content }}</div>
              <div class="barrage-status">
                <el-tag 
                  :type="getAuditStatusType(barrage.auditStatus)" 
                  size="mini"
                >
                  {{ getAuditStatusText(barrage.auditStatus) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>今日数据概览</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="refreshData"
            >
              刷新数据
            </el-button>
          </div>
          <el-table :data="todayData" stripe style="width: 100%">
            <el-table-column prop="type" label="数据类型" width="120" />
            <el-table-column prop="count" label="数量" width="100" />
            <el-table-column prop="percentage" label="相比昨日" width="120">
              <template slot-scope="scope">
                <span 
                  :class="scope.row.percentage > 0 ? 'text-success' : 'text-danger'"
                  v-if="scope.row.percentage !== 0"
                >
                  {{ scope.row.percentage > 0 ? '+' : '' }}{{ scope.row.percentage }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getStatistics, getUserRanking, getLatestBarrages, getTodayData } from "@/api/miniapp/statistics";
import * as echarts from 'echarts';

export default {
  name: "MiniappStatistics",
  data() {
    return {
      loading: false,
      chartPeriod: 'week',
      statistics: {
        totalUsers: 0,
        newUsersToday: 0,
        totalBarrages: 0,
        newBarragesToday: 0,
        pendingAuditCount: 0,
        totalActivities: 0,
        newActivitiesToday: 0
      },
      topUsers: [],
      latestBarrages: [],
      todayData: [],
      userChart: null,
      auditChart: null
    };
  },
  created() {
    this.loadData();
  },
  mounted() {
    this.initCharts();
  },
  methods: {
    /** 加载数据 */
    async loadData() {
      this.loading = true;
      try {
        const [statisticsRes, usersRes, barragesRes, todayRes] = await Promise.all([
          getStatistics(),
          getUserRanking(),
          getLatestBarrages(),
          getTodayData()
        ]);
        
        this.statistics = statisticsRes.data;
        this.topUsers = usersRes.data || [];
        this.latestBarrages = barragesRes.data || [];
        this.todayData = todayRes.data || [];
        
        this.updateCharts();
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },
    /** 初始化图表 */
    initCharts() {
      this.userChart = echarts.init(this.$refs.userChart);
      this.auditChart = echarts.init(this.$refs.auditChart);
      
      // 监听窗口变化
      window.addEventListener('resize', () => {
        this.userChart.resize();
        this.auditChart.resize();
      });
    },
    /** 更新图表 */
    updateCharts() {
      // 用户注册趋势图
      const userOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.statistics.userTrendDates || []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '新增用户',
          type: 'line',
          data: this.statistics.userTrendCounts || [],
          smooth: true,
          itemStyle: {
            color: '#409EFF'
          }
        }]
      };
      this.userChart.setOption(userOption);
      
      // 弹幕审核状态饼图
      const auditOption = {
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '弹幕审核状态',
          type: 'pie',
          radius: '60%',
          data: [
            { value: this.statistics.pendingAuditCount, name: '待审核' },
            { value: this.statistics.approvedCount, name: '已通过' },
            { value: this.statistics.rejectedCount, name: '已拒绝' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
      this.auditChart.setOption(auditOption);
    },
    /** 切换图表周期 */
    changeChartPeriod(period) {
      this.chartPeriod = period;
      this.loadData();
    },
    /** 刷新数据 */
    refreshData() {
      this.loadData();
      this.$message.success('数据刷新成功');
    },
    /** 跳转到审核页面 */
    goToAudit() {
      this.$router.push('/miniapp/content/barrage');
    },
    /** 获取审核状态类型 */
    getAuditStatusType(status) {
      const statusMap = {
        '0': 'warning',
        '1': 'success',
        '2': 'danger'
      };
      return statusMap[status] || 'info';
    },
    /** 获取审核状态文本 */
    getAuditStatusText(status) {
      const statusMap = {
        '0': '待审核',
        '1': '已通过',
        '2': '已拒绝'
      };
      return statusMap[status] || '未知';
    }
  },
  beforeDestroy() {
    if (this.userChart) {
      this.userChart.dispose();
    }
    if (this.auditChart) {
      this.auditChart.dispose();
    }
  }
};
</script>

<style scoped>
.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stat-trend {
  font-size: 12px;
  color: #999;
}

.trend-text {
  margin-left: 5px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-actions .el-button {
  width: 100%;
}

.ranking-list {
  max-height: 300px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.ranking-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
}

.user-info {
  margin-left: 10px;
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: bold;
}

.user-points {
  font-size: 12px;
  color: #666;
}

.latest-barrages {
  max-height: 300px;
  overflow-y: auto;
}

.barrage-item {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.barrage-user {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.barrage-user .user-name {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.barrage-content {
  font-size: 14px;
  margin-bottom: 5px;
}

.barrage-status {
  text-align: right;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.mb20 {
  margin-bottom: 20px;
}
</style>