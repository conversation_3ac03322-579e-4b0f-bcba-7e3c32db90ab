package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.GuidanceRegistration;

/**
 * 指导活动报名Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface GuidanceRegistrationMapper 
{
    /**
     * 查询指导活动报名
     * 
     * @param registrationId 指导活动报名主键
     * @return 指导活动报名
     */
    public GuidanceRegistration selectGuidanceRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询指导活动报名列表
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 指导活动报名集合
     */
    public List<GuidanceRegistration> selectGuidanceRegistrationList(GuidanceRegistration guidanceRegistration);

    /**
     * 新增指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    public int insertGuidanceRegistration(GuidanceRegistration guidanceRegistration);

    /**
     * 修改指导活动报名
     * 
     * @param guidanceRegistration 指导活动报名
     * @return 结果
     */
    public int updateGuidanceRegistration(GuidanceRegistration guidanceRegistration);

    /**
     * 删除指导活动报名
     * 
     * @param registrationId 指导活动报名主键
     * @return 结果
     */
    public int deleteGuidanceRegistrationByRegistrationId(Long registrationId);

    /**
     * 批量删除指导活动报名
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGuidanceRegistrationByRegistrationIds(Long[] registrationIds);
} 