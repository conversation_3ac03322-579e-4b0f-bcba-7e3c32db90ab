package com.ruoyi.miniapp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.MiniJobType;
import com.ruoyi.miniapp.mapper.MiniJobTypeMapper;
import com.ruoyi.miniapp.service.IMiniJobTypeService;

/**
 * 职位类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class MiniJobTypeServiceImpl implements IMiniJobTypeService 
{
    @Autowired
    private MiniJobTypeMapper miniJobTypeMapper;

    /**
     * 查询职位类型
     * 
     * @param jobTypeId 职位类型主键
     * @return 职位类型
     */
    @Override
    public MiniJobType selectMiniJobTypeByJobTypeId(Long jobTypeId)
    {
        return miniJobTypeMapper.selectMiniJobTypeByJobTypeId(jobTypeId);
    }

    /**
     * 查询职位类型列表
     * 
     * @param miniJobType 职位类型
     * @return 职位类型
     */
    @Override
    public List<MiniJobType> selectMiniJobTypeList(MiniJobType miniJobType)
    {
        return miniJobTypeMapper.selectMiniJobTypeList(miniJobType);
    }

    /**
     * 新增职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    @Override
    public int insertMiniJobType(MiniJobType miniJobType)
    {
        miniJobType.setCreateTime(DateUtils.getNowDate());
        return miniJobTypeMapper.insertMiniJobType(miniJobType);
    }

    /**
     * 修改职位类型
     * 
     * @param miniJobType 职位类型
     * @return 结果
     */
    @Override
    public int updateMiniJobType(MiniJobType miniJobType)
    {
        miniJobType.setUpdateTime(DateUtils.getNowDate());
        return miniJobTypeMapper.updateMiniJobType(miniJobType);
    }

    /**
     * 批量删除职位类型
     * 
     * @param jobTypeIds 需要删除的职位类型主键
     * @return 结果
     */
    @Override
    public int deleteMiniJobTypeByJobTypeIds(Long[] jobTypeIds)
    {
        return miniJobTypeMapper.deleteMiniJobTypeByJobTypeIds(jobTypeIds);
    }

    /**
     * 删除职位类型信息
     * 
     * @param jobTypeId 职位类型主键
     * @return 结果
     */
    @Override
    public int deleteMiniJobTypeByJobTypeId(Long jobTypeId)
    {
        return miniJobTypeMapper.deleteMiniJobTypeByJobTypeId(jobTypeId);
    }

    /**
     * 查询启用的职位类型列表（小程序端调用）
     * 
     * @return 职位类型集合
     */
    @Override
    public List<MiniJobType> selectEnabledMiniJobTypeList()
    {
        return miniJobTypeMapper.selectEnabledMiniJobTypeList();
    }
} 