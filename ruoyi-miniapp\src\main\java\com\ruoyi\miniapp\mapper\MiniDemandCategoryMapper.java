package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniDemandCategory;

/**
 * 需求分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniDemandCategoryMapper 
{
    /**
     * 查询需求分类
     * 
     * @param categoryId 需求分类主键
     * @return 需求分类
     */
    public MiniDemandCategory selectMiniDemandCategoryByCategoryId(Long categoryId);

    /**
     * 查询需求分类列表
     * 
     * @param miniDemandCategory 需求分类
     * @return 需求分类集合
     */
    public List<MiniDemandCategory> selectMiniDemandCategoryList(MiniDemandCategory miniDemandCategory);

    /**
     * 新增需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    public int insertMiniDemandCategory(MiniDemandCategory miniDemandCategory);

    /**
     * 修改需求分类
     * 
     * @param miniDemandCategory 需求分类
     * @return 结果
     */
    public int updateMiniDemandCategory(MiniDemandCategory miniDemandCategory);

    /**
     * 删除需求分类
     * 
     * @param categoryId 需求分类主键
     * @return 结果
     */
    public int deleteMiniDemandCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除需求分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniDemandCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 查询启用的需求分类列表（小程序端调用）
     * 
     * @return 需求分类集合
     */
    public List<MiniDemandCategory> selectEnabledMiniDemandCategoryList();
} 