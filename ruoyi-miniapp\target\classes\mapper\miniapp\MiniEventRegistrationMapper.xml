<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniEventRegistrationMapper">
    
    <resultMap type="MiniEventRegistration" id="MiniEventRegistrationResult">
        <result property="registrationId"    column="registration_id"    />
        <result property="eventId"    column="event_id"    />
        <result property="userId"    column="user_id"    />
        <result property="formData"    column="form_data"    />
        <result property="registrationTime"    column="registration_time"    />
        <result property="eventName"    column="event_name"    />
        <result property="eventType"    column="event_type"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniEventRegistrationVo">
        select
            r.registration_id,
            r.event_id,
            r.user_id,
            r.form_data,
            r.registration_time,
            r.create_by,
            r.create_time,
            r.update_by,
            r.update_time,
            r.remark,
            e.title as event_name,
            e.event_type as event_type,
            u.nick_name as user_name,
            u.phonenumber as user_phone
        from mini_event_registration r
        left join mini_event e on r.event_id = e.event_id
        left join sys_user u on r.user_id = u.user_id
    </sql>

    <select id="selectMiniEventRegistrationList" parameterType="MiniEventRegistration" resultMap="MiniEventRegistrationResult">
        <include refid="selectMiniEventRegistrationVo"/>
        <where>
            <if test="eventId != null "> and r.event_id = #{eventId}</if>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="eventName != null and eventName != ''"> and e.title like concat('%', #{eventName}, '%')</if>
            <if test="eventType != null and eventType != ''"> and e.event_type = #{eventType}</if>
            <if test="userName != null and userName != ''"> and u.nick_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null and userPhone != ''"> and u.phonenumber like concat('%', #{userPhone}, '%')</if>
            <if test="params.beginRegistrationTime != null and params.beginRegistrationTime != ''"><!-- 开始报名时间 -->
                and date_format(r.registration_time,'%y%m%d') &gt;= date_format(#{params.beginRegistrationTime},'%y%m%d')
            </if>
            <if test="params.endRegistrationTime != null and params.endRegistrationTime != ''"><!-- 结束报名时间 -->
                and date_format(r.registration_time,'%y%m%d') &lt;= date_format(#{params.endRegistrationTime},'%y%m%d')
            </if>
        </where>
        order by r.registration_time desc
    </select>
    
    <select id="selectMiniEventRegistrationByRegistrationId" parameterType="Long" resultMap="MiniEventRegistrationResult">
        <include refid="selectMiniEventRegistrationVo"/>
        where r.registration_id = #{registrationId}
    </select>
    
    <select id="selectMiniEventRegistrationByEventId" parameterType="Long" resultMap="MiniEventRegistrationResult">
        <include refid="selectMiniEventRegistrationVo"/>
        where r.event_id = #{eventId}
        order by r.registration_time desc
    </select>
    
    <select id="selectMiniEventRegistrationByUserId" parameterType="Long" resultMap="MiniEventRegistrationResult">
        <include refid="selectMiniEventRegistrationVo"/>
        where r.user_id = #{userId}
        order by r.registration_time desc
    </select>
    
    <select id="selectRegistrationByEventAndUser" resultMap="MiniEventRegistrationResult">
        <include refid="selectMiniEventRegistrationVo"/>
        where r.event_id = #{eventId} and r.user_id = #{userId}
    </select>
    
    <select id="countRegistrationByEventId" parameterType="Long" resultType="int">
        select count(*) from mini_event_registration where event_id = #{eventId}
    </select>
        
    <insert id="insertMiniEventRegistration" parameterType="MiniEventRegistration" useGeneratedKeys="true" keyProperty="registrationId">
        insert into mini_event_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="formData != null and formData != ''">form_data,</if>
            <if test="registrationTime != null">registration_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="formData != null and formData != ''">#{formData},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniEventRegistration" parameterType="MiniEventRegistration">
        update mini_event_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="formData != null and formData != ''">form_data = #{formData},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where registration_id = #{registrationId}
    </update>

    <delete id="deleteMiniEventRegistrationByRegistrationId" parameterType="Long">
        delete from mini_event_registration where registration_id = #{registrationId}
    </delete>

    <delete id="deleteMiniEventRegistrationByRegistrationIds" parameterType="String">
        delete from mini_event_registration where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>

</mapper> 