package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 大赛介绍对象 mini_competition_config
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CompetitionConfig
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 顶部图片URL */
    @Excel(name = "顶部图片URL")
    private String topImageUrl;

    /** 大赛描述内容 */
    @Excel(name = "大赛描述内容")
    private String descriptionContent;

    /** 赛程安排内容 */
    @Excel(name = "赛程安排内容")
    private String scheduleContent;

    /** 报名条件 */
    @Excel(name = "报名条件")
    private String registrationConditions;

    /** 常见问题内容 */
    @Excel(name = "常见问题内容")
    private String faqContent;

    /** 商务合作内容 */
    @Excel(name = "商务合作内容")
    private String businessCooperation;

    /** 赞助商图片URL */
    @Excel(name = "赞助商图片URL")
    private String sponsorImageUrl;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系微信 */
    @Excel(name = "联系微信")
    private String contactWechat;

    /** 状态（1启用 0禁用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=禁用")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTopImageUrl(String topImageUrl) 
    {
        this.topImageUrl = topImageUrl;
    }

    public String getTopImageUrl() 
    {
        return topImageUrl;
    }
    public void setDescriptionContent(String descriptionContent) 
    {
        this.descriptionContent = descriptionContent;
    }

    public String getDescriptionContent() 
    {
        return descriptionContent;
    }
    public void setScheduleContent(String scheduleContent) 
    {
        this.scheduleContent = scheduleContent;
    }

    public String getScheduleContent() 
    {
        return scheduleContent;
    }
    public void setRegistrationConditions(String registrationConditions) 
    {
        this.registrationConditions = registrationConditions;
    }

    public String getRegistrationConditions() 
    {
        return registrationConditions;
    }
    public void setFaqContent(String faqContent) 
    {
        this.faqContent = faqContent;
    }

    public String getFaqContent() 
    {
        return faqContent;
    }
    public void setBusinessCooperation(String businessCooperation) 
    {
        this.businessCooperation = businessCooperation;
    }

    public String getBusinessCooperation() 
    {
        return businessCooperation;
    }
    public void setSponsorImageUrl(String sponsorImageUrl) 
    {
        this.sponsorImageUrl = sponsorImageUrl;
    }

    public String getSponsorImageUrl() 
    {
        return sponsorImageUrl;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactWechat(String contactWechat) 
    {
        this.contactWechat = contactWechat;
    }

    public String getContactWechat() 
    {
        return contactWechat;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("topImageUrl", getTopImageUrl())
            .append("descriptionContent", getDescriptionContent())
            .append("scheduleContent", getScheduleContent())
            .append("registrationConditions", getRegistrationConditions())
            .append("faqContent", getFaqContent())
            .append("businessCooperation", getBusinessCooperation())
            .append("sponsorImageUrl", getSponsorImageUrl())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("contactWechat", getContactWechat())
            .append("status", getStatus())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}