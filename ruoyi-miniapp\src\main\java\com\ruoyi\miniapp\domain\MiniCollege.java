package com.ruoyi.miniapp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 学院信息对象 mini_college
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class MiniCollege extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 学院ID */
    private Long collegeId;

    /** 学院名称 */
    @Excel(name = "学院名称")
    private String collegeName;

    /** 学院代码 */
    @Excel(name = "学院代码")
    private String collegeCode;

    /** 学院描述 */
    @Excel(name = "学院描述")
    private String collegeDesc;

    /** 院长姓名 */
    @Excel(name = "院长姓名")
    private String deanName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 学院地址 */
    @Excel(name = "学院地址")
    private String address;

    /** 学院官网 */
    @Excel(name = "学院官网")
    private String websiteUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setCollegeId(Long collegeId) 
    {
        this.collegeId = collegeId;
    }

    public Long getCollegeId() 
    {
        return collegeId;
    }
    
    public void setCollegeName(String collegeName) 
    {
        this.collegeName = collegeName;
    }

    public String getCollegeName() 
    {
        return collegeName;
    }
    
    public void setCollegeCode(String collegeCode) 
    {
        this.collegeCode = collegeCode;
    }

    public String getCollegeCode() 
    {
        return collegeCode;
    }
    
    public void setCollegeDesc(String collegeDesc) 
    {
        this.collegeDesc = collegeDesc;
    }

    public String getCollegeDesc() 
    {
        return collegeDesc;
    }
    
    public void setDeanName(String deanName) 
    {
        this.deanName = deanName;
    }

    public String getDeanName() 
    {
        return deanName;
    }
    
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    
    public void setWebsiteUrl(String websiteUrl) 
    {
        this.websiteUrl = websiteUrl;
    }

    public String getWebsiteUrl() 
    {
        return websiteUrl;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("collegeId", getCollegeId())
            .append("collegeName", getCollegeName())
            .append("collegeCode", getCollegeCode())
            .append("collegeDesc", getCollegeDesc())
            .append("deanName", getDeanName())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("address", getAddress())
            .append("websiteUrl", getWebsiteUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
